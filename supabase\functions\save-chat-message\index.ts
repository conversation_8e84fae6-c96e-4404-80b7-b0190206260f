import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { method } = req

    if (method === 'POST') {
      // Save chat message
      const body = await req.json()
      const { 
        session_id, 
        message_id, 
        sender_identity, 
        sender_name, 
        content, 
        message_type = 'chat',
        timestamp,
        metadata = {}
      } = body

      // Validate required fields
      if (!session_id || !message_id || !sender_identity || !content) {
        return new Response(
          JSON.stringify({ 
            error: 'Missing required fields: session_id, message_id, sender_identity, content' 
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // Check if session exists
      const { data: sessionExists, error: sessionError } = await supabaseClient
        .from('rep_room_sessions')
        .select('session_id')
        .eq('session_id', session_id)
        .single()

      if (sessionError || !sessionExists) {
        return new Response(
          JSON.stringify({ 
            error: 'Session not found',
            details: sessionError?.message 
          }),
          { 
            status: 404, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // Insert chat message
      const { data, error } = await supabaseClient
        .from('rep_room_chat_messages')
        .insert({
          session_id,
          message_id,
          sender_identity,
          sender_name,
          content,
          message_type,
          timestamp: timestamp ? new Date(timestamp).toISOString() : new Date().toISOString(),
          metadata
        })
        .select()
        .single()

      if (error) {
        console.error('Error saving chat message:', error)
        return new Response(
          JSON.stringify({ 
            error: 'Failed to save chat message',
            details: error.message 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Chat message saved successfully',
          data 
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )

    } else if (method === 'GET') {
      // Get chat messages for a session
      const url = new URL(req.url)
      const session_id = url.searchParams.get('session_id')
      const limit = parseInt(url.searchParams.get('limit') || '50')
      const offset = parseInt(url.searchParams.get('offset') || '0')
      const message_type = url.searchParams.get('message_type')

      if (!session_id) {
        return new Response(
          JSON.stringify({ error: 'session_id parameter is required' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // Build query
      let query = supabaseClient
        .from('rep_room_chat_messages')
        .select('*')
        .eq('session_id', session_id)
        .order('timestamp', { ascending: true })
        .range(offset, offset + limit - 1)

      // Filter by message type if specified
      if (message_type) {
        query = query.eq('message_type', message_type)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching chat messages:', error)
        return new Response(
          JSON.stringify({ 
            error: 'Failed to fetch chat messages',
            details: error.message 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      return new Response(
        JSON.stringify({ 
          success: true, 
          data,
          pagination: {
            limit,
            offset,
            count: data.length
          }
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )

    } else {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})