#!/usr/bin/env node

/**
 * Voice Inactivity Timeout Verification Script
 * 
 * This script verifies that the voice inactivity timeout implementation
 * is working correctly by checking:
 * 1. Configuration files are updated
 * 2. Required files exist
 * 3. Code integration is complete
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '../..');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath, description) {
  const fullPath = path.join(projectRoot, filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    log(`✅ ${description}: ${filePath}`, 'green');
    return true;
  } else {
    log(`❌ ${description}: ${filePath}`, 'red');
    return false;
  }
}

function checkFileContains(filePath, searchText, description) {
  const fullPath = path.join(projectRoot, filePath);
  
  if (!fs.existsSync(fullPath)) {
    log(`❌ ${description}: File not found - ${filePath}`, 'red');
    return false;
  }
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    const contains = content.includes(searchText);
    
    if (contains) {
      log(`✅ ${description}: Found in ${filePath}`, 'green');
      return true;
    } else {
      log(`❌ ${description}: Not found in ${filePath}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ ${description}: Error reading ${filePath} - ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log('🔍 Voice Inactivity Timeout Implementation Verification', 'bold');
  log('='.repeat(60), 'blue');
  
  let allChecks = true;
  
  // Check 1: Configuration file update
  log('\n📋 Checking Configuration Updates...', 'blue');
  allChecks &= checkFileContains(
    'api/voice-processing-agent/.env',
    'VOICE_IDLE_TIMEOUT_MINUTES=1.5',
    'Backend timeout configuration (90 seconds)'
  );
  
  // Check 2: Core service files
  log('\n🔧 Checking Core Service Files...', 'blue');
  allChecks &= checkFileExists(
    'src/services/voiceInactivityManager.ts',
    'Inactivity manager service'
  );
  
  // Check 3: Context integration
  log('\n🔗 Checking Context Integration...', 'blue');
  allChecks &= checkFileContains(
    'src/contexts/rroom/UnifiedVoiceContext.tsx',
    'VoiceInactivityManager',
    'Inactivity manager import in voice context'
  );
  
  allChecks &= checkFileContains(
    'src/contexts/rroom/UnifiedVoiceContext.tsx',
    'inactivityTimeRemaining',
    'Inactivity state in voice context'
  );
  
  allChecks &= checkFileContains(
    'src/contexts/rroom/UnifiedVoiceContext.tsx',
    'recordSpeechActivity',
    'Speech activity tracking'
  );
  
  allChecks &= checkFileContains(
    'src/contexts/rroom/UnifiedVoiceContext.tsx',
    'recordInteractionActivity',
    'Interaction activity tracking'
  );
  
  // Check 4: UI Components
  log('\n🎨 Checking UI Components...', 'blue');
  allChecks &= checkFileExists(
    'src/components/voice/InactivityWarning.tsx',
    'Inactivity warning component'
  );
  
  allChecks &= checkFileExists(
    'src/components/voice/VoiceSessionInfo.tsx',
    'Voice session info component'
  );
  
  // Check 5: Documentation
  log('\n📚 Checking Documentation...', 'blue');
  allChecks &= checkFileExists(
    'docs/fixes/voice-inactivity-timeout-implementation.md',
    'Implementation documentation'
  );
  
  // Check 6: Key implementation details
  log('\n🔍 Checking Implementation Details...', 'blue');
  allChecks &= checkFileContains(
    'src/services/voiceInactivityManager.ts',
    'timeoutSeconds: 90',
    'Default 90-second timeout configuration'
  );
  
  allChecks &= checkFileContains(
    'src/services/voiceInactivityManager.ts',
    'warningSeconds: 75',
    'Warning threshold at 75 seconds'
  );
  
  allChecks &= checkFileContains(
    'src/contexts/rroom/UnifiedVoiceContext.tsx',
    'onTimeout: async () =>',
    'Timeout handler implementation'
  );
  
  allChecks &= checkFileContains(
    'src/contexts/rroom/UnifiedVoiceContext.tsx',
    'onWarning: () =>',
    'Warning handler implementation'
  );
  
  // Final result
  log('\n' + '='.repeat(60), 'blue');
  if (allChecks) {
    log('🎉 All checks passed! Voice inactivity timeout implementation is complete.', 'green');
    log('\n📋 Next Steps:', 'blue');
    log('1. Test the implementation in a development environment', 'yellow');
    log('2. Verify 90-second timeout behavior', 'yellow');
    log('3. Test warning notifications at 75 seconds', 'yellow');
    log('4. Confirm proper cleanup and reconnection', 'yellow');
    log('5. Monitor logs for proper activity tracking', 'yellow');
    
    process.exit(0);
  } else {
    log('❌ Some checks failed. Please review the implementation.', 'red');
    process.exit(1);
  }
}

// Run the verification
main();