import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { CopilotKit } from '@copilotkit/react-core';
import { RepRoomInterfaceEnhanced, createEnhancedDemoRepRoomState } from '../components/rep-room/RepRoomInterfaceEnhanced';
import { LandingViewV3 } from '../components/rep-room/LandingViewV3';
import { UnifiedVoiceProvider } from '../contexts/rroom/UnifiedVoiceContext';
import { RepRoomState } from '../types/rep-room';

interface RepRoomPageV3Props {
  className?: string;
}

/**
 * Generate a unique session ID for Rep Room V3
 */
const generateSessionId = (): string => {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  return `session-${timestamp}-${randomSuffix}`;
};

/**
 * Rep Room V3 - Enhanced Multi-Agent Collaborative Interface
 *
 * Features:
 * - Three-panel layout (20% conversation, 50% presentation, 30% participants)
 * - LiveKit voice integration with immediate feedback states
 * - CopilotKit AI assistant integration
 * - Multi-agent collaboration with specialist agents
 * - Real-time presentation updates
 * - Enhanced voice feedback (yellow connecting → green active)
 * - Session ID generation and redirect functionality
 *
 * Based on Enhanced Rep Room Technical Specification
 */
export const RepRoomPageV3: React.FC<RepRoomPageV3Props> = ({
  className = ''
}) => {
  const { slug, sessionId } = useParams<{ slug: string; sessionId?: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [repRoomConfig, setRepRoomConfig] = useState<{
    repRoom: {
      id: string;
      slug: string;
      title: string;
      settings: {
        voice: {
          enabled: boolean;
          provider: string;
          sttProvider?: string;
          ttsProvider?: string;
          voiceId?: string;
        };
        appearance: {
          themeColor: string;
          backgroundColor?: string;
          backgroundType?: string;
        };
        behavior: {
          greeting_message: string;
          voice_input_enabled: boolean;
          file_upload_enabled: boolean;
        };
      };
    };
    agent: {
      id: string;
      name: string;
      mastraApiBaseUrl?: string;
      mastraAgentId?: string;
      cloneId: string;
      cloneName?: string;
    };
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [repRoomState, setRepRoomState] = useState<RepRoomState | null>(null);
  const [showLanding, setShowLanding] = useState(true);

  // Extract configuration from URL parameters
  const agentName = searchParams.get('agent') || 'AI Assistant';
  const theme = searchParams.get('theme') || 'system';
  const voiceEnabled = searchParams.get('voice') !== 'false';
  const demoMode = searchParams.get('demo') === 'true';

  // Handle session ID and landing page logic
  useEffect(() => {
    if (slug && !sessionId) {
      // Show landing page when no session ID is provided
      console.log('[RepRoomV3] No session ID provided, showing landing page');
      setShowLanding(true);
    } else if (slug && sessionId) {
      // Session ID is present, proceed normally
      console.log('[RepRoomV3] Session ID present:', sessionId);
      setShowLanding(false);
    }
  }, [slug, sessionId]);

  // Load rep room configuration
  useEffect(() => {
    // Don't load configuration if showing landing page
    if (showLanding) {
      setLoading(false);
      return;
    }
    const loadRepRoomConfig = async () => {
      try {
        setLoading(true);
        setError(null);

        if (demoMode || !slug) {
          // Demo mode - use enhanced demo data
          console.log('[RepRoomV3] Running in demo mode');
          const demoState = createEnhancedDemoRepRoomState(agentName);
          setRepRoomState(demoState as RepRoomState);
          setRepRoomConfig({
            repRoom: {
              id: 'demo-v3',
              slug: slug || 'demo-v3',
              title: 'Rep Room V3 Demo',
              settings: {
                voice: {
                  enabled: voiceEnabled,
                  provider: 'livekit',
                  sttProvider: 'deepgram',
                  ttsProvider: 'elevenlabs',
                  voiceId: 'demo-voice'
                },
                appearance: {
                  themeColor: '#3b82f6',
                  backgroundColor: '#f8fafc',
                  backgroundType: 'solid'
                },
                behavior: {
                  greeting_message: `Welcome to Rep Room V3! I'm ${agentName}, your enhanced AI assistant.`,
                  voice_input_enabled: voiceEnabled,
                  file_upload_enabled: false
                }
              }
            },
            agent: {
              id: 'demo-agent-v3',
              name: agentName,
              mastraApiBaseUrl: 'https://demo.mastra.cloud',
              mastraAgentId: 'demo-agent',
              cloneId: 'demo-clone-v3',
              cloneName: `${agentName} (V3 Clone)`
            }
          });
          setLoading(false);
          return;
        }

        // Production mode - load from API
        console.log('[RepRoomV3] Loading configuration for slug:', slug);
        const response = await fetch(`/api/public-rep-room-config?slug=${slug}`);
        
        if (!response.ok) {
          throw new Error(`Failed to load rep room config: ${response.status} ${response.statusText}`);
        }

        const config = await response.json();
        console.log('[RepRoomV3] Loaded configuration:', config);
        
        setRepRoomConfig(config);
        
        // Initialize enhanced state with loaded config
        const enhancedState = createEnhancedDemoRepRoomState(config.agent?.name || agentName);
        enhancedState.sessionInfo = {
          title: config.repRoom?.title || 'Rep Room V3 Session',
          sessionId: sessionId || generateSessionId(),
          slug: slug
        };
        
        setRepRoomState(enhancedState as RepRoomState);
        setLoading(false);
        
      } catch (err) {
        console.error('[RepRoomV3] Failed to load configuration:', err);
        setError(err instanceof Error ? err.message : 'Failed to load rep room configuration');
        
        // Fallback to demo mode on error
        console.log('[RepRoomV3] Falling back to demo mode due to error');
        const demoState = createEnhancedDemoRepRoomState(agentName);
        demoState.sessionInfo = {
          title: 'Rep Room V3 (Fallback)',
          sessionId: sessionId || generateSessionId(),
          slug: slug || 'fallback-v3'
        };
        setRepRoomState(demoState as RepRoomState);
        setRepRoomConfig({
          repRoom: {
            id: 'fallback-v3',
            slug: slug || 'fallback-v3',
            title: 'Rep Room V3 (Fallback)',
            settings: {
              voice: { enabled: voiceEnabled, provider: 'livekit' },
              appearance: { themeColor: '#ef4444' },
              behavior: {
                greeting_message: 'Running in fallback mode',
                voice_input_enabled: voiceEnabled,
                file_upload_enabled: false
              }
            }
          },
          agent: {
            id: 'fallback-agent',
            name: agentName,
            cloneId: 'fallback-clone'
          }
        });
        setLoading(false);
      }
    };

    loadRepRoomConfig();
  }, [slug, sessionId, agentName, voiceEnabled, demoMode, showLanding]);

  // Handle state changes
  const handleStateChange = (newState: RepRoomState) => {
    console.log('[RepRoomV3] State updated:', newState);
    setRepRoomState(newState);
  };

  // Handle landing page start action
  const handleLandingStart = () => {
    if (slug) {
      // Generate a new session ID and navigate to the session
      const newSessionId = generateSessionId();
      const currentSearchParams = searchParams.toString();
      const redirectPath = `/rroom-v3/${slug}/${newSessionId}${currentSearchParams ? `?${currentSearchParams}` : ''}`;
      
      console.log('[RepRoomV3] Starting session, navigating to:', redirectPath);
      navigate(redirectPath, { replace: true });
    }
  };

  // Handle demo mode from landing page
  const handleLandingDemo = () => {
    if (slug) {
      // Add demo=true to search params and generate session
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('demo', 'true');
      const newSessionId = generateSessionId();
      const redirectPath = `/rroom-v3/${slug}/${newSessionId}?${newSearchParams.toString()}`;
      
      console.log('[RepRoomV3] Starting demo mode, navigating to:', redirectPath);
      navigate(redirectPath, { replace: true });
    }
  };

  // Show landing page when no session ID is present
  if (showLanding) {
    // Prepare landing page configuration
    const landingConfig = repRoomConfig ? {
      title: repRoomConfig.agent.name,
      description: repRoomConfig.repRoom.settings.behavior.greeting_message,
      logoUrl: undefined, // logoUrl not available in current config structure
      primaryColor: repRoomConfig.repRoom.settings.appearance.themeColor || '#4F46E5'
    } : {
      title: agentName,
      description: `Welcome to Rep Room V3! Experience our enhanced multi-agent collaborative interface with ${agentName}.`,
      logoUrl: undefined,
      primaryColor: '#4F46E5'
    };

    return (
      <LandingViewV3
        onStart={handleLandingStart}
        config={landingConfig}
        loading={loading}
        error={error}
        canTryDemo={true}
        onTryDemo={handleLandingDemo}
      />
    );
  }

  // Loading state
  if (loading) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Rep Room V3</h2>
          <p className="text-gray-600">Initializing enhanced multi-agent interface...</p>
        </div>
      </div>
    );
  }

  // Error state with fallback
  if (error && !repRoomConfig) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Rep Room</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Get voice configuration
  const voiceConfig = {
    enabled: repRoomConfig?.repRoom?.settings?.voice?.enabled ?? voiceEnabled,
    provider: repRoomConfig?.repRoom?.settings?.voice?.provider || 'livekit',
    sttProvider: repRoomConfig?.repRoom?.settings?.voice?.sttProvider || 'deepgram',
    ttsProvider: repRoomConfig?.repRoom?.settings?.voice?.ttsProvider || 'elevenlabs',
    voiceId: repRoomConfig?.repRoom?.settings?.voice?.voiceId || 'default'
  };

  // Get CopilotKit runtime URL
  const copilotKitRuntimeUrl = repRoomConfig?.agent?.mastraApiBaseUrl 
    ? `${repRoomConfig.agent.mastraApiBaseUrl}/copilotkit`
    : '/api/copilotkit';

  return (
    <div className={`rep-room-v3 ${className}`}>
      {/* Error banner if in fallback mode */}
      {error && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Running in fallback mode. Some features may be limited. Error: {error}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Voice Provider Wrapper */}
      <UnifiedVoiceProvider voiceConfig={voiceConfig}>
        {/* CopilotKit Provider */}
        <CopilotKit runtimeUrl={copilotKitRuntimeUrl}>
          {/* Enhanced Rep Room Interface */}
          <RepRoomInterfaceEnhanced
            initialState={repRoomState || undefined}
            onStateChange={handleStateChange}
            agentName={repRoomConfig?.agent?.name || agentName}
            sessionId={sessionId || generateSessionId()}
            copilotKitRuntimeUrl={copilotKitRuntimeUrl}
            className="rep-room-v3-interface"
          />
        </CopilotKit>
      </UnifiedVoiceProvider>

      {/* Development Info (only in demo mode) */}
      {demoMode && (
        <div className="fixed bottom-4 right-4 bg-blue-900 text-white p-3 rounded-lg shadow-lg text-xs max-w-xs">
          <div className="font-semibold mb-1">Rep Room V3 Demo</div>
          <div>Slug: {slug || 'demo'}</div>
          <div>Session: {sessionId || 'auto-generated'}</div>
          <div>Voice: {voiceConfig.enabled ? 'Enabled' : 'Disabled'}</div>
          <div>Agent: {repRoomConfig?.agent?.name || agentName}</div>
        </div>
      )}
    </div>
  );
};

export default RepRoomPageV3;