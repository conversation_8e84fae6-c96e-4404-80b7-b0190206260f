# Voice Inactivity Timeout Implementation

## Overview

This document describes the implementation of a 90-second inactivity timeout feature for the voice processing system. The feature automatically disconnects users after 90 seconds of inactivity to reduce costs while allowing easy reconnection.

## Implementation Summary

### 1. Configuration Update
- **File**: `api/voice-processing-agent/.env`
- **Change**: Updated `VOICE_IDLE_TIMEOUT_MINUTES` from 10 minutes (600 seconds) to 1.5 minutes (90 seconds)

### 2. Inactivity Manager Service
- **File**: `src/services/voiceInactivityManager.ts`
- **Purpose**: Core service that tracks user activity and manages timeout logic
- **Features**:
  - Tracks speech activity and user interactions
  - Configurable timeout and warning thresholds
  - Automatic cleanup and session management
  - Event-driven callbacks for warnings and timeouts

### 3. Voice Context Integration
- **File**: `src/contexts/rroom/UnifiedVoiceContext.tsx`
- **Changes**:
  - Added inactivity manager initialization on connection
  - Integrated activity tracking with VAD (Voice Activity Detection)
  - Added interaction tracking for user actions (connect, disconnect, enable/disable audio)
  - Added cleanup logic for proper resource management
  - Extended state interface with inactivity information

### 4. UI Components
- **Files**: 
  - `src/components/voice/InactivityWarning.tsx`
  - `src/components/voice/VoiceSessionInfo.tsx`
- **Purpose**: Provide visual feedback about session status and inactivity warnings

## Technical Details

### Activity Tracking

The system tracks two types of user activity:

1. **Speech Activity**: Detected via VAD (Voice Activity Detection)
   - Automatically recorded when user starts speaking
   - Resets the inactivity timer

2. **Interaction Activity**: Manual user actions
   - Connect/disconnect operations
   - Enable/disable audio operations
   - Send interruption signals

### Timeout Configuration

```typescript
{
  timeoutSeconds: 90,     // Total timeout duration
  warningSeconds: 75,     // Warning threshold (15 seconds before timeout)
  checkIntervalMs: 1000   // Check frequency for UI updates
}
```

### Event Flow

1. **Connection**: Inactivity manager starts when voice connection is established
2. **Activity Detection**: Speech or interaction resets the timer
3. **Warning Phase**: At 75 seconds, warning is triggered
4. **Timeout Phase**: At 90 seconds, automatic disconnection occurs
5. **Cleanup**: All resources are properly cleaned up

### State Management

The voice context now includes inactivity-related state:

```typescript
interface UnifiedVoiceState {
  // ... existing properties
  inactivityTimeRemaining: number;    // Seconds until timeout
  inactivityWarningActive: boolean;   // Warning state
  sessionDuration: number;            // Total session time
}
```

## Usage

### Basic Integration

The inactivity timeout is automatically active when using the `UnifiedVoiceProvider`:

```tsx
import { UnifiedVoiceProvider } from './contexts/rroom/UnifiedVoiceContext';

function App() {
  return (
    <UnifiedVoiceProvider voiceConfig={config} sessionId={sessionId}>
      {/* Your app content */}
    </UnifiedVoiceProvider>
  );
}
```

### UI Components

Display inactivity warnings and session info:

```tsx
import { InactivityWarning } from './components/voice/InactivityWarning';
import { VoiceSessionInfo } from './components/voice/VoiceSessionInfo';

function VoiceInterface() {
  return (
    <div>
      <InactivityWarning />
      <VoiceSessionInfo showInactivityTimer={true} />
      {/* Other voice UI components */}
    </div>
  );
}
```

### Event Handling

Listen for inactivity events:

```tsx
import { useUnifiedVoice } from './contexts/rroom/UnifiedVoiceContext';

function MyComponent() {
  const { controls } = useUnifiedVoice();
  
  useEffect(() => {
    const handleWarning = (data) => {
      console.log('Inactivity warning:', data);
      // Show custom warning UI
    };
    
    const handleTimeout = (data) => {
      console.log('Inactivity timeout:', data);
      // Handle disconnection
    };
    
    controls.on('inactivity_warning', handleWarning);
    controls.on('inactivity_timeout', handleTimeout);
    
    return () => {
      controls.off('inactivity_warning', handleWarning);
      controls.off('inactivity_timeout', handleTimeout);
    };
  }, [controls]);
}
```

## Benefits

1. **Cost Reduction**: Automatically disconnects inactive sessions to reduce Deepgram and LiveKit usage costs
2. **User Experience**: 15-second warning period allows users to stay connected if needed
3. **Easy Reconnection**: Users can reconnect by refreshing or clicking the connection button
4. **Resource Management**: Proper cleanup prevents memory leaks and resource accumulation
5. **Configurable**: Timeout values can be easily adjusted via configuration

## Monitoring and Debugging

The implementation includes comprehensive logging:

- Activity detection events
- Warning and timeout triggers
- Cleanup operations
- Error handling

All logs are prefixed with `[UnifiedVoice]` or `[VoiceInactivityManager]` for easy filtering.

## Future Enhancements

Potential improvements for future iterations:

1. **User Preferences**: Allow users to configure their own timeout preferences
2. **Activity Types**: Distinguish between different types of activity (speech vs. interaction)
3. **Grace Period**: Add a brief grace period after timeout before full disconnection
4. **Analytics**: Track inactivity patterns for optimization
5. **Smart Timeout**: Adjust timeout based on conversation context or user behavior

## Testing

To test the inactivity timeout:

1. Connect to a voice session
2. Remain inactive (no speech or interaction) for 75 seconds
3. Observe the warning notification
4. Continue inactivity for 15 more seconds (90 total)
5. Verify automatic disconnection occurs
6. Test reconnection functionality

## Troubleshooting

Common issues and solutions:

1. **Timeout not triggering**: Check that VAD is working and activity is being detected
2. **Memory leaks**: Ensure proper cleanup in disconnect and unmount handlers
3. **UI not updating**: Verify state updates are properly propagated
4. **Reconnection issues**: Check that all resources are properly cleaned up before reconnection

## Configuration Files

Key configuration files modified:

- `api/voice-processing-agent/.env`: Backend timeout configuration
- `src/contexts/rroom/UnifiedVoiceContext.tsx`: Frontend integration
- `src/services/voiceInactivityManager.ts`: Core timeout logic

This implementation provides a robust, user-friendly inactivity timeout system that balances cost optimization with user experience.