import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { nanoid } from 'nanoid';
import { CopilotKit } from '@copilotkit/react-core';
import { RepRoomInterfaceEnhanced, createEnhancedDemoRepRoomState } from '../components/rep-room/RepRoomInterfaceEnhanced';
import { LandingViewV3 } from '../components/rep-room/LandingViewV3';
import { UnifiedVoiceProvider } from '../contexts/rroom/UnifiedVoiceContext';
import { RepRoomState } from '../types/rep-room';
import { useSupabase } from '../contexts/supabase-context';
import { ErrorBoundary } from '../components/common/ErrorBoundary';
import { ConnectionStatusIndicator } from '../components/common/ConnectionStatusIndicator';
import { useConnectionState } from '../hooks/useConnectionState';
import { useSessionValidation } from '../hooks/useSessionValidation';
import { fetchWithRetry, retryDatabaseOperation } from '../utils/retryMechanism';

interface RepRoomSessionPageProps {
  className?: string;
}

interface RepRoomConfig {
  repRoom: {
    id: string;
    slug: string;
    title: string;
    settings: {
      voice: {
        enabled: boolean;
        provider: string;
        sttProvider?: string;
        ttsProvider?: string;
        voiceId?: string;
      };
      appearance: {
        themeColor: string;
        backgroundColor?: string;
        backgroundType?: string;
      };
      behavior: {
        greeting_message: string;
        voice_input_enabled: boolean;
        file_upload_enabled: boolean;
      };
    };
  };
  agent: {
    id: string;
    name: string;
    mastraApiBaseUrl?: string;
    mastraAgentId?: string;
    cloneId: string;
    cloneName?: string;
  };
}

/**
 * Generate a unique 12-character session ID using nanoid
 * Format: 12 characters, URL-safe
 */
const generateSessionId = (): string => {
  return nanoid(12);
};

/**
 * Validate session ID format
 * Should be 12 characters, alphanumeric + URL-safe characters
 */
const isValidSessionId = (sessionId: string): boolean => {
  return /^[A-Za-z0-9_-]{12}$/.test(sessionId);
};

/**
 * Rep Room Session Page - Enhanced with persistent session management
 * 
 * Features:
 * - URL pattern: /rroom-v3/{slug}/{sessionId}
 * - Auto-generates sessionId if not provided and redirects
 * - Session ID validation and error handling
 * - Persistent LiveKit sessions
 * - Session context passed to all child components
 * - Backward compatibility with existing routes
 * 
 * Based on REP-106 Implementation Plan Phase 1
 */
export const RepRoomSessionPage: React.FC<RepRoomSessionPageProps> = ({
  className = ''
}) => {
  const { slug, sessionId } = useParams<{ slug: string; sessionId?: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [repRoomConfig, setRepRoomConfig] = useState<RepRoomConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [repRoomState, setRepRoomState] = useState<RepRoomState | null>(null);
  const [showLanding, setShowLanding] = useState(false);
  const [joiningSession, setJoiningSession] = useState(false);
  const [sessionExists, setSessionExists] = useState<boolean | null>(null);
  const [sessionParticipants, setSessionParticipants] = useState<Array<{
    id: string;
    name: string;
    joined_at: string;
    last_seen: string;
    is_active: boolean;
  }>>([]);
  const [networkError, setNetworkError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Get Supabase client from context
  const { supabase } = useSupabase();

  // Connection state management
  const {
    connectionState,
    connect: connectToNetwork,
    disconnect: disconnectFromNetwork,
    forceReconnect,
    isConnected: isNetworkConnected,
    isOffline: isNetworkOffline
  } = useConnectionState({
    maxReconnectAttempts: 5,
    reconnectInterval: 2000,
    exponentialBackoff: true,
    onStatusChange: (status) => {
      console.log('[RepRoomSession] Network status changed:', status);
      if (status === 'failed') {
        setNetworkError('Network connection failed. Please check your internet connection.');
      } else if (status === 'connected') {
        setNetworkError(null);
      }
    },
    onError: (error) => {
      console.error('[RepRoomSession] Network error:', error);
      setNetworkError(error);
    }
  });

  // Session validation
  const {
    validationResult,
    isValidating,
    revalidate,
    canJoinSession,
    errorMessage: validationErrorMessage,
    sessionData
  } = useSessionValidation({
    sessionId: sessionId || '',
    slug: slug || '',
    onValidationChange: (result) => {
      console.log('[RepRoomSession] Session validation result:', result);
      if (!result.isValid && result.error) {
        setError(result.error);
      }
    },
    onError: (error) => {
      console.error('[RepRoomSession] Session validation error:', error);
      setError(error);
    }
  });

  // Extract configuration from URL parameters
  const agentName = searchParams.get('agent') || 'AI Assistant';
  const theme = searchParams.get('theme') || 'system';
  const voiceEnabled = searchParams.get('voice') !== 'false';
  const demoMode = searchParams.get('demo') === 'true';

  // Check if session exists in database and load session state
  const checkSessionExists = async (sessionId: string): Promise<boolean> => {
    try {
      return await retryDatabaseOperation(async () => {
        const { data, error } = await supabase
          .from('rep_room_sessions')
          .select('session_id, created_at, status, metadata')
          .eq('session_id', sessionId)
          .single();

        if (error) {
          console.log('[RepRoomSession] Session not found in database:', sessionId);
          return false;
        }

        if (data) {
          console.log('[RepRoomSession] Found existing session:', data);
          setSessionExists(true);
          
          // Load session participants with retry
          const { data: participants, error: participantsError } = await supabase
            .from('rep_room_participants')
            .select('participant_id, name, joined_at, last_seen, is_active')
            .eq('session_id', sessionId);

          if (!participantsError && participants) {
            setSessionParticipants(participants.map(p => ({
              id: p.participant_id,
              name: p.name,
              joined_at: p.joined_at,
              last_seen: p.last_seen,
              is_active: p.is_active
            })));
          }

          return true;
        }

        return false;
      }, {
        onRetry: (error, attempt) => {
          console.log(`[RepRoomSession] Retrying session check (attempt ${attempt}):`, error.message);
        }
      });
    } catch (error) {
      console.error('[RepRoomSession] Error checking session existence:', error);
      return false;
    }
  };

  // Create new session in database
  const createSession = async (sessionId: string) => {
    try {
      await retryDatabaseOperation(async () => {
        const { error } = await supabase
          .from('rep_room_sessions')
          .insert({
            session_id: sessionId,
            slug: slug,
            status: 'active',
            metadata: {
              agentName,
              voiceEnabled,
              theme,
              demoMode
            }
          });

        if (error) {
          console.error('[RepRoomSession] Error creating session:', error);
          throw new Error(`Failed to create session: ${error.message}`);
        } else {
          console.log('[RepRoomSession] Created new session:', sessionId);
          setSessionExists(true);
        }
      }, {
        onRetry: (error, attempt) => {
          console.log(`[RepRoomSession] Retrying session creation (attempt ${attempt}):`, error.message);
        }
      });
    } catch (error) {
      console.error('[RepRoomSession] Error creating session:', error);
      setError(`Failed to create session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle session ID validation and redirect logic
  useEffect(() => {
    if (!slug) {
      setError('Invalid URL: Missing slug parameter');
      setLoading(false);
      return;
    }

    if (!sessionId) {
      // No session ID provided - show landing page
      console.log('[RepRoomSession] No session ID provided, showing landing page');
      setShowLanding(true);
      setLoading(false);
      return;
    }

    // Validate session ID format
    if (!isValidSessionId(sessionId)) {
      console.warn('[RepRoomSession] Invalid session ID format:', sessionId);
      setError(`Invalid session ID format. Expected 12 characters, got: ${sessionId}`);
      setLoading(false);
      return;
    }

    // Valid session ID - check if session exists and proceed with session loading
    console.log('[RepRoomSession] Valid session ID provided:', sessionId);
    setShowLanding(false);
    setJoiningSession(true);
    
    // Check if session exists in database
    checkSessionExists(sessionId).then(exists => {
      if (exists) {
        console.log('[RepRoomSession] Reconnecting to existing session:', sessionId);
      } else {
        console.log('[RepRoomSession] Creating new session:', sessionId);
        createSession(sessionId);
      }
    });
  }, [slug, sessionId]);

  // Load rep room configuration when we have a valid session
  useEffect(() => {
    if (showLanding || !slug || !sessionId || !joiningSession) {
      return;
    }

    const loadRepRoomConfig = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('[RepRoomSession] Loading configuration for slug:', slug, 'session:', sessionId);

        if (demoMode) {
          // Demo mode - use enhanced demo data
          console.log('[RepRoomSession] Running in demo mode');
          const demoState = createEnhancedDemoRepRoomState(agentName);
          demoState.sessionInfo = {
            title: 'Rep Room V3 Demo Session',
            sessionId: sessionId,
            slug: slug
          };
          setRepRoomState(demoState as RepRoomState);
          setRepRoomConfig({
            repRoom: {
              id: 'demo-v3',
              slug: slug,
              title: 'Rep Room V3 Demo',
              settings: {
                voice: {
                  enabled: voiceEnabled,
                  provider: 'livekit',
                  sttProvider: 'deepgram',
                  ttsProvider: 'elevenlabs',
                  voiceId: 'demo-voice'
                },
                appearance: {
                  themeColor: '#3b82f6',
                  backgroundColor: '#f8fafc',
                  backgroundType: 'solid'
                },
                behavior: {
                  greeting_message: `Welcome to Rep Room V3! I'm ${agentName}, your enhanced AI assistant. Session: ${sessionId}`,
                  voice_input_enabled: voiceEnabled,
                  file_upload_enabled: false
                }
              }
            },
            agent: {
              id: 'demo-agent-v3',
              name: agentName,
              mastraApiBaseUrl: 'https://demo.mastra.cloud',
              mastraAgentId: 'demo-agent',
              cloneId: 'demo-clone-v3',
              cloneName: `${agentName} (V3 Clone)`
            }
          });
          setLoading(false);
          setJoiningSession(false);
          return;
        }

        // Production mode - load from API with session context using retry mechanism
        const response = await fetchWithRetry(
          `/api/public-rep-room-config?slug=${slug}&sessionId=${sessionId}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          },
          {
            maxAttempts: 3,
            initialDelay: 1000,
            onRetry: (error, attempt) => {
              console.log(`[RepRoomSession] Retrying config load (attempt ${attempt}):`, error.message);
              setRetryCount(attempt);
            },
            onFailure: (error, attempts) => {
              console.error(`[RepRoomSession] Config load failed after ${attempts} attempts:`, error);
              setRetryCount(0);
            }
          },
          'rep-room-config'
        );

        const config = await response.json();
        console.log('[RepRoomSession] Loaded configuration:', config);
        
        setRepRoomConfig(config);
        
        // Initialize enhanced state with loaded config and session context
        const enhancedState = createEnhancedDemoRepRoomState(config.agent?.name || agentName);
        enhancedState.sessionInfo = {
          title: config.repRoom?.title || 'Rep Room V3 Session',
          sessionId: sessionId,
          slug: slug
        };
        
        setRepRoomState(enhancedState as RepRoomState);
        setLoading(false);
        setJoiningSession(false);
        
      } catch (err) {
        console.error('[RepRoomSession] Failed to load configuration:', err);
        setError(err instanceof Error ? err.message : 'Failed to load rep room configuration');
        
        // Fallback to demo mode on error
        console.log('[RepRoomSession] Falling back to demo mode due to error');
        const demoState = createEnhancedDemoRepRoomState(agentName);
        demoState.sessionInfo = {
          title: 'Rep Room V3 (Fallback)',
          sessionId: sessionId,
          slug: slug
        };
        setRepRoomState(demoState as RepRoomState);
        setRepRoomConfig({
          repRoom: {
            id: 'fallback-v3',
            slug: slug,
            title: 'Rep Room V3 (Fallback)',
            settings: {
              voice: { enabled: voiceEnabled, provider: 'livekit' },
              appearance: { themeColor: '#ef4444' },
              behavior: {
                greeting_message: `Running in fallback mode. Session: ${sessionId}`,
                voice_input_enabled: voiceEnabled,
                file_upload_enabled: false
              }
            }
          },
          agent: {
            id: 'fallback-agent',
            name: agentName,
            cloneId: 'fallback-clone'
          }
        });
        setLoading(false);
        setJoiningSession(false);
      }
    };

    loadRepRoomConfig();
  }, [slug, sessionId, agentName, voiceEnabled, demoMode, joiningSession]);

  // Handle state changes
  const handleStateChange = (newState: RepRoomState) => {
    console.log('[RepRoomSession] State updated:', newState);
    setRepRoomState(newState);
  };

  // Handle landing page start action - generates new session ID and redirects
  const handleLandingStart = () => {
    if (slug) {
      const newSessionId = generateSessionId();
      const currentSearchParams = searchParams.toString();
      const redirectPath = `/rroom-v3/${slug}/${newSessionId}${currentSearchParams ? `?${currentSearchParams}` : ''}`;
      
      console.log('[RepRoomSession] Starting new session, navigating to:', redirectPath);
      navigate(redirectPath, { replace: true });
    }
  };

  // Handle demo mode from landing page
  const handleLandingDemo = () => {
    if (slug) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('demo', 'true');
      const newSessionId = generateSessionId();
      const redirectPath = `/rroom-v3/${slug}/${newSessionId}?${newSearchParams.toString()}`;
      
      console.log('[RepRoomSession] Starting demo session, navigating to:', redirectPath);
      navigate(redirectPath, { replace: true });
    }
  };

  // Show landing page when no session ID is present
  if (showLanding) {
    const landingConfig = {
      title: agentName,
      description: `Welcome to Rep Room V3! Experience our enhanced multi-agent collaborative interface with ${agentName}.`,
      logoUrl: undefined,
      primaryColor: '#4F46E5'
    };

    return (
      <LandingViewV3
        onStart={handleLandingStart}
        config={landingConfig}
        loading={loading}
        error={error}
        canTryDemo={true}
        onTryDemo={handleLandingDemo}
      />
    );
  }

  // Show joining session state
  if (joiningSession) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {sessionExists === true ? 'Reconnecting to Session' : 'Joining Session'}
          </h2>
          <p className="text-gray-600 mb-1">
            {sessionExists === true ? 'Restoring session state...' : 'Connecting to session:'} {sessionId}
          </p>
          {sessionExists === true && sessionParticipants.length > 0 && (
            <p className="text-sm text-green-600 mb-2">
              Found {sessionParticipants.length} previous participant{sessionParticipants.length !== 1 ? 's' : ''}
            </p>
          )}
          <p className="text-sm text-gray-500">
            {sessionExists === true ? 'Loading chat history and session data...' : 'Initializing LiveKit connection...'}
          </p>
        </div>
      </div>
    );
  }

  // Loading state
  if (loading) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Rep Room V3</h2>
          <p className="text-gray-600">Initializing enhanced multi-agent interface...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !repRoomConfig) {
    return (
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Rep Room</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="space-y-2">
            <button 
              onClick={() => window.location.reload()} 
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors w-full"
            >
              Retry
            </button>
            <button 
              onClick={() => navigate(`/rroom-v3/${slug}`)} 
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors w-full"
            >
              Back to Landing
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Get voice configuration
  const voiceConfig = {
    enabled: repRoomConfig?.repRoom?.settings?.voice?.enabled ?? voiceEnabled,
    provider: repRoomConfig?.repRoom?.settings?.voice?.provider || 'livekit',
    sttProvider: repRoomConfig?.repRoom?.settings?.voice?.sttProvider || 'deepgram',
    ttsProvider: repRoomConfig?.repRoom?.settings?.voice?.ttsProvider || 'elevenlabs',
    voiceId: repRoomConfig?.repRoom?.settings?.voice?.voiceId || 'default'
  };

  // Get CopilotKit runtime URL
  const copilotKitRuntimeUrl = repRoomConfig?.agent?.mastraApiBaseUrl 
    ? `${repRoomConfig.agent.mastraApiBaseUrl}/copilotkit`
    : '/api/copilotkit';

  return (
    <ErrorBoundary
      fallback={
        <div className="h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Rep Room Error</h2>
            <p className="text-gray-600 mb-4">
              An unexpected error occurred while loading the Rep Room session.
            </p>
            <div className="space-y-2">
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors w-full"
              >
                Reload Page
              </button>
              <button
                onClick={() => navigate(`/rroom-v3/${slug}`)}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors w-full"
              >
                Back to Landing
              </button>
            </div>
          </div>
        </div>
      }
      onError={(error, errorInfo) => {
        console.error('[RepRoomSession] Error boundary caught error:', error, errorInfo);
      }}
    >
      <div className={`rep-room-session ${className}`}>
        {/* Connection Status Indicator */}
        <ConnectionStatusIndicator
          connectionState={connectionState}
          onReconnect={forceReconnect}
          className="fixed top-4 right-4 z-50"
        />

        {/* Network Error Banner */}
        {networkError && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Network Error: {networkError}
                </p>
                {retryCount > 0 && (
                  <p className="text-xs text-red-600 mt-1">
                    Retry attempt: {retryCount}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Session Validation Error Banner */}
        {validationErrorMessage && (
          <div className="bg-orange-50 border-l-4 border-orange-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-orange-700">
                  Session Validation: {validationErrorMessage}
                </p>
                <button
                  onClick={revalidate}
                  className="text-xs text-orange-600 underline mt-1 hover:text-orange-800"
                >
                  Retry Validation
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error banner if in fallback mode */}
        {error && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Running in fallback mode. Some features may be limited. Error: {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Voice Provider Wrapper with Session Context */}
        <UnifiedVoiceProvider voiceConfig={voiceConfig} sessionId={sessionId}>
          {/* CopilotKit Provider */}
          <CopilotKit runtimeUrl={copilotKitRuntimeUrl}>
            {/* Enhanced Rep Room Interface with Session Context */}
            <RepRoomInterfaceEnhanced
              initialState={repRoomState || undefined}
              onStateChange={handleStateChange}
              agentName={repRoomConfig?.agent?.name || agentName}
              sessionId={sessionId || generateSessionId()}
              copilotKitRuntimeUrl={copilotKitRuntimeUrl}
              className="rep-room-session-interface"
            />
          </CopilotKit>
        </UnifiedVoiceProvider>

        {/* Session Info (only in demo mode) */}
        {demoMode && (
          <div className="fixed bottom-4 right-4 bg-blue-900 text-white p-3 rounded-lg shadow-lg text-xs max-w-xs">
            <div className="font-semibold mb-1">Rep Room V3 Session</div>
            <div>Slug: {slug}</div>
            <div>Session: {sessionId}</div>
            <div>Voice: {voiceConfig.enabled ? 'Enabled' : 'Disabled'}</div>
            <div>Agent: {repRoomConfig?.agent?.name || agentName}</div>
            <div className="text-green-300 mt-1">✓ Session Active</div>
            <div className="text-xs text-blue-200 mt-1">
              Network: {connectionState.status}
            </div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
};

export default RepRoomSessionPage;