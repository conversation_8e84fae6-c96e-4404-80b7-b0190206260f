import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// Request interface for session creation
interface CreateSessionRequest {
  rep_room_slug: string
  participant_name?: string
  metadata?: Record<string, any>
}

// Response interface for session creation
interface CreateSessionResponse {
  success: boolean
  data?: {
    slug: string
    session_id: string
    redirect_url: string
  }
  error?: {
    code: string
    message: string
    details?: any
  }
}

// Helper function to generate session ID
function generateSessionId(): string {
  return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// Helper function to validate rep room slug
function validateSlug(slug: string): boolean {
  // Allow alphanumeric characters, hyphens, and underscores
  const slugRegex = /^[a-zA-Z0-9_-]+$/
  return slugRegex.test(slug) && slug.length >= 1 && slug.length <= 50
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Only POST method is allowed',
        },
      } as CreateSessionResponse),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }

  try {
    // Initialize Supabase service client
    const supabaseServiceClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    let requestData: CreateSessionRequest
    try {
      requestData = await req.json()
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_JSON',
            message: 'Invalid JSON in request body',
          },
        } as CreateSessionResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Validate required fields
    if (!requestData.rep_room_slug) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_FIELD',
            message: 'rep_room_slug is required',
          },
        } as CreateSessionResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Validate slug format
    if (!validateSlug(requestData.rep_room_slug)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_SLUG',
            message: 'Invalid rep room slug format',
          },
        } as CreateSessionResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Check if rep room exists and is accessible
    let repRoom: any = null
    
    // Check if the slug is a UUID format (direct rep room ID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    const isUuidFormat = uuidRegex.test(requestData.rep_room_slug)

    if (isUuidFormat) {
      // Look for rep room by ID
      const { data: repRoomData, error: repRoomError } = await supabaseServiceClient
        .from('rep_rooms')
        .select('id, title, public_slug, is_enabled, user_agent_clone_id')
        .eq('id', requestData.rep_room_slug)
        .eq('is_enabled', true)
        .single()

      if (repRoomError || !repRoomData) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'NOT_FOUND',
              message: 'Rep room not found or not enabled',
            },
          } as CreateSessionResponse),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }
      
      repRoom = repRoomData
    } else {
      // Look for rep room by public slug
      const { data: repRoomData, error: repRoomError } = await supabaseServiceClient
        .from('rep_rooms')
        .select('id, title, public_slug, is_enabled, user_agent_clone_id')
        .eq('public_slug', requestData.rep_room_slug)
        .eq('is_enabled', true)
        .single()

      if (repRoomError || !repRoomData) {
        // Try fallback search by title
        const { data: repRoomByTitle, error: titleError } = await supabaseServiceClient
          .from('rep_rooms')
          .select('id, title, public_slug, is_enabled, user_agent_clone_id')
          .ilike('title', `%${requestData.rep_room_slug}%`)
          .eq('is_enabled', true)
          .single()

        if (titleError || !repRoomByTitle) {
          return new Response(
            JSON.stringify({
              success: false,
              error: {
                code: 'NOT_FOUND',
                message: `Rep room not found for slug: ${requestData.rep_room_slug}`,
              },
            } as CreateSessionResponse),
            {
              status: 404,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
          )
        }
        
        repRoom = repRoomByTitle
      } else {
        repRoom = repRoomData
      }
    }

    // Generate unique session ID
    const sessionId = generateSessionId()
    
    // Use the actual slug from the rep room or the provided slug
    const actualSlug = repRoom.public_slug || requestData.rep_room_slug
    
    // Create session record in database
    const sessionData = {
      slug: actualSlug,
      session_id: sessionId,
      room_name: `rrs-${actualSlug}-${sessionId}`,
      status: 'active',
      participant_count: 0,
      agent_joined: false,
      metadata: {
        rep_room_id: repRoom.id,
        rep_room_title: repRoom.title,
        agent_clone_id: repRoom.user_agent_clone_id,
        participant_name: requestData.participant_name,
        created_via: 'session_creation_endpoint',
        ...requestData.metadata
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { error: insertError } = await supabaseServiceClient
      .from('rep_room_sessions')
      .insert(sessionData)

    if (insertError) {
      console.error('Failed to create session record:', insertError)
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: 'Failed to create session',
            details: insertError.message,
          },
        } as CreateSessionResponse),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Generate redirect URL
    const redirectUrl = `/rroom-v3/${actualSlug}/${sessionId}`

    console.log('✅ Session created successfully:', {
      slug: actualSlug,
      sessionId,
      repRoomId: repRoom.id,
      redirectUrl
    })

    // Return session details for frontend redirect
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          slug: actualSlug,
          session_id: sessionId,
          redirect_url: redirectUrl,
        },
      } as CreateSessionResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Session creation error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          details: error.message,
        },
      } as CreateSessionResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})