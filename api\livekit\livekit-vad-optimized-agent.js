#!/usr/bin/env node

/**
 * VAD-Optimized LiveKit Agent for Rep Room Voice Processing
 * Implements Voice Activity Detection to drastically reduce Deepgram costs
 * Only processes speech when actually detected using advanced VAD algorithms
 */

import { AccessToken } from 'livekit-server-sdk';
import WebSocket from 'ws';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '../../.env' });
dotenv.config({ path: '.env' });
dotenv.config();

// Configuration
const LIVEKIT_URL = process.env.LIVEKIT_URL || 'wss://ng53116-dc2-9mp3kcwz.livekit.cloud';
const LIVEKIT_API_KEY = process.env.LIVEKIT_API_KEY;
const LIVEKIT_API_SECRET = process.env.LIVEKIT_API_SECRET;
const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

// VAD and cost optimization configuration
const VAD_CONFIG = {
  // VAD sensitivity settings
  speechThreshold: 0.5,
  silenceThreshold: 0.3,
  minSpeechDurationMs: 300,
  maxSilenceDurationMs: 2000,
  
  // Cost optimization settings
  sttStartupDelayMs: 100,
  sttShutdownDelayMs: 1000,
  maxSttSessionDurationMs: 30000,
  
  // Audio analysis settings
  audioLevelSmoothing: 0.7,
  silenceFramesThreshold: 50,
  speechFramesThreshold: 3,
  
  // Inactivity settings
  inactivityTimeoutSeconds: 90,
  warningThresholdSeconds: 75
};

// VAD States
const VAD_STATES = {
  IDLE: 'idle',
  SPEECH_DETECTED: 'speech_detected',
  PROCESSING: 'processing',
  COOLDOWN: 'cooldown'
};

// Agent configuration
const AGENT_NAME = 'rep-room-vad-optimized-agent';
const AGENT_IDENTITY = 'vad-voice-agent';

console.log('🚀 Starting VAD-Optimized LiveKit Agent');
console.log(`📍 LiveKit URL: ${LIVEKIT_URL}`);
console.log(`🔑 API Key: ${LIVEKIT_API_KEY ? 'Set' : 'Missing'}`);
console.log(`🔐 API Secret: ${LIVEKIT_API_SECRET ? 'Set' : 'Missing'}`);
console.log(`🎤 Deepgram Key: ${DEEPGRAM_API_KEY ? 'Set' : 'Missing'}`);
console.log(`🧠 OpenAI Key: ${OPENAI_API_KEY ? 'Set' : 'Missing'}`);
console.log(`🎯 VAD Optimization: Enabled (${VAD_CONFIG.speechThreshold} threshold)`);

// Check required environment variables
const requiredVars = ['LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET', 'DEEPGRAM_API_KEY', 'OPENAI_API_KEY'];
const missingVars = requiredVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars);
  process.exit(1);
}

// Get room name from command line or use default
const roomName = process.argv[2] || 'rep-room-5225851a-0349-497d-9398-9f42f811a93d';
console.log(`🏠 Target room: ${roomName}`);

// Global state
let conversationHistory = [];
let deepgramWs = null;
let isProcessingAudio = false;

// VAD state management
class VoiceActivityDetector {
  constructor(config) {
    this.config = config;
    this.state = {
      currentState: VAD_STATES.IDLE,
      lastSpeechTime: Date.now(),
      lastActivityTime: Date.now(),
      sessionStartTime: Date.now(),
      
      // Audio analysis
      currentAudioLevel: 0,
      smoothedAudioLevel: 0,
      silenceFrameCount: 0,
      speechFrameCount: 0,
      
      // STT session management
      sttSessionActive: false,
      sttSessionStartTime: 0,
      sttStartupTimer: null,
      sttShutdownTimer: null,
      
      // Cost tracking
      totalSttTimeSeconds: 0,
      sttActivations: 0,
      falsePositiveCount: 0
    };
    
    this.callbacks = {
      speechStart: [],
      speechEnd: [],
      sttStart: [],
      sttStop: [],
      inactivityWarning: [],
      inactivityTimeout: []
    };
  }
  
  addCallback(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }
  
  emitEvent(event, data = {}) {
    this.callbacks[event]?.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in VAD callback for ${event}:`, error);
      }
    });
  }
  
  analyzeAudioLevel(audioBuffer) {
    // Calculate RMS audio level
    let sum = 0;
    for (let i = 0; i < audioBuffer.length; i++) {
      sum += audioBuffer[i] * audioBuffer[i];
    }
    const rms = Math.sqrt(sum / audioBuffer.length);
    
    // Normalize to 0-1 range
    this.state.currentAudioLevel = Math.min(rms / 32768.0, 1.0);
    
    // Apply exponential smoothing
    this.state.smoothedAudioLevel = (
      this.config.audioLevelSmoothing * this.state.smoothedAudioLevel +
      (1 - this.config.audioLevelSmoothing) * this.state.currentAudioLevel
    );
    
    return this.state.smoothedAudioLevel > this.config.speechThreshold;
  }
  
  processAudioFrame(audioBuffer) {
    const currentTime = Date.now();
    const isSpeech = this.analyzeAudioLevel(audioBuffer);
    
    if (isSpeech) {
      this.state.speechFrameCount++;
      this.state.silenceFrameCount = 0;
    } else {
      this.state.silenceFrameCount++;
      if (this.state.smoothedAudioLevel < this.config.silenceThreshold) {
        this.state.speechFrameCount = Math.max(0, this.state.speechFrameCount - 1);
      }
    }
    
    // State machine for VAD
    switch (this.state.currentState) {
      case VAD_STATES.IDLE:
        if (isSpeech && this.state.speechFrameCount >= this.config.speechFramesThreshold) {
          this.transitionToSpeechDetected(currentTime);
        }
        break;
        
      case VAD_STATES.SPEECH_DETECTED:
        if (isSpeech) {
          this.state.lastSpeechTime = currentTime;
          // Start STT after minimum speech duration
          if (currentTime - this.state.lastSpeechTime >= this.config.minSpeechDurationMs) {
            this.transitionToProcessing(currentTime);
          }
        } else if (this.state.silenceFrameCount >= this.config.silenceFramesThreshold) {
          this.transitionToIdle();
        }
        break;
        
      case VAD_STATES.PROCESSING:
        if (isSpeech) {
          this.state.lastSpeechTime = currentTime;
        } else {
          const silenceDuration = currentTime - this.state.lastSpeechTime;
          if (silenceDuration >= this.config.maxSilenceDurationMs) {
            this.transitionToCooldown(currentTime);
          }
        }
        break;
        
      case VAD_STATES.COOLDOWN:
        if (isSpeech) {
          this.transitionToProcessing(currentTime);
        } else {
          const cooldownDuration = currentTime - this.state.lastActivityTime;
          if (cooldownDuration >= this.config.sttShutdownDelayMs) {
            this.transitionToIdle();
          }
        }
        break;
    }
    
    // Check for inactivity timeout
    this.checkInactivityTimeout(currentTime);
    
    // Check for maximum STT session duration
    if (this.state.sttSessionActive && 
        (currentTime - this.state.sttSessionStartTime) >= this.config.maxSttSessionDurationMs) {
      console.warn('Maximum STT session duration reached, forcing stop');
      this.stopSttSession();
    }
  }
  
  transitionToSpeechDetected(currentTime) {
    console.log('VAD: Speech detected');
    this.state.currentState = VAD_STATES.SPEECH_DETECTED;
    this.state.lastSpeechTime = currentTime;
    this.state.lastActivityTime = currentTime;
    this.emitEvent('speechStart', {
      timestamp: currentTime,
      audioLevel: this.state.smoothedAudioLevel
    });
  }
  
  transitionToProcessing(currentTime) {
    console.log('VAD: Starting STT processing');
    this.state.currentState = VAD_STATES.PROCESSING;
    this.state.lastActivityTime = currentTime;
    
    // Cancel any pending shutdown timer
    if (this.state.sttShutdownTimer) {
      clearTimeout(this.state.sttShutdownTimer);
      this.state.sttShutdownTimer = null;
    }
    
    // Start STT session with delay
    if (!this.state.sttSessionActive) {
      this.state.sttStartupTimer = setTimeout(() => {
        if (this.state.currentState === VAD_STATES.PROCESSING) {
          this.startSttSession();
        }
      }, this.config.sttStartupDelayMs);
    }
  }
  
  transitionToCooldown(currentTime) {
    console.log('VAD: Entering cooldown');
    this.state.currentState = VAD_STATES.COOLDOWN;
    this.state.lastActivityTime = currentTime;
    this.emitEvent('speechEnd', {
      timestamp: currentTime,
      duration: currentTime - this.state.lastSpeechTime
    });
    
    // Schedule STT shutdown with delay
    if (this.state.sttSessionActive && !this.state.sttShutdownTimer) {
      this.state.sttShutdownTimer = setTimeout(() => {
        if (this.state.currentState === VAD_STATES.COOLDOWN) {
          this.stopSttSession();
        }
      }, this.config.sttShutdownDelayMs);
    }
  }
  
  transitionToIdle() {
    console.log('VAD: Returning to idle');
    this.state.currentState = VAD_STATES.IDLE;
    this.state.speechFrameCount = 0;
    this.state.silenceFrameCount = 0;
    
    // Ensure STT is stopped
    if (this.state.sttSessionActive) {
      this.stopSttSession();
    }
  }
  
  startSttSession() {
    if (!this.state.sttSessionActive) {
      const currentTime = Date.now();
      this.state.sttSessionActive = true;
      this.state.sttSessionStartTime = currentTime;
      this.state.sttActivations++;
      
      console.log(`🎤 STT Session Started (Activation #${this.state.sttActivations})`);
      this.emitEvent('sttStart', {
        timestamp: currentTime,
        activationCount: this.state.sttActivations
      });
    }
  }
  
  stopSttSession() {
    if (this.state.sttSessionActive) {
      const currentTime = Date.now();
      const sessionDuration = (currentTime - this.state.sttSessionStartTime) / 1000;
      this.state.totalSttTimeSeconds += sessionDuration;
      this.state.sttSessionActive = false;
      
      // Cancel any pending timers
      if (this.state.sttStartupTimer) {
        clearTimeout(this.state.sttStartupTimer);
        this.state.sttStartupTimer = null;
      }
      if (this.state.sttShutdownTimer) {
        clearTimeout(this.state.sttShutdownTimer);
        this.state.sttShutdownTimer = null;
      }
      
      console.log(`🛑 STT Session Stopped (Duration: ${sessionDuration.toFixed(2)}s, Total: ${this.state.totalSttTimeSeconds.toFixed(2)}s)`);
      this.emitEvent('sttStop', {
        timestamp: currentTime,
        sessionDuration,
        totalSttTime: this.state.totalSttTimeSeconds
      });
    }
  }
  
  checkInactivityTimeout(currentTime) {
    const timeSinceActivity = (currentTime - this.state.lastActivityTime) / 1000;
    
    if (timeSinceActivity >= this.config.inactivityTimeoutSeconds) {
      console.warn('Inactivity timeout reached');
      this.emitEvent('inactivityTimeout', {
        timestamp: currentTime,
        inactiveDuration: timeSinceActivity
      });
    } else if (timeSinceActivity >= this.config.warningThresholdSeconds) {
      console.warn('Inactivity warning threshold reached');
      this.emitEvent('inactivityWarning', {
        timestamp: currentTime,
        remainingTime: this.config.inactivityTimeoutSeconds - timeSinceActivity
      });
    }
  }
  
  getStats() {
    const currentTime = Date.now();
    const sessionDuration = (currentTime - this.state.sessionStartTime) / 1000;
    
    return {
      state: this.state.currentState,
      sessionDuration,
      totalSttTime: this.state.totalSttTimeSeconds,
      sttActivations: this.state.sttActivations,
      sttEfficiency: sessionDuration > 0 ? (this.state.totalSttTimeSeconds / sessionDuration * 100) : 0,
      currentAudioLevel: this.state.currentAudioLevel,
      smoothedAudioLevel: this.state.smoothedAudioLevel,
      sttSessionActive: this.state.sttSessionActive,
      timeSinceActivity: (currentTime - this.state.lastActivityTime) / 1000
    };
  }
}

// Initialize VAD detector
const vadDetector = new VoiceActivityDetector(VAD_CONFIG);

/**
 * Generate agent token for LiveKit room
 */
function generateAgentToken(roomName) {
  try {
    const token = new AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET, {
      identity: AGENT_IDENTITY,
      name: AGENT_NAME,
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = token.toJwt();
    console.log('✅ Generated agent token successfully');
    return jwt;
  } catch (error) {
    console.error('❌ Failed to generate agent token:', error);
    throw error;
  }
}

/**
 * Initialize Deepgram WebSocket for STT (only when VAD detects speech)
 */
function initializeDeepgram() {
  return new Promise((resolve, reject) => {
    try {
      const deepgramUrl = `wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&smart_format=true&interim_results=true&endpointing=300&utterance_end_ms=1000&vad_turnoff=250`;
      
      deepgramWs = new WebSocket(deepgramUrl, {
        headers: {
          'Authorization': `Token ${DEEPGRAM_API_KEY}`,
        },
      });

      deepgramWs.on('open', () => {
        console.log('✅ Connected to Deepgram STT WebSocket (VAD-controlled)');
        resolve();
      });

      deepgramWs.on('message', (data) => {
        try {
          const result = JSON.parse(data);
          if (result.channel?.alternatives?.[0]?.transcript) {
            const transcript = result.channel.alternatives[0].transcript;
            const confidence = result.channel.alternatives[0].confidence;
            const isFinal = result.is_final;
            
            console.log(`🗣️ STT: "${transcript}" (confidence: ${confidence}, final: ${isFinal})`);
            
            // Process final transcripts for AI response
            if (isFinal && transcript.trim()) {
              handleFinalTranscript(transcript);
            }
          }
        } catch (error) {
          console.error('Error parsing Deepgram response:', error);
        }
      });

      deepgramWs.on('error', (error) => {
        console.error('❌ Deepgram WebSocket error:', error);
        reject(error);
      });

      deepgramWs.on('close', () => {
        console.log('👋 Deepgram connection closed');
        deepgramWs = null;
      });

    } catch (error) {
      console.error('❌ Failed to initialize Deepgram:', error);
      reject(error);
    }
  });
}

/**
 * Handle final transcript for AI processing
 */
async function handleFinalTranscript(transcript) {
  try {
    console.log(`🧠 Processing final transcript: "${transcript}"`);
    
    // Record speech activity to reset inactivity timeout
    vadDetector.state.lastActivityTime = Date.now();
    
    // Add to conversation history
    conversationHistory.push({
      role: 'user',
      content: transcript,
      timestamp: Date.now()
    });

    // Generate AI response
    const response = await generateAIResponse(transcript);
    
    if (response) {
      console.log(`🤖 AI Response: "${response}"`);
      
      // Add AI response to history
      conversationHistory.push({
        role: 'assistant',
        content: response,
        timestamp: Date.now()
      });

      console.log('📤 Response ready for frontend delivery');
    }
    
  } catch (error) {
    console.error('❌ Error handling final transcript:', error);
  }
}

/**
 * Generate AI response using OpenAI
 */
async function generateAIResponse(userInput) {
  try {
    const messages = [
      {
        role: 'system',
        content: 'You are a helpful SEO and keyword research assistant in Rep Room T1. You help users with SEO strategies, keyword research, content optimization, and digital marketing insights. Keep your responses conversational and under 100 words for voice interactions. Be friendly and professional.'
      },
      ...conversationHistory.slice(-10),
      {
        role: 'user',
        content: userInput
      }
    ];

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages,
        max_tokens: 150,
        temperature: 0.7,
      }),
    });

    const data = await response.json();
    return data.choices?.[0]?.message?.content?.trim();
    
  } catch (error) {
    console.error('❌ Error generating AI response:', error);
    return 'I apologize, but I encountered an error processing your request. Please try again.';
  }
}

/**
 * Simulate audio processing with VAD optimization
 */
function simulateVADOptimizedProcessing() {
  console.log('\n🎵 Simulating VAD-optimized audio processing...');
  
  // Simulate audio frames with varying levels
  let frameCount = 0;
  const audioSimulation = setInterval(() => {
    frameCount++;
    
    // Simulate audio buffer with varying levels
    const audioBuffer = new Array(1024).fill(0).map(() => 
      Math.random() * (frameCount % 100 < 20 ? 16000 : 1000) // Speech vs silence
    );
    
    // Process through VAD
    vadDetector.processAudioFrame(audioBuffer);
    
    // Log stats every 100 frames
    if (frameCount % 100 === 0) {
      const stats = vadDetector.getStats();
      console.log(`📊 VAD Stats - State: ${stats.state}, STT Efficiency: ${stats.sttEfficiency.toFixed(1)}%`);
    }
    
    // Stop simulation after 1000 frames
    if (frameCount >= 1000) {
      clearInterval(audioSimulation);
      console.log('✅ VAD simulation complete');
      
      const finalStats = vadDetector.getStats();
      console.log(`💰 Final Cost Optimization Results:`);
      console.log(`   STT Efficiency: ${finalStats.sttEfficiency.toFixed(1)}%`);
      console.log(`   STT Activations: ${finalStats.sttActivations}`);
      console.log(`   Total STT Time: ${finalStats.totalSttTime.toFixed(1)}s`);
      console.log(`   Session Duration: ${finalStats.sessionDuration.toFixed(1)}s`);
    }
  }, 50); // 50ms intervals (20fps)
}

/**
 * Set up VAD callbacks for STT lifecycle management
 */
function setupVADCallbacks() {
  vadDetector.addCallback('sttStart', async () => {
    if (!deepgramWs) {
      console.log('🎯 VAD triggered STT start - initializing Deepgram');
      try {
        await initializeDeepgram();
        isProcessingAudio = true;
      } catch (error) {
        console.error('Failed to start Deepgram on VAD trigger:', error);
      }
    }
  });
  
  vadDetector.addCallback('sttStop', () => {
    if (deepgramWs) {
      console.log('🛑 VAD triggered STT stop - closing Deepgram');
      deepgramWs.close();
      deepgramWs = null;
      isProcessingAudio = false;
      
      // Log cost savings
      const stats = vadDetector.getStats();
      console.log(`💰 STT Cost Optimization - Efficiency: ${stats.sttEfficiency.toFixed(1)}% ` +
                 `(Active: ${stats.totalSttTime.toFixed(1)}s / Session: ${stats.sessionDuration.toFixed(1)}s)`);
    }
  });
  
  vadDetector.addCallback('inactivityTimeout', () => {
    console.log('🚨 VAD detected inactivity timeout - shutting down');
    if (deepgramWs) {
      deepgramWs.close();
    }
    process.exit(0);
  });
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('\n🎯 Initializing VAD-Optimized Voice Agent...');
    
    // Set up VAD callbacks
    setupVADCallbacks();
    
    // Generate and test LiveKit token
    console.log('\n🎫 Testing LiveKit token generation...');
    const token = generateAgentToken(roomName);
    console.log('✅ LiveKit token generated successfully');
    
    console.log('\n🎉 VAD-Optimized Agent is ready!');
    console.log('\n📋 Current Status:');
    console.log('   ✅ LiveKit token generation working');
    console.log('   ✅ VAD-based STT lifecycle management active');
    console.log('   ✅ OpenAI AI response generation working');
    console.log('   ✅ Cost optimization enabled');
    
    console.log('\n🎯 The agent can now:');
    console.log('   • Detect voice activity using advanced VAD');
    console.log('   • Start/stop STT only when speech is detected');
    console.log('   • Drastically reduce Deepgram costs');
    console.log('   • Generate AI responses via OpenAI');
    console.log('   • Monitor for inactivity and auto-shutdown');
    
    console.log('\n🎮 Agent is running. Press Ctrl+C to stop.');
    
    // Start audio simulation for testing
    setTimeout(() => {
      simulateVADOptimizedProcessing();
    }, 2000);
    
    // Keep the process running and show periodic stats
    setInterval(() => {
      const stats = vadDetector.getStats();
      console.log(`💓 Heartbeat - Session: ${stats.sessionDuration.toFixed(0)}s, ` +
                 `STT Efficiency: ${stats.sttEfficiency.toFixed(1)}%, ` +
                 `Idle: ${stats.timeSinceActivity.toFixed(0)}s`);
    }, 30000);
    
  } catch (error) {
    console.error('❌ Agent startup failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down VAD-optimized agent...');
  
  // Stop VAD processing
  if (vadDetector.state.sttSessionActive) {
    vadDetector.stopSttSession();
  }
  
  // Close Deepgram connection
  if (deepgramWs) {
    deepgramWs.close();
    console.log('✅ Closed Deepgram connection');
  }
  
  // Show final stats
  const finalStats = vadDetector.getStats();
  console.log('\n📊 Final Session Statistics:');
  console.log(`   Session Duration: ${finalStats.sessionDuration.toFixed(1)}s`);
  console.log(`   Total STT Time: ${finalStats.totalSttTime.toFixed(1)}s`);
  console.log(`   STT Efficiency: ${finalStats.sttEfficiency.toFixed(1)}%`);
  console.log(`   STT Activations: ${finalStats.sttActivations}`);
  console.log(`   Cost Savings: ${(100 - finalStats.sttEfficiency).toFixed(1)}%`);
  
  console.log('🧹 Cleanup completed');
  process.exit(0);
});

// Run the agent
main().catch(console.error);