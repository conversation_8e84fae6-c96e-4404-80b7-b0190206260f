import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// Helper function to create properly signed LiveKit JWT tokens
async function createLiveKitJWT(payload: any, secret: string): Promise<string> {
  // JWT header
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  }

  // Base64URL encode header and payload
  const encodedHeader = base64UrlEncode(JSON.stringify(header))
  const encodedPayload = base64UrlEncode(JSON.stringify(payload))
  
  // Create signature data
  const signatureData = `${encodedHeader}.${encodedPayload}`
  
  // Create HMAC-SHA256 signature
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(secret),
    { name: '<PERSON><PERSON>', hash: 'SHA-256' },
    false,
    ['sign']
  )
  
  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    new TextEncoder().encode(signatureData)
  )
  
  // Base64URL encode signature
  const encodedSignature = base64UrlEncode(signature)
  
  // Return complete JWT
  return `${signatureData}.${encodedSignature}`
}

// Helper function for Base64URL encoding
function base64UrlEncode(data: string | ArrayBuffer): string {
  let base64: string
  
  if (typeof data === 'string') {
    base64 = btoa(data)
  } else {
    // Convert ArrayBuffer to base64
    const bytes = new Uint8Array(data)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    base64 = btoa(binary)
  }
  
  // Convert base64 to base64url
  return base64
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
}

// LiveKit room metadata structure for agent context passing
interface LiveKitRoomMetadata {
  // Operational flags
  agent_required: boolean
  auto_dispatch_agent: boolean  // false = wait for external agent (Fly.io)
  
  // Core identifiers
  rep_room_id: string
  agent_clone_id: string
  organization_id: string
  
  // User and session context
  user_id: string | null
  session_id: string
  participant_id: string
  participant_name: string
  
  // Agent configuration
  agent_config: Record<string, any> | null
  
  // Access control metadata
  is_public_access: boolean
  is_anonymous: boolean
  
  // Debugging metadata
  metadata_created_at: string
  room_creation_context: string
}

// LiveKit token generation with proper JWT signing
interface LiveKitTokenRequest {
  rep_room_id: string
  session_id_from_url?: string // Accept session ID from URL parameter
  session_id?: string // CRITICAL: Accept session ID from frontend (backward compatibility)
  participant_name?: string
  participant_metadata?: Record<string, any>
}

interface LiveKitTokenResponse {
  success: boolean
  data?: {
    token: string
    room_name: string
    participant_id: string
    expires_at: string
    livekit_url: string
    deepgram_api_key?: string
    session_id?: string // Add session ID for frontend tracking
  }
  error?: {
    code: string
    message: string
    details?: any
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Only POST method is allowed',
        },
      } as LiveKitTokenResponse),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }

  try {
    // Initialize Supabase client for user authentication
    // Handle both authenticated and anonymous users
    const authHeader = req.headers.get('Authorization')
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      authHeader ? {
        global: {
          headers: { Authorization: authHeader },
        },
      } : {}
    )

    // Initialize service role client for database queries (bypasses RLS)
    const supabaseServiceClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get current user (may be null for public rep rooms)
    let user = null
    let userError = null
    
    if (authHeader) {
      const authResult = await supabaseClient.auth.getUser()
      user = authResult.data.user
      userError = authResult.error
    }

    // For debugging: log authentication status
    console.log('Authentication status:', {
      hasUser: !!user,
      userError: userError?.message,
      authHeader: !!authHeader
    })

    // Parse request body
    let requestData: LiveKitTokenRequest
    try {
      requestData = await req.json()
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_JSON',
            message: 'Invalid JSON in request body',
          },
        } as LiveKitTokenResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Validate required fields
    if (!requestData.rep_room_id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_FIELD',
            message: 'rep_room_id is required',
          },
        } as LiveKitTokenResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Try to get rep room first, if not found, try to find by agent clone ID
    let repRoom: any = null
    let repRoomError: any = null

    // Check if the ID is a UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    const isUuidFormat = uuidRegex.test(requestData.rep_room_id)

    if (isUuidFormat) {
      // First attempt: Look for rep room by ID (UUID format)
      const { data: repRoomData, error: repRoomQueryError } = await supabaseServiceClient
        .from('rep_rooms')
        .select('id, title, settings, user_agent_clone_id, public_slug, is_enabled')
        .eq('id', requestData.rep_room_id)
        .single()

      if (repRoomQueryError && repRoomQueryError.code === 'PGRST116') {
        // Rep room not found by ID, try to find by agent clone ID
        console.log('Rep room not found by ID, trying to find by agent clone ID:', requestData.rep_room_id)
        
        const { data: agentCloneData, error: agentCloneError } = await supabaseServiceClient
          .from('user_agent_clones')
          .select('id, user_id')
          .eq('id', requestData.rep_room_id)
          .single()

        if (agentCloneError || !agentCloneData) {
          return new Response(
            JSON.stringify({
              success: false,
              error: {
                code: 'NOT_FOUND',
                message: 'Rep room or agent clone not found',
              },
            } as LiveKitTokenResponse),
            {
              status: 404,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
          )
        }
        // Create a virtual rep room for the agent clone
        repRoom = {
          id: requestData.rep_room_id,
          title: 'Agent Clone Voice Session',
          settings: { behavior: { voice_input_enabled: true } },
          user_agent_clone_id: agentCloneData.id,
          public_slug: null, // Don't hardcode to t1 - this was causing the bug!
          is_enabled: true
        }
      } else if (repRoomQueryError) {
        throw repRoomQueryError
      } else {
        repRoom = repRoomData
      }
    } else {
      // Non-UUID format - treat as public slug (like "t1")
      console.log('Non-UUID format detected, treating as public slug:', requestData.rep_room_id)
      
      // TEMPORARY WORKAROUND: For "t1", try the known rep room ID first
      if (requestData.rep_room_id === 't1') {
        console.log('🔧 Temporary workaround: Looking for known t1 rep room...')
        const { data: knownRepRoom, error: knownError } = await supabaseServiceClient
          .from('rep_rooms')
          .select('id, title, settings, user_agent_clone_id, public_slug, is_enabled')
          .eq('id', '5225851a-0349-497d-9398-9f42f811a93d')
          .single()
        
        if (!knownError && knownRepRoom) {
          console.log('✅ Found t1 rep room by known ID:', knownRepRoom)
          repRoom = knownRepRoom
          // Skip the rest of the lookup logic
        } else {
          console.log('❌ Known t1 rep room not found, falling back to public_slug lookup')
          // Continue with normal public_slug lookup
        }
      }
      
      // Only do public_slug lookup if we haven't found the room yet
      if (!repRoom) {
        // Query rep_rooms table by public_slug to find the corresponding rep room
        const { data: repRoomBySlug, error: repRoomSlugError } = await supabaseServiceClient
        .from('rep_rooms')
        .select('id, title, settings, user_agent_clone_id, public_slug, is_enabled')
        .eq('public_slug', requestData.rep_room_id)
        .eq('is_enabled', true)
        .single()

        if (repRoomSlugError || !repRoomBySlug) {
          console.error('Rep room not found by public slug:', requestData.rep_room_id, repRoomSlugError)
          
          // FALLBACK: Try to find by title or other identifier if public_slug fails
          console.log('🔍 Fallback: Searching for rep room by title matching slug...')
          const { data: repRoomByTitle, error: titleError } = await supabaseServiceClient
            .from('rep_rooms')
            .select('id, title, settings, user_agent_clone_id, public_slug, is_enabled')
            .ilike('title', `%${requestData.rep_room_id}%`)
            .eq('is_enabled', true)
            .single()
          
          if (titleError || !repRoomByTitle) {
            // Additional debugging: Try to query all rep rooms for diagnostics
            console.log('🔍 Final fallback: Searching for any enabled rep rooms...')
            const { data: allPublicRooms, error: debugError } = await supabaseServiceClient
              .from('rep_rooms')
              .select('id, title, public_slug, is_enabled, user_agent_clone_id')
              .eq('is_enabled', true)
              .limit(10)
            
            console.log('Available rep rooms:', allPublicRooms)
            
            return new Response(
              JSON.stringify({
                success: false,
                error: {
                  code: 'NOT_FOUND',
                  message: 'Rep room not found for slug: ' + requestData.rep_room_id,
                  details: repRoomSlugError?.message,
                  debug: {
                    searchedSlug: requestData.rep_room_id,
                    availableRooms: allPublicRooms?.map(r => ({ slug: r.public_slug, id: r.id, title: r.title, agentCloneId: r.user_agent_clone_id })) || []
                  }
                },
              } as LiveKitTokenResponse),
              {
                status: 404,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              }
            )
          } else {
            console.log('✅ Found rep room by title fallback:', repRoomByTitle)
            repRoom = repRoomByTitle
          }
        } else {
          console.log('✅ Found rep room by public slug:', repRoomBySlug)
          repRoom = repRoomBySlug
        }
      }
      console.log('✅ Found rep room by public slug:', {
        slug: requestData.rep_room_id,
        repRoomId: repRoom.id,
        agentCloneId: repRoom.user_agent_clone_id,
        publicSlug: repRoom.public_slug
      })
    }

    // Check if this is a public rep room (has public_slug and is enabled)
    const isPublicRepRoom = !!(repRoom.public_slug && repRoom.is_enabled)
    console.log('Rep room access check:', {
      repRoomId: repRoom.id,
      isPublicRepRoom,
      hasUser: !!user,
      publicSlug: repRoom.public_slug,
      isEnabled: repRoom.is_enabled
    })

    // Check if voice is enabled
    const voiceEnabled = repRoom.settings?.behavior?.voice_input_enabled || false;
    if (!voiceEnabled) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'VOICE_DISABLED',
            message: 'Voice is not enabled for this rep room',
          },
        } as LiveKitTokenResponse),
        {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Choose which client to use based on rep room type
    const dbClient = isPublicRepRoom ? supabaseServiceClient : supabaseClient

    // Get organization ID through user_agent_clone - use service client for public rep rooms
    const { data: agentClone, error: agentCloneError } = await dbClient
      .from('user_agent_clones')
      .select('id, user_id')
      .eq('id', repRoom.user_agent_clone_id)
      .single()

    if (agentCloneError || !agentClone) {
      console.error('Agent clone query error:', agentCloneError);
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Rep room agent clone not found',
          },
        } as LiveKitTokenResponse),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Get user organization - use service client for public rep rooms
    const { data: userInfo, error: userInfoError } = await dbClient
      .from('users')
      .select('id, organization_id, email, first_name, last_name')
      .eq('id', agentClone.user_id)
      .single()

    if (userInfoError || !userInfo?.organization_id) {
      console.error('User info query error:', userInfoError);
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Rep room owner organization not found',
          },
        } as LiveKitTokenResponse),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    const repRoomOrgId = userInfo.organization_id;

    // Handle user access verification based on rep room type
    let userOrg: any = null
    let participantName: string
    let participantId: string

    if (isPublicRepRoom) {
      // For public rep rooms, allow anonymous access
      if (user) {
        // Authenticated user accessing public rep room
        const { data: userData, error: userOrgError } = await supabaseClient
          .from('users')
          .select('organization_id, email, first_name, last_name')
          .eq('id', user.id)
          .single()

        if (!userOrgError) {
          userOrg = userData
          participantName = requestData.participant_name ||
            `${userOrg.first_name || 'User'} ${userOrg.last_name || ''}`.trim() ||
            userOrg.email || 'Anonymous'
          participantId = `user-${user.id}-${Date.now()}`
        } else {
          // Fallback to anonymous if user data fetch fails
          participantName = requestData.participant_name || 'Anonymous Visitor'
          participantId = `anonymous-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        }
      } else {
        // Anonymous user accessing public rep room
        participantName = requestData.participant_name || 'Anonymous Visitor'
        participantId = `anonymous-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }
      
      console.log('Public rep room access granted:', {
        hasUser: !!user,
        participantName,
        participantId
      })
    } else {
      // For private rep rooms, require authentication and organization access
      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'Authentication required for private rep room',
            },
          } as LiveKitTokenResponse),
          {
            status: 401,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      const { data: userData, error: userOrgError } = await supabaseClient
        .from('users')
        .select('organization_id, email, first_name, last_name')
        .eq('id', user.id)
        .single()

      if (userOrgError || userData.organization_id !== repRoomOrgId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'FORBIDDEN',
              message: 'Access denied to this rep room',
            },
          } as LiveKitTokenResponse),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      userOrg = userData
      participantName = requestData.participant_name ||
        `${userOrg.first_name || 'User'} ${userOrg.last_name || ''}`.trim() ||
        userOrg.email || 'Anonymous'
      participantId = `user-${user.id}-${Date.now()}`
    }

    // Handle session ID from URL parameter or generate new one
    let sessionId: string
    let isInitiator = false
    
    if (requestData.session_id_from_url) {
      // Use existing session ID from URL
      sessionId = requestData.session_id_from_url
      console.log('Using existing session ID from URL:', sessionId)
    } else if (requestData.session_id) {
      // Backward compatibility: use session_id field
      sessionId = requestData.session_id
      console.log('Using session ID from request:', sessionId)
    } else {
      // Generate new session ID and mark as initiator
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      isInitiator = true
      console.log('Generated new session ID:', sessionId)
    }

    // Validate session ID format (should be UUID-like or session-* format)
    const sessionIdRegex = /^(session-\d+-[a-z0-9]+|[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12})$/i
    if (!sessionIdRegex.test(sessionId)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_SESSION_ID',
            message: 'Invalid session ID format',
          },
        } as LiveKitTokenResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Get rep room slug for room naming
    const repRoomSlug = repRoom.public_slug || requestData.rep_room_id

    // NEW: Use rrs-{slug}-{sessionId} room naming convention for Rep Room Sessions
    const roomName = `rrs-${repRoomSlug}-${sessionId}`

    // Get LiveKit configuration
    const livekitUrl = Deno.env.get('LIVEKIT_URL') || 'wss://your-livekit-server.com'
    const livekitApiKey = Deno.env.get('LIVEKIT_API_KEY')
    const livekitApiSecret = Deno.env.get('LIVEKIT_API_SECRET')

    if (!livekitApiKey || !livekitApiSecret) {
      console.error('LiveKit credentials not configured')
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'CONFIGURATION_ERROR',
            message: 'Voice service not properly configured',
          },
        } as LiveKitTokenResponse),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Get agent configuration from the actual agent table (BEFORE ROOM CREATION)
    let agentConfig = null
    try {
      // First get the full agent clone data including tenant_activation_id
      const { data: fullAgentClone, error: cloneError } = await supabaseServiceClient
        .from('user_agent_clones')
        .select('tenant_activation_id')
        .eq('id', agentClone.id)
        .single()
      
      if (cloneError || !fullAgentClone?.tenant_activation_id) {
        console.error('Error getting agent clone tenant_activation_id:', cloneError)
      } else {
        // Get the tenant activation to find the agent_id
        const { data: activation, error: activationError } = await supabaseServiceClient
          .from('tenant_agent_activations')
          .select('agent_id')
          .eq('id', fullAgentClone.tenant_activation_id)
          .single()
        
        if (activationError || !activation?.agent_id) {
          console.error('Error getting tenant activation agent_id:', activationError)
        } else {
          // Get the actual agent configuration
          const { data: agent, error: agentError } = await supabaseServiceClient
            .from('agents')
            .select('*')
            .eq('id', activation.agent_id)
            .single()
          
          if (agentError || !agent) {
            console.error('Error getting agent configuration:', agentError)
          } else {
            // Build the complete agent config object
            agentConfig = {
              id: agent.id,
              name: agent.name,
              type: agent.type,
              mastra_api_base_url: agent.mastra_api_base_url,
              mastra_agent_id: agent.mastra_agent_id || agent.id,
              configuration: agent.configuration,
              copilotkit_endpoint: agent.configuration?.copilotkit?.endpoint || agent.mastra_api_base_url,
              // Include other relevant config fields
              description: agent.description,
              version: agent.version,
              status: agent.status
            }
            
            console.log('✅ Retrieved agent configuration:', {
              agentId: agent.id,
              name: agent.name,
              type: agent.type,
              mastraBaseUrl: agent.mastra_api_base_url,
              mastraAgentId: agent.mastra_agent_id,
              copilotKitEndpoint: agent.configuration?.copilotkit?.endpoint,
              hasFullConfig: !!agentConfig
            })
            
            console.log('🔗 Agent chain resolution successful:', {
              repRoomId: repRoom.id,
              agentCloneId: agentClone.id,
              tenantActivationId: fullAgentClone.tenant_activation_id,
              agentId: activation.agent_id,
              mastraUrl: agent.mastra_api_base_url,
              mastraAgentId: agent.mastra_agent_id
            })
          }
        }
      }
    } catch (error) {
      console.error('Error fetching agent config:', error)
    }

    // Manual agent dispatch - Create room with agent metadata to trigger agent join
    try {
      console.log(`🤖 Dispatching agent to room: ${roomName}`)
      
      // Import LiveKit RoomServiceClient for manual agent dispatch
      const { RoomServiceClient } = await import('https://esm.sh/livekit-server-sdk@2.6.1')
      
      const roomService = new RoomServiceClient(livekitUrl, livekitApiKey, livekitApiSecret)
      
      // Create room with comprehensive agent metadata (wait for external agent)
      const roomMetadata: LiveKitRoomMetadata = {
        // Operational flags
        agent_required: true,
        auto_dispatch_agent: false, // Let Fly.io agent connect instead of auto-dispatch
        
        // Core identifiers
        rep_room_id: repRoom.id, // Use actual rep room UUID
        agent_clone_id: repRoom.user_agent_clone_id,
        organization_id: repRoomOrgId,
        
        // User and session context
        user_id: user?.id || null,
        session_id: sessionId,
        participant_id: participantId,
        participant_name: participantName,
        
        // Agent configuration
        agent_config: agentConfig,
        
        // Access control metadata
        is_public_access: isPublicRepRoom,
        is_anonymous: !user,
        
        // Debugging metadata
        metadata_created_at: new Date().toISOString(),
        room_creation_context: 'voice_token_function'
      }

      // Try to create room, if it exists, update its metadata
      try {
        await roomService.createRoom({
          name: roomName,
          emptyTimeout: 300, // 5 minutes
          maxParticipants: 10,
          metadata: JSON.stringify(roomMetadata)
        })
        console.log(`✅ Room created with agent dispatch: ${roomName}`)
      } catch (createError) {
        console.log(`🔄 Room already exists, updating metadata: ${roomName}`)
        // Room exists, update its metadata instead
        await roomService.updateRoomMetadata(roomName, JSON.stringify(roomMetadata))
        console.log(`✅ Room metadata updated: ${roomName}`)
      }
      
      console.log('🔍 Room metadata includes agent config:', {
        hasAgentConfig: !!roomMetadata.agent_config,
        agentConfigKeys: roomMetadata.agent_config ? Object.keys(roomMetadata.agent_config) : [],
        mastraBaseUrl: roomMetadata.agent_config?.mastra_api_base_url,
        agentCloneId: roomMetadata.agent_clone_id
      })
      
    } catch (roomError) {
      console.error('❌ Failed to create/update room metadata:', roomError.message)
      // Don't fail token generation, but ensure we log the error
    }

    // Generate LiveKit token with proper JWT signing and correct structure
    const currentTime = Math.floor(Date.now() / 1000)
    const tokenPayload = {
      iss: livekitApiKey,
      sub: participantId,
      iat: currentTime,
      exp: currentTime + (4 * 60 * 60), // 4 hour expiry (extended from 1 hour)
      video: {
        room: roomName,
        roomJoin: true,
        canPublish: true,
        canSubscribe: true,
        canPublishData: true,
        hidden: false,
        recorder: false,
      },
      metadata: JSON.stringify({
        user_id: user?.id || null,
        rep_room_id: repRoom.id,
        rep_room_slug: repRoomSlug,
        session_id: sessionId,
        agent_clone_id: repRoom.user_agent_clone_id,
        organization_id: repRoomOrgId,
        is_public_access: isPublicRepRoom,
        is_anonymous: !user,
        is_public_room: isPublicRepRoom,
        is_initiator: isInitiator,
        full_agent_config: agentConfig,
        ...requestData.participant_metadata,
      }),
    }

    // Log token timing for debugging
    console.log('Token timing:', {
      currentTime,
      issuedAt: tokenPayload.iat,
      expiresAt: tokenPayload.exp,
      validFor: `${(tokenPayload.exp - tokenPayload.iat) / 3600} hours`,
      currentTimeISO: new Date(currentTime * 1000).toISOString(),
      expiresAtISO: new Date(tokenPayload.exp * 1000).toISOString()
    })

    // Create proper JWT token using HMAC-SHA256 signing
    const token = await createLiveKitJWT(tokenPayload, livekitApiSecret)
    const expiresAt = new Date(Date.now() + (4 * 60 * 60 * 1000)).toISOString() // 4 hours to match token expiry

    // Create voice session record in database
    let voiceSession = null
    try {
      // Gather browser metadata if available from request headers
      const userAgent = req.headers.get('User-Agent') || 'Unknown'
      const metadata = {
        user_agent: userAgent,
        participant_name: participantName,
        is_public_access: isPublicRepRoom,
        is_anonymous: !user,
        is_public_room: isPublicRepRoom,
        agent_clone_id: agentClone.id,
        organization_id: repRoomOrgId,
        livekit_room_name: roomName,
        participant_id: participantId,
        ...requestData.participant_metadata
      }

      const { data: sessionData, error: sessionError } = await supabaseServiceClient
        .from('voice_sessions')
        .insert({
          room_id: repRoom.id,
          user_id: user?.id || '00000000-0000-0000-0000-000000000000', // Use anonymous UUID if no user
          session_id: sessionId,
          livekit_token: token,
          started_at: new Date().toISOString(),
          metadata
        })
        .select()
        .single()

      if (sessionError) {
        console.error('Failed to create voice session record:', sessionError)
        // Log to audit log but continue - don't block token generation
        await supabaseServiceClient
          .from('voice_audit_log')
          .insert({
            user_id: user?.id,
            event_type: 'voice_session_create_failed',
            event_data: {
              error: sessionError.message,
              room_id: repRoom.id,
              session_id: sessionId
            }
          })
      } else {
        voiceSession = sessionData
        console.log('✅ Voice session created:', sessionId)
        
        // Log successful session creation
        await supabaseServiceClient
          .from('voice_audit_log')
          .insert({
            session_id: sessionId,
            user_id: user?.id,
            event_type: 'voice_session_created',
            event_data: {
              room_id: repRoom.id,
              participant_id: participantId
            }
          })
      }
    } catch (error) {
      console.error('Error creating voice session:', error)
      // Continue without session tracking
    }

    // Dispatch agent via agent-lifecycle function (NEW: Direct LiveKit integration)
    const agentLifecycleUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/agent-lifecycle/dispatch/${roomName}`
    const agentAuthHeader = req.headers.get('Authorization')

    try {
      console.log(`🤖 Dispatching agent via agent-lifecycle for room: ${roomName}`)
      
      const agentDispatchPayload = {
        roomId: repRoom.id, // Pass the actual room UUID
        userId: user?.id || null,
        sessionId: sessionId,
        agentCloneId: agentClone.id,
        organizationId: repRoomOrgId,
        agentConfig: agentConfig,
        // CRITICAL FIX: Pass the complete room metadata as job metadata for the Python agent
        jobMetadata: {
          agentConfig: agentConfig,
          context: {
            repRoomId: repRoom.id,
            userId: user?.id || null,
            sessionId: sessionId,
            agentCloneId: agentClone.id,
            organizationId: repRoomOrgId,
            tenantId: repRoomOrgId,
            repRoomSlug: requestData.rep_room_id,
            userJwtToken: '',
            threadId: `room-${roomName}-${Date.now()}`,
            participantId,
            participantName,
            isPublicAccess: isPublicRepRoom,
            isAnonymous: !user,
            // Include all room metadata fields
            rep_room_id: repRoom.id,
            agent_clone_id: agentClone.id,
            organization_id: repRoomOrgId,
            user_id: user?.id || null,
            session_id: sessionId,
            participant_id: participantId,
            participant_name: participantName,
            is_public_access: isPublicRepRoom,
            is_anonymous: !user,
            agent_config: agentConfig
          }
        },
        metadata: {
          participantId,
          participantName,
          isPublicAccess: isPublicRepRoom,
          isAnonymous: !user
        }
      }
      
      const response = await fetch(agentLifecycleUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader || '',
          'apikey': Deno.env.get('SUPABASE_ANON_KEY') || ''
        },
        body: JSON.stringify(agentDispatchPayload)
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Failed to dispatch agent via agent-lifecycle:', response.status, errorText)
        
        // Log agent dispatch failure
        await supabaseServiceClient
          .from('voice_audit_log')
          .insert({
            session_id: sessionId,
            user_id: user?.id,
            event_type: 'agent_dispatch_failed',
            event_data: {
              status: response.status,
              error: errorText,
              method: 'agent-lifecycle'
            }
          })
      } else {
        const result = await response.json()
        console.log('✅ Successfully dispatched agent via agent-lifecycle:', result)
        
        // Log successful agent dispatch
        await supabaseServiceClient
          .from('voice_audit_log')
          .insert({
            session_id: sessionId,
            user_id: user?.id,
            event_type: 'agent_dispatched',
            event_data: {
              response: result,
              method: 'agent-lifecycle'
            }
          })
      }
    } catch (err) {
      console.error('❌ Error dispatching agent via agent-lifecycle (non-blocking):', err)
      // Log error but continue
      await supabaseServiceClient
        .from('voice_audit_log')
        .insert({
          session_id: sessionId,
          user_id: user?.id,
          event_type: 'agent_dispatch_error',
          event_data: {
            error: err.message,
            method: 'agent-lifecycle'
          }
        })
    }

    // Get Deepgram API key for STT
    const deepgramApiKey = Deno.env.get('DEEPGRAM_API_KEY')
    
    // Return the token and session details
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          token: token,
          room_name: roomName,
          participant_id: participantId,
          expires_at: expiresAt,
          livekit_url: livekitUrl,
          deepgram_api_key: deepgramApiKey, // Add Deepgram API key for frontend STT
          session_id: sessionId, // Include session ID for frontend tracking
        },
      } as LiveKitTokenResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('LiveKit token generation error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          details: error.message,
        },
      } as LiveKitTokenResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})