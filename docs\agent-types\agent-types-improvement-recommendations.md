# Agent Types Improvement Recommendations

## Overview

After analyzing the Agent Runtime Context and Dynamic Agents documentation, this document provides recommendations for enhancing our agent types/blueprints system while maintaining simplicity and avoiding overcomplication.

## Key Insights from Mastra Documentation

### 1. Runtime Context Power
- **Dynamic Behavior**: Agents can modify instructions, models, and tools based on runtime variables
- **Type Safety**: Runtime context uses TypeScript types for safe variable access
- **Dependency Injection**: Clean separation between static configuration and dynamic behavior
- **Single Agent, Multiple Behaviors**: One agent can handle different user tiers, languages, and scenarios

### 2. Dynamic Agent Capabilities
- **Conditional Instructions**: Instructions that change based on user context
- **Model Selection**: Different models for different user tiers (e.g., GPT-4 for enterprise)
- **Tool Selection**: Dynamic tool availability based on user permissions
- **Middleware Integration**: Runtime context set at server middleware level

## Recommended Improvements

### 1. Enhanced Runtime Context Schema

**Current State**: Basic runtime context schema in agent types
**Improvement**: Expand to support dynamic agent patterns

```json
{
  "type": "object",
  "title": "Enhanced Runtime Context Schema",
  "properties": {
    "user": {
      "type": "object",
      "properties": {
        "id": { "type": "string" },
        "tier": { "enum": ["free", "pro", "enterprise"] },
        "language": { "enum": ["en", "es", "fr", "de"] },
        "permissions": { "type": "array", "items": { "type": "string" } }
      }
    },
    "organization": {
      "type": "object",
      "properties": {
        "id": { "type": "string" },
        "plan": { "enum": ["basic", "professional", "enterprise"] },
        "features": { "type": "array", "items": { "type": "string" } }
      }
    },
    "session": {
      "type": "object",
      "properties": {
        "id": { "type": "string" },
        "channel": { "enum": ["website", "phone", "reproom"] },
        "context": { "type": "object" }
      }
    }
  }
}
```

### 2. Dynamic Configuration Support

**New Field**: `dynamic_config_schema`
**Purpose**: Define how agent behavior changes based on runtime context

```json
{
  "type": "object",
  "title": "Dynamic Configuration Schema",
  "properties": {
    "instructions": {
      "type": "object",
      "properties": {
        "template": { "type": "string" },
        "variables": { "type": "array", "items": { "type": "string" } }
      }
    },
    "model_selection": {
      "type": "object",
      "properties": {
        "rules": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "condition": { "type": "string" },
              "model": { "type": "string" }
            }
          }
        }
      }
    },
    "tool_selection": {
      "type": "object",
      "properties": {
        "base_tools": { "type": "array", "items": { "type": "string" } },
        "conditional_tools": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "condition": { "type": "string" },
              "tools": { "type": "array", "items": { "type": "string" } }
            }
          }
        }
      }
    }
  }
}
```

### 3. Simplified Agent Type Categories

**Current**: Generic category field
**Improvement**: Structured categories with behavior implications

```typescript
type AgentCategory = {
  primary: "support" | "sales" | "analytics" | "content" | "workflow";
  behavior: "static" | "dynamic" | "adaptive";
  complexity: "simple" | "moderate" | "advanced";
}
```

### 4. Template-Based Instructions

**Enhancement**: Support for dynamic instruction templates

```json
{
  "instruction_template": {
    "base": "You are a {{agent_role}} for {{organization.name}}.",
    "user_tier_additions": {
      "free": "Provide basic support and documentation links.",
      "pro": "Offer detailed technical support and best practices.",
      "enterprise": "Provide priority support with custom solutions."
    },
    "language_support": {
      "enabled": true,
      "fallback": "en"
    }
  }
}
```

## Implementation Strategy (Keeping It Simple)

### Phase 1: Foundation (No Breaking Changes)
1. **Add Optional Fields**: Introduce new schema fields as optional
2. **Backward Compatibility**: Ensure existing agent types continue working
3. **Documentation**: Update guide with new capabilities

### Phase 2: Enhanced Defaults
1. **Smart Defaults**: Provide sensible defaults for dynamic behavior
2. **Template Library**: Create common instruction templates
3. **Validation**: Add schema validation for new fields

### Phase 3: Advanced Features
1. **Runtime Testing**: Tools for testing dynamic behavior
2. **Performance Monitoring**: Track dynamic agent performance
3. **Migration Tools**: Help upgrade existing agent types

## Specific Recommendations

### 1. Keep Current Structure, Add Power

**Don't Change**: Basic Info, Presentation Configuration, Technical Parameters
**Enhance**: Configuration schemas with dynamic capabilities
**Add**: Template support for instructions and responses

### 2. Introduce Agent Behavior Modes

```typescript
enum AgentBehaviorMode {
  STATIC = "static",           // Traditional fixed behavior
  DYNAMIC = "dynamic",         // Runtime context-driven
  ADAPTIVE = "adaptive"        // Learning and evolving
}
```

### 3. Simplified Dynamic Configuration

Instead of complex rule engines, use simple condition-value pairs:

```json
{
  "dynamic_instructions": {
    "user.tier === 'enterprise'": "Provide premium support with dedicated resources.",
    "user.language === 'es'": "Responde siempre en español.",
    "organization.plan === 'basic'": "Focus on essential features and documentation."
  }
}
```

### 4. Enhanced Default Values with Context

```json
{
  "contextual_defaults": {
    "model": {
      "default": "gpt-3.5-turbo",
      "enterprise": "gpt-4",
      "high_complexity": "gpt-4"
    },
    "temperature": {
      "default": 0.7,
      "support": 0.3,
      "creative": 0.9
    }
  }
}
```

## Benefits of These Improvements

### 1. Reduced Agent Proliferation
- One agent type can handle multiple user tiers
- Fewer duplicated configurations
- Easier maintenance and updates

### 2. Better User Experience
- Personalized responses based on user context
- Appropriate model selection for user tier
- Language-aware interactions

### 3. Operational Efficiency
- Dynamic resource allocation
- Cost optimization through smart model selection
- Simplified deployment and management

### 4. Developer Experience
- Type-safe runtime context
- Clear separation of static vs dynamic config
- Powerful yet simple configuration options

## Migration Path

### For Existing Agent Types
1. **No Immediate Changes Required**: Current agent types continue working
2. **Gradual Enhancement**: Add dynamic capabilities as needed
3. **Template Migration**: Convert static instructions to templates when beneficial

### For New Agent Types
1. **Start with Templates**: Use instruction templates from day one
2. **Plan for Growth**: Consider future dynamic needs in initial design
3. **Leverage Patterns**: Use established patterns for common scenarios

## Avoiding Overcomplication

### Keep Simple Things Simple
- **Basic agents remain basic**: No forced complexity
- **Progressive enhancement**: Add features only when needed
- **Clear defaults**: Sensible behavior without configuration

### Clear Boundaries
- **Static vs Dynamic**: Clear distinction between fixed and variable behavior
- **Template vs Logic**: Use templates for text, logic for behavior
- **Configuration vs Code**: Keep business logic in configuration, not code

### Documentation Strategy
- **Layered Learning**: Basic → Intermediate → Advanced
- **Use Cases First**: Show problems before solutions
- **Working Examples**: Complete, runnable examples for each pattern

## Conclusion

These improvements leverage Mastra's runtime context and dynamic agent capabilities while maintaining the simplicity and usability of our current agent types system. The key is to add power without adding complexity, providing clear upgrade paths for existing implementations while enabling sophisticated new use cases.

The focus should be on:
1. **Template-driven instructions** for dynamic behavior
2. **Context-aware defaults** for smart resource allocation
3. **Simple condition syntax** for dynamic configuration
4. **Backward compatibility** to protect existing investments
5. **Progressive enhancement** to grow with user needs

This approach ensures that junior developers can still create simple, static agents while power users can leverage advanced dynamic capabilities when needed.