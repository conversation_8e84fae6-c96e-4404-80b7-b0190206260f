import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { MainContextProvider } from '../contexts/rroom/MainContextProvider';
import { RepRoomInterfaceEnhanced, createEnhancedDemoRepRoomState } from '../components/rep-room/RepRoomInterfaceEnhanced';
import { RepRoomState } from '../types/rep-room';

// Configuration interface matching the existing system
interface RepRoomConfig {
  repRoom: {
    id: string;
    title: string;
    settings: {
      appearance: {
        logoUrl?: string;
        primaryColor?: string;
        backgroundColor?: string;
      };
      behavior: {
        greeting_message: string;
        conversation_starters?: string[];
      };
      voice: {
        enabled: boolean;
        provider: string;
        sttProvider?: string;
        ttsProvider?: string;
        voiceId?: string;
        settings: Record<string, unknown>;
      };
      deployment: {
        password_protection_enabled: boolean;
        password_hash?: string;
      };
    };
  };
  agent: {
    id: string;
    name: string;
    mastraApiBaseUrl: string;
    mastraAgentId: string;
    cloneId: string;
    cloneName: string;
  };
}

// Loading spinner component
const LoadingSpinner: React.FC<{ message?: string }> = ({ message = "Loading Rep Room..." }) => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
      <p className="text-gray-600">{message}</p>
    </div>
  </div>
);

// Not found component
const NotFoundPage: React.FC<{ error?: string; onRetry?: () => void }> = ({ error, onRetry }) => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
    <div className="text-center max-w-md">
      <div className="text-red-600 mb-4">
        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">Rep Room Configuration Issue</h2>
      <p className="text-gray-600 mb-6">{error || 'The requested rep room could not be loaded.'}</p>
      <div className="space-y-3">
        {onRetry && (
          <button
            onClick={onRetry}
            className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Demo Mode
          </button>
        )}
        <button
          onClick={() => window.history.back()}
          className="w-full bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
        >
          Go Back
        </button>
      </div>
    </div>
  </div>
);

export const RepRoomPageEnhanced: React.FC = () => {
  const { slug, sessionId } = useParams<{
    slug: string;
    sessionId?: string;
  }>();
  const navigate = useNavigate();
  
  // State management
  const [config, setConfig] = useState<RepRoomConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useDemoMode, setUseDemoMode] = useState(false);
  
  // Rep Room UI state
  const [repRoomState, setRepRoomState] = useState<RepRoomState | null>(null);
  
  // LiveKit state management
  const [liveKitToken, setLiveKitToken] = useState<string | null>(null);
  const [liveKitUrl, setLiveKitUrl] = useState<string | null>(null);
  const [roomName, setRoomName] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [agentsDispatched, setAgentsDispatched] = useState(false);
  const [isReadyToConnect, setIsReadyToConnect] = useState(false);

  // Effect to manage persistent session URL
  useEffect(() => {
    if (slug && !sessionId) {
      const newSessionId = `session-${crypto.randomUUID()}`;
      console.log('[RepRoomPageEnhanced] No session ID found, generating new one:', newSessionId);
      navigate(`/rroom-v2/${slug}/${newSessionId}`, { replace: true });
    }
  }, [slug, sessionId, navigate]);

  // Create demo configuration
  const createDemoConfig = (): RepRoomConfig => ({
    repRoom: {
      id: slug || 'demo-room',
      title: `Demo Rep Room: ${slug}`,
      settings: {
        appearance: {
          logoUrl: '',
          primaryColor: '#3B82F6',
          backgroundColor: '#F8FAFC'
        },
        behavior: {
          greeting_message: 'Welcome to the enhanced Rep Room demo! This interface features LiveKit voice integration and CopilotKit AI assistance.',
          conversation_starters: [
            'Tell me about SEO optimization',
            'How can I improve my content strategy?',
            'What are the latest digital marketing trends?'
          ]
        },
        voice: {
          enabled: true,
          provider: 'livekit',
          settings: {}
        },
        deployment: {
          password_protection_enabled: false
        }
      }
    },
    agent: {
      id: 'demo-agent',
      name: 'Demo AI Assistant',
      mastraApiBaseUrl: '',
      mastraAgentId: 'demo',
      cloneId: 'demo-clone',
      cloneName: 'Demo AI Assistant'
    }
  });

  // Effect to fetch the room's configuration or use demo mode
  useEffect(() => {
    if (!slug) {
      setError('No room slug provided');
      setIsLoading(false);
      return;
    }

    const fetchConfig = async () => {
      try {
        console.log('[RepRoomPageEnhanced] Fetching configuration for slug:', slug);
        setIsLoading(true);
        setError(null);

        if (useDemoMode) {
          // Use demo configuration
          const demoConfig = createDemoConfig();
          setConfig(demoConfig);
          
          const demoState = createEnhancedDemoRepRoomState(demoConfig.agent.name);
          const initialState: RepRoomState = {
            activeAgent: null,
            presentationContent: {
              type: 'default',
              title: `Welcome to ${demoConfig.repRoom.title}`,
              data: demoConfig.repRoom.settings.behavior.greeting_message,
              loading: false
            },
            messages: [],
            isVoiceActive: false,
            participants: {
              mainAgent: {
                id: demoConfig.agent.id,
                name: demoConfig.agent.name,
                avatar: '',
                type: 'main',
                status: 'ready'
              },
              specialists: demoState.participants?.specialists || [],
              humans: []
            },
            sessionInfo: {
              title: demoConfig.repRoom.title,
              sessionId: sessionId || `session-${Date.now()}`,
              slug: slug
            }
          };
          
          setRepRoomState(initialState);
          setIsLoading(false);
          return;
        }

        const response = await fetch(`/api/public-rep-room-config?slug=${slug}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Rep Room not found - would you like to try demo mode?');
          }
          throw new Error(`Failed to fetch configuration: ${response.status}`);
        }

        const data = await response.json();
        console.log('[RepRoomPageEnhanced] Configuration loaded:', data);
        
        // Validate required fields
        if (!data.repRoom || !data.agent) {
          throw new Error('Invalid configuration format');
        }

        setConfig(data);
        
        // Initialize Rep Room state with config data
        const demoState = createEnhancedDemoRepRoomState(data.agent.name || data.agent.cloneName);
        const initialState: RepRoomState = {
          activeAgent: null,
          presentationContent: {
            type: 'default',
            title: `Welcome to ${data.repRoom.title}`,
            data: data.repRoom.settings.behavior.greeting_message || 'Welcome to the Rep Room!',
            loading: false
          },
          messages: [],
          isVoiceActive: false,
          participants: {
            mainAgent: {
              id: data.agent.id,
              name: data.agent.name || data.agent.cloneName,
              avatar: '',
              type: 'main',
              status: 'ready'
            },
            specialists: demoState.participants?.specialists || [],
            humans: []
          },
          sessionInfo: {
            title: data.repRoom.title,
            sessionId: sessionId || `session-${Date.now()}`,
            slug: slug
          }
        };
        
        setRepRoomState(initialState);
      } catch (err) {
        console.error('[RepRoomPageEnhanced] Failed to fetch configuration:', err);
        setError(err instanceof Error ? err.message : 'Failed to load Rep Room');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfig();
  }, [slug, sessionId, useDemoMode]);

  // Effect to fetch LiveKit credentials when config is loaded
  useEffect(() => {
    if (!config?.repRoom?.id || !slug) return;

    const fetchLiveKitCredentials = async () => {
      setIsConnecting(true);
      setError(null);
      
      try {
        // In demo mode, skip LiveKit token fetching and use mock credentials
        if (useDemoMode) {
          console.log('[RepRoomPageEnhanced] Demo mode: Using mock LiveKit credentials');
          
          // Set mock credentials for demo mode
          setLiveKitToken('demo-token');
          setLiveKitUrl('wss://demo.livekit.io');
          setRoomName(`demo-room-${slug}-${Date.now()}`);
          
          console.log('[RepRoomPageEnhanced] Demo mode credentials set');
          setIsConnecting(false);
          return;
        }
        
        console.log('[RepRoomPageEnhanced] Requesting LiveKit token for rep room:', config.repRoom.id);
        
        // Use the Supabase edge function that properly handles agent configuration metadata
        const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://kjkehonxatogcwrybslr.supabase.co';
        const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtqa2Vob254YXRvZ2N3cnlic2xyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5OTQ4NzEsImV4cCI6MjA1MDU3MDg3MX0.f0af84a4a9ea2dc369768aa4de9ba5d1c7c54231';
        
        const response = await fetch(`${supabaseUrl}/functions/v1/rep-room-voice-token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseAnonKey}`,
          },
          body: JSON.stringify({
            rep_room_id: slug, // Use slug as rep_room_id since it gets resolved by public_slug
            participant_name: 'Anonymous Visitor'
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to get voice token: ${response.status}`);
        }

        const responseData = await response.json();
        
        // Handle Supabase edge function response format: { success: true, data: { ... } }
        const tokenData = responseData.success ? responseData.data : responseData;
        
        console.log('[RepRoomPageEnhanced] 🔍 FULL Voice Token Response:', responseData);
        console.log('[RepRoomPageEnhanced] 🔍 Token Data:', tokenData);
        console.log('[RepRoomPageEnhanced] LiveKit credentials received:', {
          hasToken: !!tokenData?.token,
          hasUrl: !!tokenData?.livekit_url,
          roomName: tokenData?.room_name,
          sessionId: tokenData?.session_id
        });
        
        setLiveKitToken(tokenData.token);
        setLiveKitUrl(tokenData.livekit_url);
        setRoomName(tokenData.room_name);

        console.log('💾 STORED ROOM NAME FOR AGENT DISPATCH:', tokenData.room_name);
      } catch (error) {
        console.error('[RepRoomPageEnhanced] LiveKit token error:', error);
        
        // In case of error, fall back to demo mode
        console.log('[RepRoomPageEnhanced] Falling back to demo mode due to LiveKit error');
        setUseDemoMode(true);
        
        // Set mock credentials for fallback demo mode
        setLiveKitToken('demo-token');
        setLiveKitUrl('wss://demo.livekit.io');
        setRoomName(`demo-room-${slug}-${Date.now()}`);
      } finally {
        setIsConnecting(false);
      }
    };

    fetchLiveKitCredentials();
  }, [config?.repRoom?.id, slug, useDemoMode]);

  // Function to dispatch agents BEFORE user connects
  const dispatchAgents = async (roomNameToUse: string) => {
    if (!roomNameToUse || !config) {
      console.error('❌ No room name or config available for agent dispatch');
      return false;
    }

    console.log('🚀 PRE-DISPATCHING AGENTS TO ROOM:', roomNameToUse);
    console.log('🚀 DISPATCH REQUEST DETAILS:', {
      roomName: roomNameToUse,
      timestamp: new Date().toISOString()
    });
    
    try {
      // TEMPORARY: Skip frontend agent dispatch due to edge function 500 error
      // Rely on voice token function's built-in agent dispatch instead
      console.log('⚠️ Temporarily skipping frontend agent dispatch due to edge function error');
      console.log('🎯 Relying on voice token function to dispatch agent with metadata');
      
      // Wait a moment for stability
      console.log('⏳ Waiting 1 second for stability...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('✅ Ready for user connection (voice token function will handle agent dispatch)');
      
      return true;
    } catch (dispatchError) {
      console.error('❌ FAILED TO DISPATCH AGENTS:', dispatchError);
      console.error('🚨 This means voice functionality will not work!');
      return false;
    }
  };

  // Enhanced room connection with pre-dispatch
  const connectToRoomWithAgents = async () => {
    if (!roomName) {
      console.error('❌ No room name available for connection');
      return;
    }

    console.log('🎯 STARTING ENHANCED CONNECTION SEQUENCE');
    console.log('📋 Step 1: Pre-dispatching agents to room:', roomName);
    
    const agentDispatchSuccess = await dispatchAgents(roomName);
    
    if (agentDispatchSuccess) {
      console.log('✅ Step 2: Agents dispatched successfully, user ready to connect');
      setAgentsDispatched(true);
      setIsReadyToConnect(true);
    } else {
      console.error('❌ Step 2: Agent dispatch failed, proceeding anyway');
      setIsReadyToConnect(true); // Still allow connection even if agents fail
    }
  };

  // Trigger agent dispatch when room name is available and user starts
  useEffect(() => {
    if (roomName && hasStarted && !agentsDispatched && !isReadyToConnect) {
      console.log('🚀 Room name available, starting enhanced connection sequence');
      connectToRoomWithAgents();
    }
  }, [roomName, hasStarted, agentsDispatched, isReadyToConnect]);

  const handleRepRoomStateChange = (newState: RepRoomState) => {
    setRepRoomState(newState);
    console.log('Rep Room state updated:', newState);
  };

  const handleRetryWithDemo = () => {
    setUseDemoMode(true);
    setError(null);
  };

  // Loading state
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Error state
  if (error && !useDemoMode) {
    return <NotFoundPage error={error} onRetry={handleRetryWithDemo} />;
  }

  if (!config || !repRoomState) {
    return <NotFoundPage error="Configuration not available" onRetry={handleRetryWithDemo} />;
  }

  // Show connecting state while fetching LiveKit credentials
  if (isConnecting || !liveKitToken || !liveKitUrl) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Setting up voice connection...</p>
        </div>
      </div>
    );
  }

  // Show start button before connecting to LiveKit room
  if (!hasStarted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mb-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-3xl">🤖</span>
            </div>
            <h2 className="text-2xl font-bold mb-2 text-gray-900">{config.agent.name}</h2>
            <p className="text-gray-600">Ready for enhanced voice conversation</p>
          </div>

          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              This enhanced rep room features LiveKit voice integration with multi-agent collaboration.
              Your conversation will take place in a secure voice room with real-time audio processing and AI assistance.
            </p>
          </div>

          <button
            onClick={() => setHasStarted(true)}
            className="w-full px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
          >
            Start Enhanced Voice Conversation
          </button>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Show loading while agents are being dispatched
  if (hasStarted && !isReadyToConnect) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Preparing voice agents...</p>
          <p className="text-sm text-gray-500 mt-2">
            {agentsDispatched ? 'Agents ready, connecting...' : 'Dispatching agents to LiveKit room...'}
          </p>
        </div>
      </div>
    );
  }

  // Success state - render the enhanced Rep Room experience
  console.log('[RepRoomPageEnhanced] 🎯 FINAL RENDER - Enhanced Rep Room:', {
    slug,
    sessionId,
    useDemoMode,
    hasLiveKitToken: !!liveKitToken,
    hasLiveKitUrl: !!liveKitUrl,
    roomName,
    agentsDispatched,
    hasStarted,
    isReadyToConnect,
    renderCount: Math.random() // Track if this is a new render
  });
  
  // Create the main interface component
  const MainInterface = (
    <MainContextProvider config={config} sessionId={sessionId!}>
      <div className="h-screen overflow-hidden">
        <RepRoomInterfaceEnhanced
          initialState={repRoomState}
          onStateChange={handleRepRoomStateChange}
          agentName={config.agent.name}
          sessionId={sessionId || 'demo-session'}
          copilotKitRuntimeUrl="/api/copilotkit"
        />
      </div>
    </MainContextProvider>
  );
  
  // In demo mode, render without LiveKit wrapper
  if (useDemoMode) {
    console.log('[RepRoomPageEnhanced] 🎯 DEMO MODE: Rendering without LiveKit integration');
    return MainInterface;
  }
  
  // FIXED: Remove outer LiveKit room wrapper to prevent conflicts
  // The UnifiedVoiceProvider inside MainContextProvider handles LiveKit connectivity
  console.log('[RepRoomPageEnhanced] 🎯 Rendering without outer LiveKit wrapper to prevent conflicts');
  return MainInterface;
};

export default RepRoomPageEnhanced;