import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import '@testing-library/jest-dom';
import { RepRoomSessionPage } from '../../pages/RepRoomSessionPage';

// Mock nanoid to return predictable session IDs
jest.mock('nanoid', () => ({
  nanoid: jest.fn(() => 'test-session-id')
}));

// Mock Supabase client
const mockSupabaseClient = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(() => Promise.resolve({ data: null, error: null }))
      }))
    })),
    insert: jest.fn(() => Promise.resolve({ error: null }))
  }))
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient)
}));

// Mock CopilotKit
jest.mock('@copilotkit/react-core', () => ({
  CopilotKit: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="copilot-kit">{children}</div>
  )
}));

// Mock RepRoomInterfaceEnhanced
jest.mock('../../components/rep-room/RepRoomInterfaceEnhanced', () => ({
  RepRoomInterfaceEnhanced: ({ sessionId }: { sessionId: string }) => (
    <div data-testid="rep-room-interface" data-session-id={sessionId}>
      Rep Room Interface
    </div>
  ),
  createEnhancedDemoRepRoomState: jest.fn(() => ({
    sessionInfo: { title: 'Demo', sessionId: 'test', slug: 'test' }
  }))
}));

// Mock LandingViewV3
jest.mock('../../components/rep-room/LandingViewV3', () => ({
  LandingViewV3: ({ onStart, onTryDemo }: { onStart: () => void; onTryDemo: () => void }) => (
    <div data-testid="landing-view">
      <button onClick={onStart} data-testid="start-button">Start</button>
      <button onClick={onTryDemo} data-testid="demo-button">Try Demo</button>
    </div>
  )
}));

// Mock UnifiedVoiceProvider
jest.mock('../../contexts/rroom/UnifiedVoiceContext', () => ({
  UnifiedVoiceProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="voice-provider">{children}</div>
  )
}));

// Mock ErrorBoundary
jest.mock('../../components/common/ErrorBoundary', () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="error-boundary">{children}</div>
  )
}));

// Mock ConnectionStatusIndicator
jest.mock('../../components/common/ConnectionStatusIndicator', () => ({
  ConnectionStatusIndicator: ({ connectionState }: { connectionState: { status: string } }) => (
    <div data-testid="connection-indicator" data-status={connectionState.status}>
      Connection: {connectionState.status}
    </div>
  )
}));

jest.mock('../../utils/retryMechanism', () => ({
  fetchWithRetry: jest.fn().mockResolvedValue({}),
  retryDatabaseOperation: jest.fn((fn: () => Promise<unknown>) => fn()),
  retryWithExponentialBackoff: jest.fn().mockResolvedValue('success'),
  createCircuitBreaker: jest.fn().mockReturnValue({
    execute: jest.fn().mockResolvedValue('success'),
    getState: jest.fn().mockReturnValue('CLOSED'),
    reset: jest.fn()
  }),
  deduplicateRequests: jest.fn().mockImplementation((fn: (...args: unknown[]) => unknown) => fn)
}));

// Import the mocked retry mechanism
import * as retryMechanism from '../../utils/retryMechanism';
const mockRetryMechanism = retryMechanism as jest.Mocked<typeof retryMechanism>;

// Mock hooks
jest.mock('../../hooks/useConnectionState', () => ({
  useConnectionState: jest.fn()
}));

jest.mock('../../hooks/useSessionValidation', () => ({
  useSessionValidation: jest.fn()
}));

// Import mocked hooks
import { useConnectionState } from '../../hooks/useConnectionState';
import { useSessionValidation } from '../../hooks/useSessionValidation';

const mockUseConnectionState = useConnectionState as jest.MockedFunction<typeof useConnectionState>;
const mockUseSessionValidation = useSessionValidation as jest.MockedFunction<typeof useSessionValidation>;

// Mock state objects
const mockConnectionState = {
  connectionState: {
    status: 'connected' as const,
    isOnline: true,
    error: null,
    lastConnected: Date.now(),
    reconnectAttempts: 0,
    latency: 50
  },
  connect: jest.fn(),
  disconnect: jest.fn(),
  forceReconnect: jest.fn(),
  reconnect: jest.fn(),
  ping: jest.fn(),
  isConnected: true,
  isOffline: false,
  isConnecting: false,
  isReconnecting: false,
  isFailed: false,
  canReconnect: true
};

const mockValidationState = {
  validationResult: {
    isValid: true,
    exists: true,
    hasAccess: true,
    isExpired: false,
    requiresPassword: false,
    error: null
  },
  isValidating: false,
  revalidate: jest.fn(),
  canJoinSession: true,
  errorMessage: null as string | null,
  isValid: true,
  exists: true,
  hasAccess: true,
  isExpired: false,
  requiresPassword: false,
  sessionData: undefined
};

// Set up default mock implementations
mockUseConnectionState.mockReturnValue(mockConnectionState);
mockUseSessionValidation.mockReturnValue(mockValidationState);

// Mock react-router-dom navigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Helper function to render component with proper routing
const renderWithRouter = (initialEntries: string[]) => {
  return render(
    <MemoryRouter initialEntries={initialEntries}>
      <Routes>
        <Route path="/rroom-v3/:slug" element={<RepRoomSessionPage />} />
        <Route path="/rroom-v3/:slug/:sessionId" element={<RepRoomSessionPage />} />
      </Routes>
    </MemoryRouter>
  );
};

describe('RepRoomSessionPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset mocks to default state
    mockUseConnectionState.mockReturnValue(mockConnectionState);
    mockUseSessionValidation.mockReturnValue(mockValidationState);
    
    // Mock environment variables
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-key';
  });

  describe('Session ID Generation and Redirect Logic', () => {
    it('should show landing page when no session ID is provided', async () => {
      renderWithRouter(['/rroom-v3/test-slug']);

      await waitFor(() => {
        expect(screen.getByTestId('landing-view')).toBeInTheDocument();
      });
    });

    it('should generate session ID and redirect when start button is clicked', async () => {
      renderWithRouter(['/rroom-v3/test-slug']);

      await waitFor(() => {
        expect(screen.getByTestId('landing-view')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('start-button'));

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(
          '/rroom-v3/test-slug/test-session-id',
          { replace: true }
        );
      });
    });

    it('should validate session ID format and show error for invalid format', async () => {
      renderWithRouter(['/rroom-v3/test-slug/invalid-id']);

      await waitFor(() => {
        expect(screen.getByText(/Invalid session ID format/)).toBeInTheDocument();
      });
    });

    it('should accept valid session ID format', async () => {
      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByText(/Joining Session/)).toBeInTheDocument();
      });
    });
  });

  describe('Session Validation Flow', () => {
    it('should show joining session state during validation', async () => {
      mockUseSessionValidation.mockReturnValue({
        ...mockValidationState,
        isValidating: true
      });

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByText(/Joining Session/)).toBeInTheDocument();
        expect(screen.getByText(/abcd1234efgh/)).toBeInTheDocument();
      });
    });

    it('should show validation error when session validation fails', async () => {
      mockUseSessionValidation.mockReturnValue({
        ...mockValidationState,
        validationResult: {
          isValid: false,
          exists: false,
          hasAccess: false,
          isExpired: false,
          requiresPassword: false,
          error: 'Session not found'
        },
        errorMessage: 'Session not found',
        canJoinSession: false,
        isValid: false,
        exists: false,
        hasAccess: false
      });

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByText(/Session Validation: Session not found/)).toBeInTheDocument();
      });
    });

    it('should allow retry validation when validation fails', async () => {
      const mockRevalidate = jest.fn();
      mockUseSessionValidation.mockReturnValue({
        ...mockValidationState,
        validationResult: {
          isValid: false,
          exists: false,
          hasAccess: false,
          isExpired: false,
          requiresPassword: false,
          error: 'Session not found'
        },
        errorMessage: 'Session not found',
        revalidate: mockRevalidate,
        canJoinSession: false,
        isValid: false,
        exists: false,
        hasAccess: false
      });

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByText(/Retry Validation/)).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText(/Retry Validation/));
      expect(mockRevalidate).toHaveBeenCalled();
    });
  });

  describe('Error Boundary Integration', () => {
    it('should wrap content in error boundary', async () => {
      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      });
    });
  });

  describe('Loading States and Error States', () => {
    it('should show loading state initially', async () => {
      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      // Initially should show joining session
      expect(screen.getByText(/Joining Session/)).toBeInTheDocument();
    });

    it('should show error state when configuration loading fails', async () => {
      mockRetryMechanism.fetchWithRetry.mockRejectedValue(new Error('Failed to load config'));

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh?demo=false']);

      await waitFor(() => {
        expect(screen.getByText(/Running in fallback mode/)).toBeInTheDocument();
      });
    });

    it('should show network error banner when network error occurs', async () => {
      mockUseConnectionState.mockReturnValue({
        ...mockConnectionState,
        connectionState: {
          status: 'failed' as const,
          isOnline: false,
          error: 'Network error',
          lastConnected: Date.now(),
          reconnectAttempts: 3,
          latency: 0
        }
      });

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByText(/Running in fallback mode/)).toBeInTheDocument();
      });
    });

    it('should show retry button in error state', async () => {
      renderWithRouter(['/rroom-v3/invalid-slug']);

      await waitFor(() => {
        // The component shows landing page for invalid slug, not an error message
        expect(screen.getByTestId('landing-view')).toBeInTheDocument();
        expect(screen.getByTestId('start-button')).toBeInTheDocument();
      });
    });
  });

  describe('Existing Session Recovery', () => {
    it('should show reconnecting message for existing sessions', async () => {
      mockRetryMechanism.retryDatabaseOperation.mockImplementation((fn) => {
        return fn().then(() => true); // Session exists
      });

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        // The component shows the rep room interface when session exists
        expect(screen.getByTestId('rep-room-interface')).toBeInTheDocument();
      });
    });
  });

  describe('Demo Mode', () => {
    it('should enable demo mode when demo=true in URL', async () => {
      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh?demo=true']);

      await waitFor(() => {
        expect(screen.getByTestId('rep-room-interface')).toBeInTheDocument();
      });

      // Should show demo info in bottom right
      await waitFor(() => {
        expect(screen.getByText(/Rep Room V3 Session/)).toBeInTheDocument();
        expect(screen.getByText(/Session Active/)).toBeInTheDocument();
      });
    });

    it('should redirect to demo mode when demo button is clicked', async () => {
      renderWithRouter(['/rroom-v3/test-slug']);

      await waitFor(() => {
        expect(screen.getByTestId('demo-button')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('demo-button'));

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(
          expect.stringContaining('demo=true'),
          { replace: true }
        );
      });
    });
  });

  describe('Connection State Integration', () => {
    it('should show connection status indicator', async () => {
      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByTestId('connection-indicator')).toBeInTheDocument();
        expect(screen.getByTestId('connection-indicator')).toHaveAttribute('data-status', 'connected');
      });
    });

    it('should handle connection state changes', async () => {
      const mockForceReconnect = jest.fn();
      mockUseConnectionState.mockReturnValue({
        ...mockConnectionState,
        connectionState: {
          status: 'reconnecting',
          isOnline: true,
          error: null,
          lastConnected: Date.now(),
          reconnectAttempts: 1,
          latency: 100
        },
        forceReconnect: mockForceReconnect
      });

      renderWithRouter(['/rroom-v3/test-slug/abcd1234efgh']);

      await waitFor(() => {
        expect(screen.getByTestId('connection-indicator')).toHaveAttribute('data-status', 'reconnecting');
      });
    });
  });
});