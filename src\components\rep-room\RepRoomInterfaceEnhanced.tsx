import React, { useState, useEffect, useCallback, useRef } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { Agent, Human, ChatMessage, PresentationContent, RepRoomState } from '../../types/rep-room';
import { CopilotKitConversationFlow } from './CopilotKitConversationFlow';
import { PresentationArea } from './PresentationArea';
import { ParticipantsPanel } from './ParticipantsPanel';
import { ConnectionStatusBar } from './ConnectionStatusBar';
import { MobileTabNavigation, useMobileTabNavigation, MobileTab } from './mobile/MobileTabNavigation';
import { CollapsiblePanel, useCollapsiblePanel } from './mobile/CollapsiblePanel';
import { useUnifiedVoice } from '../../contexts/rroom/UnifiedVoiceContext';
import { useRepRoomSync } from '../../hooks/useRepRoomSync';
import { useResponsiveLayout, useResponsivePanels } from '../../hooks/useResponsiveLayout';

interface RepRoomInterfaceEnhancedProps {
  initialState?: Partial<RepRoomState>;
  onStateChange?: (state: RepRoomState) => void;
  className?: string;
  agentName?: string;
  sessionId?: string;
  copilotKitRuntimeUrl?: string;
}

export const RepRoomInterfaceEnhanced: React.FC<RepRoomInterfaceEnhancedProps> = ({
  initialState,
  onStateChange,
  className = '',
  agentName = 'AI Assistant',
  sessionId = 'demo-session',
  copilotKitRuntimeUrl = '/api/copilotkit'
}) => {
  // Responsive layout hooks
  const responsive = useResponsiveLayout();
  const panels = useResponsivePanels();
  const leftPanel = useCollapsiblePanel(false, `rep-room-left-${sessionId}`);
  const mobileTab = useMobileTabNavigation('participants', `rep-room-mobile-${sessionId}`);

  // Voice integration
  const voice = useUnifiedVoice();

  // Initialize state with defaults
  const [state, setState] = useState<RepRoomState>({
    activeAgent: null,
    presentationContent: {
      type: 'default',
      title: 'Welcome to Rep Room',
      data: 'This is an enhanced Rep Room with LiveKit voice integration and CopilotKit AI assistance. Select an agent or start a conversation to begin.',
      loading: false
    },
    messages: [],
    isVoiceActive: false,
    participants: {
      mainAgent: {
        id: 'main-agent',
        name: agentName,
        avatar: '',
        type: 'main',
        status: 'ready'
      },
      humans: [],
      specialists: [
        {
          id: 'seo-specialist',
          name: 'SEO Specialist',
          avatar: '',
          type: 'specialist',
          status: 'idle',
          specialization: 'Search Engine Optimization'
        },
        {
          id: 'content-specialist',
          name: 'Content Specialist',
          avatar: '',
          type: 'specialist',
          status: 'idle',
          specialization: 'Content Strategy'
        }
      ]
    },
    sessionInfo: {
      title: 'Enhanced Rep Room Session',
      sessionId: sessionId,
      slug: 'enhanced-demo'
    },
    ...initialState
  });

  // Update state helper
  const updateState = useCallback((updates: Partial<RepRoomState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Get all agents (main + specialists)
  const getAllAgents = useCallback((): Agent[] => {
    return [state.participants.mainAgent, ...state.participants.specialists];
  }, [state.participants.mainAgent, state.participants.specialists]);

  // Stabilized sync callbacks
  const onParticipantJoined = useCallback((participant: Human | Agent) => {
    console.log('[RepRoomSync] Participant joined:', participant);
    if ('role' in participant) {
      // It's a human
      setState(prev => ({
        ...prev,
        participants: {
          ...prev.participants,
          humans: [...prev.participants.humans, participant as Human]
        }
      }));
    }
  }, []);

  const onParticipantLeft = useCallback((participantId: string) => {
    console.log('[RepRoomSync] Participant left:', participantId);
    setState(prev => ({
      ...prev,
      participants: {
        ...prev.participants,
        humans: prev.participants.humans.filter(h => h.id !== participantId)
      }
    }));
  }, []);

  const onMessageReceived = useCallback((message: ChatMessage) => {
    console.log('[RepRoomSync] Message received:', message);
    setState(prev => ({
      ...prev,
      messages: [...prev.messages, message]
    }));
  }, []);

  const onStateUpdate = useCallback((updates: Partial<RepRoomState>) => {
    console.log('[RepRoomSync] State update received:', updates);
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const onAgentStatusChange = useCallback((agentId: string, status: string) => {
    console.log('[RepRoomSync] Agent status changed:', agentId, status);
    setState(prev => {
      const allAgents = [prev.participants.mainAgent, ...prev.participants.specialists];
      const updatedAgents = allAgents.map(agent =>
        agent.id === agentId ? { ...agent, status: status as Agent['status'] } : agent
      );
      const mainAgent = updatedAgents.find(a => a.type === 'main');
      const specialists = updatedAgents.filter(a => a.type === 'specialist');
      
      if (mainAgent) {
        return {
          ...prev,
          participants: {
            ...prev.participants,
            mainAgent,
            specialists
          }
        };
      }
      return prev;
    });
  }, []);

  const onConnectionStateChange = useCallback((connectionState: string) => {
    console.log('[RepRoomSync] Connection state changed:', connectionState);
  }, []);

  const onError = useCallback((error: string) => {
    console.error('[RepRoomSync] Error:', error);
  }, []);

  const syncCallbacks = {
    onParticipantJoined,
    onParticipantLeft,
    onMessageReceived,
    onStateUpdate,
    onAgentStatusChange,
    onConnectionStateChange,
    onError
  };

  // Real-time synchronization
  const sync = useRepRoomSync(
    {
      sessionId,
      wsUrl: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/rep-room-sync`,
    },
    syncCallbacks
  );

  // Notify parent of state changes - use ref to prevent infinite loops
  const onStateChangeRef = useRef(onStateChange);
  onStateChangeRef.current = onStateChange;

  useEffect(() => {
    onStateChangeRef.current?.(state);
  }, [state]); // Use ref to prevent infinite loops from onStateChange prop changes

  // Sync voice messages with chat messages
  useEffect(() => {
    if (voice.state.visibleMessages.length > 0) {
      console.log('[RepRoomInterface] Voice messages received:', voice.state.visibleMessages);
      
      const voiceMessages: ChatMessage[] = voice.state.visibleMessages.map(msg => ({
        id: msg.id,
        sender: msg.role === 'user' ? 'You' : agentName,
        content: msg.content,
        timestamp: new Date(msg.timestamp),
        type: msg.role === 'user' ? 'human' : 'agent',
        isTyping: !msg.isComplete,
        avatar: msg.role === 'assistant' ? '' : undefined
      }));

      // Only update if messages have actually changed to prevent infinite loops
      setState(prev => {
        const currentMessageIds = prev.messages.map(m => m.id).sort().join(',');
        const newMessageIds = voiceMessages.map(m => m.id).sort().join(',');
        
        if (currentMessageIds !== newMessageIds) {
          console.log('[RepRoomInterface] Updating messages with voice messages:', voiceMessages);
          return { ...prev, messages: voiceMessages };
        }
        return prev;
      });
    }
  }, [voice.state.visibleMessages, agentName]);

  // Note: Removed redundant voice state sync to prevent conflicts
  // Voice state is managed directly by voice context


  // Handle agent selection
  const handleAgentSelect = (agent: Agent) => {
    updateState({
      activeAgent: agent.id,
      presentationContent: {
        type: 'default',
        title: `${agent.name} Selected`,
        data: `You've selected ${agent.name}. ${agent.specialization ? `Specialization: ${agent.specialization}` : 'Main agent ready to assist.'}`,
        loading: false,
        generatedBy: agent.id
      }
    });

    // Simulate agent generating content
    setTimeout(() => {
      if (agent.specialization === 'Search Engine Optimization') {
        updateState({
          presentationContent: {
            type: 'keyword-analysis',
            title: 'SEO Keyword Analysis',
            data: {
              keywords: [
                { term: 'digital marketing', volume: 12000, difficulty: 'medium' },
                { term: 'SEO optimization', volume: 8500, difficulty: 'high' },
                { term: 'content strategy', volume: 6200, difficulty: 'low' },
                { term: 'search rankings', volume: 4800, difficulty: 'medium' },
                { term: 'organic traffic', volume: 7300, difficulty: 'medium' },
                { term: 'keyword research', volume: 5900, difficulty: 'low' }
              ],
              insights: [
                'Focus on long-tail keywords for better conversion rates',
                'Content strategy keywords show low competition',
                'Digital marketing terms have high search volume',
                'Consider local SEO opportunities'
              ]
            },
            loading: false,
            generatedBy: agent.id
          }
        });
      } else if (agent.specialization === 'Content Strategy') {
        updateState({
          presentationContent: {
            type: 'competitor-analysis',
            title: 'Content Competitor Analysis',
            data: {
              competitors: [
                {
                  name: 'ContentKing',
                  description: 'Leading content marketing platform',
                  strength: 'high',
                  traffic: 125000,
                  domain_authority: 78
                },
                {
                  name: 'BlogBoost',
                  description: 'Emerging content optimization tool',
                  strength: 'medium',
                  traffic: 45000,
                  domain_authority: 52
                },
                {
                  name: 'WriteWell',
                  description: 'AI-powered content assistant',
                  strength: 'low',
                  traffic: 18000,
                  domain_authority: 34
                }
              ],
              summary: 'The content marketing space shows opportunities for differentiation through AI-powered personalization and real-time optimization features.'
            },
            loading: false,
            generatedBy: agent.id
          }
        });
      }
    }, 2000);
  };

  // Handle voice toggle with LiveKit integration
  const handleToggleVoice = async () => {
    try {
      if (voice.isVoiceActive) {
        // Disconnect from voice
        console.log('[RepRoomInterface] Disconnecting voice...');
        await voice.controls.disconnect();
      } else {
        // Connect to voice
        console.log('[RepRoomInterface] Connecting voice...');
        await voice.controls.connect();
      }
    } catch (error) {
      console.error('Voice toggle error:', error);
    }
  };

  // Handle sending messages
  const handleSendMessage = (content: string) => {
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      sender: 'You',
      content,
      timestamp: new Date(),
      type: 'human',
      isTyping: false
    };

    updateState({
      messages: [...state.messages, newMessage]
    });

    // Send message via sync if connected
    if (sync.isConnected) {
      sync.sendChatMessage(newMessage);
    }

    // Simulate agent response
    setTimeout(() => {
      const activeAgent = getAllAgents().find(a => a.id === state.activeAgent) || getAllAgents()[0];
      if (activeAgent) {
        const agentResponse: ChatMessage = {
          id: `msg-${Date.now()}-response`,
          sender: activeAgent.name,
          content: `Thank you for your message: "${content}". As ${activeAgent.name}, I'm here to help you with ${activeAgent.specialization || 'your needs'}. How can I assist you further?`,
          timestamp: new Date(),
          type: 'agent',
          isTyping: false,
          avatar: activeAgent.avatar
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, agentResponse]
        }));

        // Send agent response via sync if connected
        if (sync.isConnected) {
          sync.sendChatMessage(agentResponse);
        }
      }
    }, 1000);
  };

  // Handle user invitation (placeholder)
  const handleInviteUser = () => {
    console.log('Invite user functionality would be implemented here');
    // Add a demo human participant
    const newHuman: Human = {
      id: `human-${Date.now()}`,
      name: 'New Participant',
      avatar: '',
      status: 'listening',
      role: 'Guest'
    };
    
    updateState({
      participants: {
        ...state.participants,
        humans: [...state.participants.humans, newHuman]
      }
    });

    // Sync participant join if connected
    if (sync.isConnected) {
      sync.joinSession(newHuman);
    }
  };

  // Handle settings (placeholder)
  const handleSettings = () => {
    console.log('Settings functionality would be implemented here');
  };

  // Auto-connect sync on mount - only depend on sessionId to prevent infinite loops
  useEffect(() => {
    sync.connect();
    return () => {
      sync.disconnect();
    };
  }, [sessionId]); // Remove sync methods from dependencies to prevent infinite loops

  // Update mobile tab badges based on state changes - use direct calls to prevent infinite loops
  useEffect(() => {
    const humanCount = state.participants.humans.length;
    if (humanCount > 0) {
      mobileTab.updateBadge('participants', { count: humanCount, type: 'notification' });
    } else {
      mobileTab.clearBadge('participants');
    }
  }, [state.participants.humans.length]); // Direct effect to prevent infinite loops

  useEffect(() => {
    const recentMessages = state.messages.filter(msg =>
      Date.now() - msg.timestamp.getTime() < 30000 && msg.type === 'agent'
    );
    if (recentMessages.length > 0 && mobileTab.activeTab !== 'conversation') {
      mobileTab.updateBadge('conversation', { count: recentMessages.length, type: 'notification' });
    } else if (mobileTab.activeTab === 'conversation') {
      mobileTab.clearBadge('conversation');
    }
  }, [state.messages.length, mobileTab.activeTab]); // Direct effect to prevent infinite loops

  // Handle mobile tab changes
  const handleMobileTabChange = (tab: MobileTab) => {
    mobileTab.changeTab(tab);
    // Clear badge when tab becomes active
    mobileTab.clearBadge(tab);
  };

  // Render mobile single panel view
  const renderMobileView = () => {
    const { activeTab } = mobileTab;
    
    return (
      <div className="h-full flex flex-col">
        <MobileTabNavigation
          activeTab={activeTab}
          onTabChange={handleMobileTabChange}
          badges={mobileTab.badges}
          enableSwipeGestures={true}
        />
        
        <div className="flex-1 overflow-hidden">
          {activeTab === 'participants' && (
            <ParticipantsPanel
              agents={getAllAgents()}
              humans={state.participants.humans}
              onAgentSelect={handleAgentSelect}
              onInviteUser={handleInviteUser}
              onSettings={handleSettings}
            />
          )}
          
          {activeTab === 'presentation' && (
            <PresentationArea
              content={state.presentationContent}
            />
          )}
          
          {activeTab === 'conversation' && (
            <CopilotKitConversationFlow
              messages={state.messages}
              isVoiceActive={voice.isVoiceActive}
              isVoiceConnecting={voice.state.isConnecting}
              onToggleVoice={handleToggleVoice}
              onSendMessage={handleSendMessage}
              agentName={agentName}
              sessionId={sessionId}
            />
          )}
        </div>
      </div>
    );
  };

  // Render tablet view with collapsible left panel
  const renderTabletView = () => {
    return (
      <div className="flex-1 flex">
        <CollapsiblePanel
          isCollapsed={leftPanel.isCollapsed}
          onToggle={leftPanel.toggle}
          position="left"
          title="Participants"
          resizable={true}
          persistState={true}
          persistKey={`rep-room-left-${sessionId}`}
          collapsedWidth={60}
          expandedWidth={320}
          minWidth={240}
          maxWidth={400}
        >
          <ParticipantsPanel
            agents={getAllAgents()}
            humans={state.participants.humans}
            onAgentSelect={handleAgentSelect}
            onInviteUser={handleInviteUser}
            onSettings={handleSettings}
          />
        </CollapsiblePanel>

        <div className="flex-1">
          <PresentationArea
            content={state.presentationContent}
          />
        </div>

        <div className="w-80 min-w-[320px] max-w-[400px]">
          <CopilotKitConversationFlow
            messages={state.messages}
            isVoiceActive={voice.isVoiceActive}
            isVoiceConnecting={voice.state.isConnecting}
            onToggleVoice={handleToggleVoice}
            onSendMessage={handleSendMessage}
            agentName={agentName}
            sessionId={sessionId}
          />
        </div>
      </div>
    );
  };

  // Render desktop view (original layout)
  const renderDesktopView = () => {
    return (
      <div className="flex-1 flex">
        <div className="w-1/5 min-w-[280px] max-w-[400px]">
          <ParticipantsPanel
            agents={getAllAgents()}
            humans={state.participants.humans}
            onAgentSelect={handleAgentSelect}
            onInviteUser={handleInviteUser}
            onSettings={handleSettings}
          />
        </div>

        <div className="flex-1">
          <PresentationArea
            content={state.presentationContent}
          />
        </div>

        <div className="w-3/10 min-w-[320px] max-w-[400px]">
          <CopilotKitConversationFlow
            messages={state.messages}
            isVoiceActive={voice.isVoiceActive}
            isVoiceConnecting={voice.state.isConnecting}
            onToggleVoice={handleToggleVoice}
            onSendMessage={handleSendMessage}
            agentName={agentName}
            sessionId={sessionId}
          />
        </div>
      </div>
    );
  };

  return (
    <div
      data-testid="rep-room-interface"
      className={`h-screen bg-gray-100 flex flex-col ${className}`}
    >
      {/* Connection Status Bar */}
      <ConnectionStatusBar
        connectionState={sync.connectionState}
        participantCount={sync.participantCount}
        lastSyncTimestamp={sync.lastSyncTimestamp}
        reconnectAttempts={sync.reconnectAttempts}
        error={sync.error}
        onRetry={sync.reconnect}
      />

      {/* Main Content - Responsive Layout */}
      {responsive.isDevice('mobile') && renderMobileView()}
      {responsive.isDevice('tablet') && renderTabletView()}
      {responsive.isDevice('desktop') && renderDesktopView()}
    </div>
  );
};

// Demo data factory for enhanced interface
export const createEnhancedDemoRepRoomState = (agentName: string = 'AI Assistant'): Partial<RepRoomState> => {
  const demoMessages: ChatMessage[] = [
    {
      id: 'msg-1',
      sender: agentName,
      content: `Welcome to the enhanced Rep Room! I'm ${agentName} and I'm here to help you with advanced AI-powered assistance. This interface features LiveKit voice integration and CopilotKit AI support. What would you like to work on today?`,
      timestamp: new Date(Date.now() - 60000),
      type: 'agent',
      avatar: ''
    }
  ];

  return {
    messages: demoMessages,
    activeAgent: 'main-agent',
    presentationContent: {
      type: 'default',
      title: 'Enhanced Rep Room Dashboard',
      data: 'This enhanced Rep Room features:\n\n• LiveKit voice integration for real-time communication\n• CopilotKit AI assistance for intelligent conversations\n• Multi-agent collaboration with specialized AI assistants\n• Real-time presentation and analysis capabilities\n\nSelect a specialist agent or start a conversation to explore the features.',
      loading: false,
      generatedBy: 'main-agent'
    },
    isVoiceActive: false
  };
};