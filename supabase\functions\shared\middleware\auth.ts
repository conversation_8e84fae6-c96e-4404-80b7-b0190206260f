import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { userHasPermission, Permissions } from '../rbac.ts';

// CORS headers for authentication responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type, x-api-key',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
};

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    organization_id: string;
    role?: string;
    isHouseRole?: boolean;
  };
}

export interface AuthError extends Error {
  status: number;
}

export interface AuthOptions {
  requiredPermission?: string | Permissions;
}

export function withAuth(
  handler: (req: AuthenticatedRequest) => Promise<Response>,
  options?: AuthOptions
): (req: Request) => Promise<Response> {
  return async (req: Request): Promise<Response> => {
    try {
      // Handle CORS preflight requests first, before authentication
      if (req.method === 'OPTIONS') {
        return new Response('ok', { 
          headers: corsHeaders,
          status: 200
        });
      }

      // Extract JWT token from Authorization header
      const authHeader = req.headers.get('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        const error = new Error('Missing or invalid Authorization header') as AuthError;
        error.status = 401;
        throw error;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Create Supabase client
      const supabaseClient = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_ANON_KEY') ?? ''
      );

      // Verify the JWT token and get user
      const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);

      if (authError || !user) {
        const error = new Error('Invalid or expired token') as AuthError;
        error.status = 401;
        throw error;
      }

      // Get user profile with organization information using service role
      // to bypass RLS since JWT claims might not be set yet
      const serviceClient = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
      );

      const { data: userProfile, error: profileError } = await serviceClient
        .from('users')
        .select(`
          id,
          email,
          organization_id,
          role,
          organizations (
            id,
            name,
            status
          )
        `)
        .eq('id', user.id)
        .single();

      if (profileError || !userProfile) {
        const error = new Error('User profile not found') as AuthError;
        error.status = 404;
        throw error;
      }

      // Check if organization is active
      if (userProfile.organizations?.status !== 'active') {
        const error = new Error('Organization is not active') as AuthError;
        error.status = 403;
        throw error;
      }

      // Determine if user has house role
      const isHouseRole = userProfile.role?.startsWith('house_') || false;

      // Create authenticated request object
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.user = {
        id: userProfile.id,
        email: userProfile.email,
        organization_id: userProfile.organization_id,
        role: userProfile.role,
        isHouseRole: isHouseRole,
      };

      // Check permissions if required
      if (options?.requiredPermission) {
        const hasPermission = userHasPermission(userProfile.role || '', options.requiredPermission);
        
        // For agent management, also allow house roles
        if (!hasPermission && !isHouseRole) {
          const error = new Error(`Insufficient permissions. Required: ${options.requiredPermission}`) as AuthError;
          error.status = 403;
          throw error;
        }
      }

      // Call the original handler with authenticated request
      return await handler(authenticatedReq);

    } catch (error) {
      console.error('Authentication error:', error);

      if (error.status) {
        return new Response(
          JSON.stringify({
            error: error.message,
            code: 'AUTH_ERROR'
          }),
          {
            status: error.status,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      return new Response(
        JSON.stringify({
          error: 'Authentication failed',
          code: 'AUTH_ERROR'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }
  };
}

// Optional: Role-based authorization middleware
export function withRole(
  requiredRole: string | string[]
): (handler: (req: AuthenticatedRequest) => Promise<Response>) => (req: AuthenticatedRequest) => Promise<Response> {
  return (handler) => {
    return async (req: AuthenticatedRequest): Promise<Response> => {
      const userRole = req.user.role;
      const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

      if (!userRole || !allowedRoles.includes(userRole)) {
        return new Response(
          JSON.stringify({
            error: 'Insufficient permissions',
            code: 'INSUFFICIENT_PERMISSIONS',
            required_role: requiredRole,
            user_role: userRole
          }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      return await handler(req);
    };
  };
}

// Helper function to extract organization context
export function getOrganizationContext(req: AuthenticatedRequest) {
  return {
    organizationId: req.user.organization_id,
    userId: req.user.id,
    userRole: req.user.role,
  };
}

// Helper function to create Supabase client with user context
export function createAuthenticatedSupabaseClient(req: AuthenticatedRequest) {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    {
      global: {
        headers: { Authorization: req.headers.get('Authorization')! },
      },
    }
  );
}
