import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ChevronLeft, ChevronRight, Maximize2, Minimize2 } from 'lucide-react';

export interface CollapsiblePanelProps {
  children: React.ReactNode;
  isCollapsed: boolean;
  onToggle: () => void;
  position: 'left' | 'right';
  collapsedWidth?: number;
  expandedWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  className?: string;
  title?: string;
  showToggleButton?: boolean;
  animationDuration?: number;
  resizable?: boolean;
  persistState?: boolean;
  persistKey?: string;
}

export interface CollapsiblePanelState {
  isAnimating: boolean;
  currentWidth: number;
  isDragging: boolean;
  dragStartX: number;
  dragStartWidth: number;
}

/**
 * Collapsible panel wrapper for tablet view with touch-optimized controls
 */
export const CollapsiblePanel: React.FC<CollapsiblePanelProps> = ({
  children,
  isCollapsed,
  onToggle,
  position,
  collapsedWidth = 60,
  expandedWidth = 320,
  minWidth = 240,
  maxWidth = 480,
  className = '',
  title,
  showToggleButton = true,
  animationDuration = 300,
  resizable = false,
  persistState = false,
  persistKey
}) => {
  const panelRef = useRef<HTMLDivElement>(null);
  const [state, setState] = useState<CollapsiblePanelState>({
    isAnimating: false,
    currentWidth: isCollapsed ? collapsedWidth : expandedWidth,
    isDragging: false,
    dragStartX: 0,
    dragStartWidth: 0
  });

  // Load persisted width
  useEffect(() => {
    if (persistState && persistKey && typeof window !== 'undefined') {
      const savedWidth = localStorage.getItem(`panel-width-${persistKey}`);
      if (savedWidth) {
        const width = parseInt(savedWidth, 10);
        if (width >= minWidth && width <= maxWidth) {
          setState(prev => ({ ...prev, currentWidth: isCollapsed ? collapsedWidth : width }));
        }
      }
    }
  }, [persistState, persistKey, minWidth, maxWidth, isCollapsed, collapsedWidth]);

  // Handle collapse/expand animation
  useEffect(() => {
    setState(prev => ({ ...prev, isAnimating: true }));
    
    const targetWidth = isCollapsed ? collapsedWidth : prev => prev.currentWidth > collapsedWidth ? prev.currentWidth : expandedWidth;
    
    setState(prev => ({
      ...prev,
      currentWidth: typeof targetWidth === 'function' ? targetWidth(prev) : targetWidth
    }));

    const timer = setTimeout(() => {
      setState(prev => ({ ...prev, isAnimating: false }));
    }, animationDuration);

    return () => clearTimeout(timer);
  }, [isCollapsed, collapsedWidth, expandedWidth, animationDuration]);

  // Handle resize drag
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!resizable || isCollapsed) return;
    
    e.preventDefault();
    setState(prev => ({
      ...prev,
      isDragging: true,
      dragStartX: e.clientX,
      dragStartWidth: prev.currentWidth
    }));
  }, [resizable, isCollapsed]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!resizable || isCollapsed) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    setState(prev => ({
      ...prev,
      isDragging: true,
      dragStartX: touch.clientX,
      dragStartWidth: prev.currentWidth
    }));
  }, [resizable, isCollapsed]);

  // Handle drag movement
  useEffect(() => {
    if (!state.isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = position === 'left' ? e.clientX - state.dragStartX : state.dragStartX - e.clientX;
      const newWidth = Math.max(minWidth, Math.min(maxWidth, state.dragStartWidth + deltaX));
      
      setState(prev => ({ ...prev, currentWidth: newWidth }));
    };

    const handleTouchMove = (e: TouchEvent) => {
      const touch = e.touches[0];
      const deltaX = position === 'left' ? touch.clientX - state.dragStartX : state.dragStartX - touch.clientX;
      const newWidth = Math.max(minWidth, Math.min(maxWidth, state.dragStartWidth + deltaX));
      
      setState(prev => ({ ...prev, currentWidth: newWidth }));
    };

    const handleEnd = () => {
      setState(prev => ({ ...prev, isDragging: false }));
      
      // Persist width
      if (persistState && persistKey && typeof window !== 'undefined') {
        localStorage.setItem(`panel-width-${persistKey}`, state.currentWidth.toString());
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleEnd);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleEnd);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleEnd);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleEnd);
    };
  }, [state.isDragging, state.dragStartX, state.dragStartWidth, position, minWidth, maxWidth, persistState, persistKey, state.currentWidth]);

  const togglePanel = useCallback(() => {
    if (!state.isAnimating) {
      onToggle();
    }
  }, [onToggle, state.isAnimating]);

  const getToggleIcon = () => {
    if (isCollapsed) {
      return position === 'left' ? ChevronRight : ChevronLeft;
    }
    return position === 'left' ? ChevronLeft : ChevronRight;
  };

  const ToggleIcon = getToggleIcon();

  return (
    <div
      ref={panelRef}
      className={`
        relative bg-white border-gray-200 flex-shrink-0
        ${position === 'left' ? 'border-r' : 'border-l'}
        ${state.isAnimating ? 'transition-all ease-in-out' : ''}
        ${className}
      `}
      style={{
        width: `${state.currentWidth}px`,
        transitionDuration: state.isAnimating ? `${animationDuration}ms` : '0ms'
      }}
    >
      {/* Panel Header */}
      {(title || showToggleButton) && (
        <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
          {title && !isCollapsed && (
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {title}
            </h3>
          )}
          
          {showToggleButton && (
            <button
              onClick={togglePanel}
              disabled={state.isAnimating}
              className={`
                p-1.5 rounded-md transition-all duration-200
                ${state.isAnimating 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'hover:bg-gray-200 active:bg-gray-300 active:scale-95'
                }
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
              `}
              aria-label={isCollapsed ? 'Expand panel' : 'Collapse panel'}
              title={isCollapsed ? 'Expand panel' : 'Collapse panel'}
            >
              <ToggleIcon size={16} className="text-gray-600" />
            </button>
          )}
        </div>
      )}

      {/* Panel Content */}
      <div 
        className={`
          flex-1 overflow-hidden
          ${isCollapsed ? 'opacity-0 pointer-events-none' : 'opacity-100'}
          ${state.isAnimating ? 'transition-opacity ease-in-out' : ''}
        `}
        style={{
          transitionDuration: state.isAnimating ? `${animationDuration}ms` : '0ms'
        }}
      >
        {children}
      </div>

      {/* Collapsed State Indicator */}
      {isCollapsed && showToggleButton && (
        <div className="absolute inset-0 flex items-center justify-center">
          <button
            onClick={togglePanel}
            disabled={state.isAnimating}
            className={`
              p-3 rounded-full bg-white shadow-md border border-gray-200
              transition-all duration-200
              ${state.isAnimating 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:shadow-lg hover:scale-105 active:scale-95'
              }
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            `}
            aria-label="Expand panel"
            title="Expand panel"
          >
            <Maximize2 size={20} className="text-gray-600" />
          </button>
        </div>
      )}

      {/* Resize Handle */}
      {resizable && !isCollapsed && (
        <div
          className={`
            absolute top-0 ${position === 'left' ? 'right-0' : 'left-0'} 
            w-1 h-full cursor-col-resize bg-transparent hover:bg-blue-200
            transition-colors duration-200 group
          `}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          <div className="absolute inset-y-0 -inset-x-1 flex items-center justify-center">
            <div className="w-1 h-8 bg-gray-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
          </div>
        </div>
      )}

      {/* Drag Overlay */}
      {state.isDragging && (
        <div className="fixed inset-0 cursor-col-resize z-50" />
      )}
    </div>
  );
};

/**
 * Hook for managing collapsible panel state
 */
export const useCollapsiblePanel = (
  initialCollapsed: boolean = false,
  persistKey?: string
) => {
  const [isCollapsed, setIsCollapsed] = useState(() => {
    if (persistKey && typeof window !== 'undefined') {
      const saved = localStorage.getItem(`panel-collapsed-${persistKey}`);
      return saved ? JSON.parse(saved) : initialCollapsed;
    }
    return initialCollapsed;
  });

  const toggle = useCallback(() => {
    setIsCollapsed(prev => {
      const newValue = !prev;
      if (persistKey && typeof window !== 'undefined') {
        localStorage.setItem(`panel-collapsed-${persistKey}`, JSON.stringify(newValue));
      }
      return newValue;
    });
  }, [persistKey]);

  const collapse = useCallback(() => {
    setIsCollapsed(true);
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`panel-collapsed-${persistKey}`, JSON.stringify(true));
    }
  }, [persistKey]);

  const expand = useCallback(() => {
    setIsCollapsed(false);
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`panel-collapsed-${persistKey}`, JSON.stringify(false));
    }
  }, [persistKey]);

  return {
    isCollapsed,
    toggle,
    collapse,
    expand
  };
};