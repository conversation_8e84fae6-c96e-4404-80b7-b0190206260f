import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ErrorBoundary } from '../../../components/common/ErrorBoundary';

// Test helper component that throws errors on demand
const ThrowError = ({ shouldThrow, errorMessage = 'Test error' }: { 
  shouldThrow: boolean; 
  errorMessage?: string;
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div data-testid="no-error">No error</div>;
};

// Test helper component for async errors
const AsyncThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  React.useEffect(() => {
    if (shouldThrow) {
      // This won't be caught by error boundary as it's in useEffect
      throw new Error('Async error');
    }
  }, [shouldThrow]);
  
  return <div data-testid="async-component">Async component</div>;
};

// Test helper component for event handler errors
const EventHandlerError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  const handleClick = () => {
    if (shouldThrow) {
      throw new Error('Event handler error');
    }
  };

  return (
    <button data-testid="event-button" onClick={handleClick}>
      Click me
    </button>
  );
};

// Mock console methods
const originalConsoleError = console.error;
const originalConsoleLog = console.log;

describe('ErrorBoundary', () => {
  let consoleErrorSpy: jest.SpyInstance;
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(() => {
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
    consoleLogSpy.mockRestore();
  });

  afterAll(() => {
    console.error = originalConsoleError;
    console.log = originalConsoleLog;
  });

  describe('Error Catching', () => {
    it('should catch JavaScript errors in child components', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.queryByTestId('no-error')).not.toBeInTheDocument();
    });

    it('should catch errors during render phase', () => {
      const RenderError = () => {
        throw new Error('Render phase error');
      };

      render(
        <ErrorBoundary>
          <RenderError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });

    it('should catch errors in lifecycle methods', () => {
      class LifecycleError extends React.Component {
        componentDidMount() {
          throw new Error('Lifecycle error');
        }

        render() {
          return <div>Component with lifecycle error</div>;
        }
      }

      render(
        <ErrorBoundary>
          <LifecycleError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });

    it('should NOT catch errors in event handlers', () => {
      const onError = jest.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <EventHandlerError shouldThrow={false} />
        </ErrorBoundary>
      );

      const button = screen.getByTestId('event-button');
      
      // This should not trigger the error boundary
      expect(() => {
        fireEvent.click(button);
      }).not.toThrow();

      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
      expect(onError).not.toHaveBeenCalled();
    });

    it('should render children normally when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('no-error')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });
  });

  describe('Error Display', () => {
    it('should display error message to user', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText(/We encountered an unexpected error/)).toBeInTheDocument();
    });

    it('should show technical details in development mode', () => {
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Detailed test error" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Error Details (Development)')).toBeInTheDocument();
      // Use getAllByText to handle multiple occurrences and check the first one
      const errorTexts = screen.getAllByText(/Detailed test error/);
      expect(errorTexts.length).toBeGreaterThan(0);

      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should hide technical details in production mode', () => {
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Production error" />
        </ErrorBoundary>
      );

      expect(screen.queryByText('Error Details (Development)')).not.toBeInTheDocument();
      expect(screen.queryByText(/Production error/)).not.toBeInTheDocument();

      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should display error ID', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText(/Error ID:/)).toBeInTheDocument();
    });

    it('should display error icon', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      const errorIcon = screen.getByTestId('error-icon');
      expect(errorIcon).toBeInTheDocument();
    });
  });

  describe('Recovery Functionality', () => {
    it('should reset error state when "Try Again" button is clicked', () => {
      // Test that the error boundary's internal state is reset by using resetKeys
      const { rerender } = render(
        <ErrorBoundary resetKeys={['test-key-1']}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      // Verify error state
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Click try again to reset error boundary state
      const tryAgainButton = screen.getByText('Try Again');
      fireEvent.click(tryAgainButton);

      // Change resetKeys to trigger a reset and re-render with working component
      rerender(
        <ErrorBoundary resetKeys={['test-key-2']}>
          <div data-testid="working-component">Working component</div>
        </ErrorBoundary>
      );

      // Should now show the normal content
      expect(screen.getByTestId('working-component')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should execute recovery callback', () => {
      const onError = jest.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });

    it('should handle multiple error-recovery cycles', () => {
      let shouldThrow = true;
      let key = 0;
      
      const TestComponent = () => <ThrowError shouldThrow={shouldThrow} />;
      
      const { rerender } = render(
        <ErrorBoundary key={key}>
          <TestComponent />
        </ErrorBoundary>
      );

      // First error
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // First recovery
      fireEvent.click(screen.getByText('Try Again'));
      shouldThrow = false;
      key++;
      rerender(
        <ErrorBoundary key={key}>
          <TestComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('no-error')).toBeInTheDocument();

      // Second error
      shouldThrow = true;
      key++;
      rerender(
        <ErrorBoundary key={key}>
          <TestComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Second recovery
      fireEvent.click(screen.getByText('Try Again'));
      shouldThrow = false;
      key++;
      rerender(
        <ErrorBoundary key={key}>
          <TestComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('no-error')).toBeInTheDocument();
    });

    it('should reset error state when resetKeys change', () => {
      const { rerender } = render(
        <ErrorBoundary resetKeys={['key1']}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Change resetKeys to trigger reset
      rerender(
        <ErrorBoundary resetKeys={['key2']}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('no-error')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should reset error state when resetOnPropsChange is true and props change', () => {
      const { rerender } = render(
        <ErrorBoundary resetOnPropsChange={true}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Change props to trigger reset
      rerender(
        <ErrorBoundary resetOnPropsChange={true} fallback={<div>Custom fallback</div>}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('no-error')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });
  });

  describe('Error Reporting', () => {
    it('should log errors to console.error', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Console test error" />
        </ErrorBoundary>
      );

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[ErrorBoundary] Caught an error:',
        expect.any(Error)
      );
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[ErrorBoundary] Error info:',
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });

    it('should call error reporting callback with error details', () => {
      const onError = jest.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowError shouldThrow={true} errorMessage="Callback test error" />
        </ErrorBoundary>
      );

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Callback test error'
        }),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });

    it('should include stack trace in error reports', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(consoleLogSpy).toHaveBeenCalledWith(
        '[ErrorBoundary] Error report:',
        expect.objectContaining({
          stack: expect.any(String),
          componentStack: expect.any(String)
        })
      );
    });

    it('should capture component tree info', () => {
      render(
        <ErrorBoundary>
          <div>
            <span>
              <ThrowError shouldThrow={true} />
            </span>
          </div>
        </ErrorBoundary>
      );

      expect(consoleLogSpy).toHaveBeenCalledWith(
        '[ErrorBoundary] Error report:',
        expect.objectContaining({
          componentStack: expect.stringContaining('ThrowError')
        })
      );
    });
  });

  describe('Fallback UI', () => {
    it('should render default fallback UI correctly', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
      expect(screen.getByText('Reload Page')).toBeInTheDocument();
      expect(screen.getByText('Go Home')).toBeInTheDocument();
    });

    it('should render custom fallback component when provided', () => {
      const CustomFallback = <div data-testid="custom-fallback">Custom error UI</div>;
      
      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom error UI')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should have proper accessibility attributes', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(3);
      
      buttons.forEach(button => {
        expect(button).toHaveAttribute('class');
        expect(button).not.toHaveAttribute('disabled');
      });
    });

    it('should handle reload page button click', () => {
      // Mock window.location.reload
      const mockReload = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { reload: mockReload },
        writable: true
      });

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      fireEvent.click(screen.getByText('Reload Page'));
      expect(mockReload).toHaveBeenCalled();
    });

    it('should handle go home button click', () => {
      // Mock window.location.href
      const mockLocation = { href: '' };
      Object.defineProperty(window, 'location', {
        value: mockLocation,
        writable: true
      });

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      fireEvent.click(screen.getByText('Go Home'));
      expect(mockLocation.href).toBe('/');
    });
  });

  describe('Edge Cases', () => {
    it('should handle nested error boundaries', () => {
      const InnerError = () => {
        throw new Error('Inner error');
      };

      render(
        <ErrorBoundary fallback={<div data-testid="outer-fallback">Outer fallback</div>}>
          <ErrorBoundary fallback={<div data-testid="inner-fallback">Inner fallback</div>}>
            <InnerError />
          </ErrorBoundary>
        </ErrorBoundary>
      );

      // Inner boundary should catch the error
      expect(screen.getByTestId('inner-fallback')).toBeInTheDocument();
      expect(screen.queryByTestId('outer-fallback')).not.toBeInTheDocument();
    });

    it('should handle null children', () => {
      render(
        <ErrorBoundary>
          {null}
        </ErrorBoundary>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should handle undefined children', () => {
      render(
        <ErrorBoundary>
          {undefined}
        </ErrorBoundary>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should handle errors during error boundary\'s own render', () => {
      // This is a complex edge case - if the ErrorBoundary itself throws during render
      // it would be caught by a parent error boundary
      const ProblematicFallback = () => {
        throw new Error('Fallback error');
      };

      expect(() => {
        render(
          <ErrorBoundary fallback={<ProblematicFallback />}>
            <ThrowError shouldThrow={true} />
          </ErrorBoundary>
        );
      }).toThrow('Fallback error');
    });

    it('should not reset when resetKeys are the same', () => {
      const { rerender } = render(
        <ErrorBoundary resetKeys={['key1', 'key2']}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Re-render with same resetKeys
      rerender(
        <ErrorBoundary resetKeys={['key1', 'key2']}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      // Should still show error state
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.queryByTestId('no-error')).not.toBeInTheDocument();
    });

    it('should handle empty resetKeys array', () => {
      const { rerender } = render(
        <ErrorBoundary resetKeys={[]}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Change to different empty array (should not reset)
      rerender(
        <ErrorBoundary resetKeys={[]}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });

    it('should cleanup timeout on unmount', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      
      const { unmount } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      // Trigger error state first
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      unmount();

      // The ErrorBoundary should clean up any timeouts on unmount
      // Even if no timeout was set, clearTimeout should be safe to call
      expect(() => unmount()).not.toThrow();
      
      clearTimeoutSpy.mockRestore();
    });
  });

  describe('Error Boundary Wrapper', () => {
    it('should render error boundary wrapper with testid when no error', () => {
      render(
        <ErrorBoundary>
          <div>Normal content</div>
        </ErrorBoundary>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });

    it('should not render wrapper testid when error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.queryByTestId('error-boundary')).not.toBeInTheDocument();
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });
  });
});