import { useState, useEffect, useRef, useCallback } from 'react';
import { RepRoomState, ChatM<PERSON><PERSON>, Agent, Human } from '../types/rep-room';

// Connection states
export type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

// WebSocket message payload types
export type WebSocketPayload = 
  | Agent 
  | Human 
  | ChatMessage 
  | Partial<RepRoomState>
  | { participantId: string }
  | { agentId: string; status: string }
  | { timestamp: number };

// WebSocket message types
export interface WebSocketMessage {
  type: 'participant_joined' | 'participant_left' | 'message' | 'state_update' | 'agent_status' | 'ping' | 'pong';
  payload: WebSocketPayload;
  timestamp: number;
  sessionId: string;
}

// Queued message for offline handling
export interface QueuedMessage {
  id: string;
  message: WebSocketMessage;
  timestamp: number;
  retryCount: number;
}

// Sync configuration
export interface SyncConfig {
  wsUrl?: string;
  sessionId: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  messageQueueSize?: number;
}

// Sync state
export interface SyncState {
  connectionState: ConnectionState;
  lastSyncTimestamp: Date | null;
  participantCount: number;
  queuedMessages: QueuedMessage[];
  reconnectAttempts: number;
  error: string | null;
}

// Event handlers
export interface SyncEventHandlers {
  onParticipantJoined?: (participant: Agent | Human) => void;
  onParticipantLeft?: (participantId: string) => void;
  onMessageReceived?: (message: ChatMessage) => void;
  onStateUpdate?: (updates: Partial<RepRoomState>) => void;
  onAgentStatusChange?: (agentId: string, status: string) => void;
  onConnectionStateChange?: (state: ConnectionState) => void;
  onError?: (error: string) => void;
}

const DEFAULT_CONFIG: Required<Omit<SyncConfig, 'sessionId'>> = {
  wsUrl: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/rep-room-sync`,
  reconnectInterval: 3000,
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000,
  messageQueueSize: 100,
};

export const useRepRoomSync = (config: SyncConfig, handlers: SyncEventHandlers = {}) => {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  
  // State
  const [syncState, setSyncState] = useState<SyncState>({
    connectionState: 'disconnected',
    lastSyncTimestamp: null,
    participantCount: 0,
    queuedMessages: [],
    reconnectAttempts: 0,
    error: null,
  });

  // Refs for WebSocket and timers
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messageQueueRef = useRef<QueuedMessage[]>([]);

  // Update sync state helper
  const updateSyncState = useCallback((updates: Partial<SyncState>) => {
    setSyncState(prev => ({ ...prev, ...updates }));
  }, []);

  // Generate unique message ID
  const generateMessageId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Add message to queue
  const queueMessage = useCallback((message: WebSocketMessage) => {
    const queuedMessage: QueuedMessage = {
      id: generateMessageId(),
      message,
      timestamp: Date.now(),
      retryCount: 0,
    };

    messageQueueRef.current.push(queuedMessage);
    
    // Limit queue size
    if (messageQueueRef.current.length > fullConfig.messageQueueSize) {
      messageQueueRef.current = messageQueueRef.current.slice(-fullConfig.messageQueueSize);
    }

    updateSyncState({ queuedMessages: [...messageQueueRef.current] });
  }, [fullConfig.messageQueueSize, generateMessageId, updateSyncState]);

  // Process queued messages
  const processMessageQueue = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      return;
    }

    const messagesToProcess = [...messageQueueRef.current];
    messageQueueRef.current = [];

    messagesToProcess.forEach(queuedMsg => {
      try {
        wsRef.current?.send(JSON.stringify(queuedMsg.message));
      } catch (error) {
        console.error('Failed to send queued message:', error);
        // Re-queue with retry count
        if (queuedMsg.retryCount < 3) {
          messageQueueRef.current.push({
            ...queuedMsg,
            retryCount: queuedMsg.retryCount + 1,
          });
        }
      }
    });

    updateSyncState({ queuedMessages: [...messageQueueRef.current] });
  }, [updateSyncState]);

  // Send message via WebSocket
  const sendMessage = useCallback((type: WebSocketMessage['type'], payload: WebSocketPayload) => {
    const message: WebSocketMessage = {
      type,
      payload,
      timestamp: Date.now(),
      sessionId: fullConfig.sessionId,
    };

    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
        updateSyncState({ lastSyncTimestamp: new Date() });
      } catch (error) {
        console.error('Failed to send message:', error);
        queueMessage(message);
      }
    } else {
      queueMessage(message);
    }
  }, [fullConfig.sessionId, queueMessage, updateSyncState]);

  // Handle incoming WebSocket messages
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      updateSyncState({ lastSyncTimestamp: new Date() });

      switch (message.type) {
        case 'participant_joined':
          handlers.onParticipantJoined?.(message.payload as Agent | Human);
          setSyncState(prev => ({ 
            ...prev, 
            participantCount: prev.participantCount + 1 
          }));
          break;

        case 'participant_left':
          handlers.onParticipantLeft?.((message.payload as { participantId: string }).participantId);
          setSyncState(prev => ({ 
            ...prev, 
            participantCount: Math.max(0, prev.participantCount - 1) 
          }));
          break;

        case 'message':
          handlers.onMessageReceived?.(message.payload as ChatMessage);
          break;

        case 'state_update':
          handlers.onStateUpdate?.(message.payload as Partial<RepRoomState>);
          break;

        case 'agent_status': {
          const statusPayload = message.payload as { agentId: string; status: string };
          handlers.onAgentStatusChange?.(statusPayload.agentId, statusPayload.status);
          break;
        }

        case 'ping':
          // Respond to ping with pong
          sendMessage('pong', { timestamp: Date.now() });
          break;

        case 'pong':
          // Handle pong response (connection is alive)
          break;

        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }, [handlers, sendMessage, updateSyncState]);

  // Setup heartbeat
  const setupHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        sendMessage('ping', { timestamp: Date.now() });
      }
    }, fullConfig.heartbeatInterval);
  }, [fullConfig.heartbeatInterval, sendMessage]);

  // Clear heartbeat
  const clearHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    updateSyncState({ 
      connectionState: 'connecting',
      error: null 
    });

    try {
      const ws = new WebSocket(`${fullConfig.wsUrl}?sessionId=${encodeURIComponent(fullConfig.sessionId)}`);
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        updateSyncState({ 
          connectionState: 'connected',
          reconnectAttempts: 0,
          error: null 
        });
        
        handlers.onConnectionStateChange?.('connected');
        setupHeartbeat();
        processMessageQueue();
      };

      ws.onmessage = handleMessage;

      ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        clearHeartbeat();
        
        if (event.code !== 1000) { // Not a normal closure
          updateSyncState({ connectionState: 'reconnecting' });
          scheduleReconnect();
        } else {
          updateSyncState({ connectionState: 'disconnected' });
          handlers.onConnectionStateChange?.('disconnected');
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        const errorMessage = 'WebSocket connection error';
        updateSyncState({ 
          connectionState: 'error',
          error: errorMessage 
        });
        handlers.onError?.(errorMessage);
        handlers.onConnectionStateChange?.('error');
      };

      wsRef.current = ws;
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      const errorMessage = 'Failed to create WebSocket connection';
      updateSyncState({ 
        connectionState: 'error',
        error: errorMessage 
      });
      handlers.onError?.(errorMessage);
    }
  }, [fullConfig.wsUrl, fullConfig.sessionId, handlers, updateSyncState, setupHeartbeat, processMessageQueue, handleMessage, clearHeartbeat]);

  // Schedule reconnection
  const scheduleReconnect = useCallback(() => {
    if (syncState.reconnectAttempts >= fullConfig.maxReconnectAttempts) {
      updateSyncState({ 
        connectionState: 'error',
        error: 'Maximum reconnection attempts reached' 
      });
      handlers.onError?.('Maximum reconnection attempts reached');
      return;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    reconnectTimeoutRef.current = setTimeout(() => {
      setSyncState(prev => ({ 
        ...prev, 
        reconnectAttempts: prev.reconnectAttempts + 1 
      }));
      connect();
    }, fullConfig.reconnectInterval);
  }, [syncState.reconnectAttempts, fullConfig.maxReconnectAttempts, fullConfig.reconnectInterval, updateSyncState, handlers, connect]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    clearHeartbeat();

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    updateSyncState({ 
      connectionState: 'disconnected',
      reconnectAttempts: 0,
      error: null 
    });
    handlers.onConnectionStateChange?.('disconnected');
  }, [clearHeartbeat, updateSyncState, handlers]);

  // Manual reconnect
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      updateSyncState({ reconnectAttempts: 0 });
      connect();
    }, 100);
  }, [disconnect, connect, updateSyncState]);

  // Sync methods
  const syncMethods = {
    // Send chat message
    sendChatMessage: useCallback((message: ChatMessage) => {
      sendMessage('message', message);
    }, [sendMessage]),

    // Update agent status
    updateAgentStatus: useCallback((agentId: string, status: string) => {
      sendMessage('agent_status', { agentId, status });
    }, [sendMessage]),

    // Sync state updates
    syncStateUpdate: useCallback((updates: Partial<RepRoomState>) => {
      sendMessage('state_update', updates);
    }, [sendMessage]),

    // Join session
    joinSession: useCallback((participant: Agent | Human) => {
      sendMessage('participant_joined', participant);
    }, [sendMessage]),

    // Leave session
    leaveSession: useCallback((participantId: string) => {
      sendMessage('participant_left', { participantId });
    }, [sendMessage]),
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // State
    connectionState: syncState.connectionState,
    lastSyncTimestamp: syncState.lastSyncTimestamp,
    participantCount: syncState.participantCount,
    queuedMessages: syncState.queuedMessages,
    reconnectAttempts: syncState.reconnectAttempts,
    error: syncState.error,
    
    // Connection methods
    connect,
    disconnect,
    reconnect,
    
    // Sync methods
    ...syncMethods,
    
    // Utility
    isConnected: syncState.connectionState === 'connected',
    isConnecting: syncState.connectionState === 'connecting',
    isReconnecting: syncState.connectionState === 'reconnecting',
    hasError: syncState.connectionState === 'error',
  };
};