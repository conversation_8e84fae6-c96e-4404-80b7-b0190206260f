import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SessionChat } from '../../components/rep-room/SessionChat';
import { useChatErrorHandling } from '../../hooks/useChatErrorHandling';
import { useUnifiedVoice } from '../../contexts/rroom/UnifiedVoiceContext';

// Mock dependencies
jest.mock('../../hooks/useChatErrorHandling');
jest.mock('../../contexts/rroom/UnifiedVoiceContext');

// Mock nanoid for consistent session IDs
jest.mock('nanoid', () => ({
  nanoid: jest.fn(() => 'test-message-id')
}));

// Mock scrollIntoView for jsdom environment
Object.defineProperty(Element.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

const mockUseChatErrorHandling = useChatErrorHandling as jest.MockedFunction<typeof useChatErrorHandling>;
const mockUseUnifiedVoice = useUnifiedVoice as jest.MockedFunction<typeof useUnifiedVoice>;

describe('SessionChat', () => {
  const mockProps = {
    sessionId: 'test-session-123',
    config: {
      repRoom: {
        id: 'test-rep-room-id',
        slug: 'test-slug',
        title: 'Test Rep Room',
        settings: {
          voice: {
            enabled: true,
            provider: 'livekit',
            sttProvider: 'deepgram',
            ttsProvider: 'elevenlabs',
            voiceId: 'test-voice-id'
          },
          appearance: {
            themeColor: '#007bff',
            backgroundColor: '#ffffff',
            backgroundType: 'solid'
          },
          behavior: {
            greeting_message: 'Hello! How can I help you today?',
            suggested_prompts: ['Tell me about your services'],
            voice_input_enabled: true,
            file_upload_enabled: false
          }
        }
      },
      agent: {
        id: 'test-agent-id',
        name: 'Test Agent',
        mastraApiBaseUrl: 'https://test.mastra.cloud',
        cloneId: 'test-clone-id'
      }
    },
    onError: jest.fn(),
    className: 'test-chat-class'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set default mock implementations
    mockUseChatErrorHandling.mockReturnValue({
      errorState: {
        failedMessages: new Set(),
        retryingMessages: new Set(),
        errorMessages: new Map()
      },
      markMessageFailed: jest.fn(),
      clearMessageError: jest.fn(),
      retryMessage: jest.fn(),
      sendMessageSafely: jest.fn(),
      clearAllErrors: jest.fn(),
      enhanceMessageWithErrorState: jest.fn(),
      enhanceMessages: jest.fn(),
      getErrorStats: jest.fn(() => ({
        totalFailedMessages: 0,
        currentlyRetrying: 0,
        hasErrors: false,
        hasRetrying: false
      })),
      isMessageFailed: jest.fn(() => false),
      isMessageRetrying: jest.fn(() => false),
      getMessageError: jest.fn(() => undefined)
    });

    mockUseUnifiedVoice.mockReturnValue({
      state: {
        isConnecting: false,
        isListening: false,
        isSpeaking: false,
        connectionError: null,
        isAgentSpeaking: false,
        vadActive: false,
        visibleMessages: [],
        participants: [],
        participantStates: {},
        currentUser: undefined,
        totalParticipants: 0
      },
      controls: {
        connect: jest.fn(),
        disconnect: jest.fn(),
        sendInterruption: jest.fn(),
        on: jest.fn(),
        off: jest.fn()
      },
      isVoiceActive: true
    });
  });

  describe('Component Rendering', () => {
    it('should render chat interface with correct structure', async () => {
      render(<SessionChat {...mockProps} />);
      
      // Check for main chat container
      expect(screen.getByTestId('session-chat')).toBeInTheDocument();
      expect(screen.getByTestId('session-chat')).toHaveClass('test-chat-class');
      
      // Check for messages container
      expect(screen.getByTestId('chat-messages')).toBeInTheDocument();
      
      // Check for input area
      expect(screen.getByTestId('chat-input-area')).toBeInTheDocument();
    });

    it('should display greeting message on initial load', async () => {
      render(<SessionChat {...mockProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Hello! How can I help you today?')).toBeInTheDocument();
      });
    });

    it('should render suggested prompts when enabled', async () => {
      render(<SessionChat {...mockProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Tell me about your services')).toBeInTheDocument();
      });
    });

    it('should not render suggested prompts when disabled', async () => {
      const propsWithoutPrompts = {
        ...mockProps,
        config: {
          ...mockProps.config,
          repRoom: {
            ...mockProps.config.repRoom,
            settings: {
              ...mockProps.config.repRoom.settings,
              behavior: {
                ...mockProps.config.repRoom.settings.behavior,
                suggested_prompts: []
              }
            }
          }
        }
      };

      render(<SessionChat {...propsWithoutPrompts} />);
      
      // Should not find suggested prompts container
      expect(screen.queryByTestId('suggested-prompts')).not.toBeInTheDocument();
    });
  });

  describe('Message Handling', () => {
    it('should send message when input is submitted', async () => {
      const mockSendMessage = jest.fn();
      mockUseChatErrorHandling.mockReturnValue({
        ...mockUseChatErrorHandling(),
        sendMessageSafely: mockSendMessage
      });

      const user = userEvent.setup();
      render(<SessionChat {...mockProps} />);
      
      const input = screen.getByTestId('chat-input');
      const sendButton = screen.getByTestId('send-button');
      
      await user.type(input, 'Hello, this is a test message');
      await user.click(sendButton);
      
      expect(mockSendMessage).toHaveBeenCalledWith('Hello, this is a test message', expect.any(Function));
      expect(input).toHaveValue(''); // Input should be cleared
    });

    it('should send message when Enter key is pressed', async () => {
      const mockSendMessage = jest.fn();
      mockUseChatErrorHandling.mockReturnValue({
        ...mockUseChatErrorHandling(),
        sendMessageSafely: mockSendMessage
      });

      const user = userEvent.setup();
      render(<SessionChat {...mockProps} />);
      
      const input = screen.getByTestId('chat-input');
      
      await user.type(input, 'Test message with Enter key');
      await user.keyboard('{Enter}');
      
      expect(mockSendMessage).toHaveBeenCalledWith('Test message with Enter key', expect.any(Function));
    });

    it('should not send empty messages', async () => {
      const mockSendMessage = jest.fn();
      mockUseChatErrorHandling.mockReturnValue({
        ...mockUseChatErrorHandling(),
        sendMessageSafely: mockSendMessage
      });

      const user = userEvent.setup();
      render(<SessionChat {...mockProps} />);
      
      const sendButton = screen.getByTestId('send-button');
      
      await user.click(sendButton);
      
      expect(mockSendMessage).not.toHaveBeenCalled();
    });

    it('should handle suggested prompt clicks', async () => {
      const mockSendMessage = jest.fn();
      mockUseChatErrorHandling.mockReturnValue({
        ...mockUseChatErrorHandling(),
        sendMessageSafely: mockSendMessage
      });

      const user = userEvent.setup();
      render(<SessionChat {...mockProps} />);
      
      const suggestedPrompt = screen.getByText('Tell me about your services');
      await user.click(suggestedPrompt);
      
      expect(mockSendMessage).toHaveBeenCalledWith('Tell me about your services', expect.any(Function));
    });
  });

  describe('Message Display', () => {
    it('should display user and agent messages correctly', async () => {
      const mockMessages = [
        {
          id: 'msg-1',
          role: 'user' as const,
          content: 'Hello, I need help',
          timestamp: Date.now(),
          isComplete: true
        },
        {
          id: 'msg-2',
          role: 'assistant' as const,
          content: 'Hello! I\'m here to help you.',
          timestamp: Date.now(),
          isComplete: true
        }
      ];

      mockUseUnifiedVoice.mockReturnValue({
        ...mockUseUnifiedVoice(),
        state: {
          ...mockUseUnifiedVoice().state,
          visibleMessages: mockMessages
        }
      });

      render(<SessionChat {...mockProps} />);
      
      expect(screen.getByText('Hello, I need help')).toBeInTheDocument();
      expect(screen.getByText('Hello! I\'m here to help you.')).toBeInTheDocument();
    });

    it('should show typing indicator when agent is responding', async () => {
      mockUseUnifiedVoice.mockReturnValue({
        ...mockUseUnifiedVoice(),
        state: {
          ...mockUseUnifiedVoice().state,
          isAgentSpeaking: true
        }
      });

      render(<SessionChat {...mockProps} />);
      
      expect(screen.getByTestId('typing-indicator')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display error banner when chat error occurs', async () => {
      mockUseChatErrorHandling.mockReturnValue({
        ...mockUseChatErrorHandling(),
        errorState: {
          failedMessages: new Set(['msg-1']),
          retryingMessages: new Set(),
          errorMessages: new Map([['msg-1', 'Failed to send message']])
        },
        getErrorStats: jest.fn(() => ({
          totalFailedMessages: 1,
          currentlyRetrying: 0,
          hasErrors: true,
          hasRetrying: false
        }))
      });

      render(<SessionChat {...mockProps} />);
      
      expect(screen.getByTestId('chat-error-banner')).toBeInTheDocument();
      expect(screen.getByText('Failed to send message')).toBeInTheDocument();
    });

    it('should show retry button when error is retryable', async () => {
      const mockRetryMessage = jest.fn();
      mockUseChatErrorHandling.mockReturnValue({
        ...mockUseChatErrorHandling(),
        errorState: {
          failedMessages: new Set(['msg-1']),
          retryingMessages: new Set(),
          errorMessages: new Map([['msg-1', 'Failed to send message']])
        },
        retryMessage: mockRetryMessage,
        getErrorStats: jest.fn(() => ({
          totalFailedMessages: 1,
          currentlyRetrying: 0,
          hasErrors: true,
          hasRetrying: false
        }))
      });

      const user = userEvent.setup();
      render(<SessionChat {...mockProps} />);
      
      const retryButton = screen.getByTestId('retry-button');
      await user.click(retryButton);
      
      expect(mockRetryMessage).toHaveBeenCalled();
    });

    it('should call onError prop when critical error occurs', async () => {
      const mockOnError = jest.fn();
      mockUseUnifiedVoice.mockReturnValue({
        ...mockUseUnifiedVoice(),
        state: {
          ...mockUseUnifiedVoice().state,
          connectionError: 'Connection failed'
        }
      });

      render(<SessionChat {...mockProps} onError={mockOnError} />);
      
      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('Connection failed');
      });
    });
  });

  describe('Voice Integration', () => {
    it('should show voice input button when voice is enabled', async () => {
      render(<SessionChat {...mockProps} />);
      
      expect(screen.getByTestId('voice-input-button')).toBeInTheDocument();
    });

    it('should not show voice input button when voice is disabled', async () => {
      const propsWithoutVoice = {
        ...mockProps,
        config: {
          ...mockProps.config,
          repRoom: {
            ...mockProps.config.repRoom,
            settings: {
              ...mockProps.config.repRoom.settings,
              behavior: {
                ...mockProps.config.repRoom.settings.behavior,
                voice_input_enabled: false
              }
            }
          }
        }
      };

      render(<SessionChat {...propsWithoutVoice} />);
      
      expect(screen.queryByTestId('voice-input-button')).not.toBeInTheDocument();
    });

    it('should show connection status indicator', async () => {
      render(<SessionChat {...mockProps} />);
      
      expect(screen.getByTestId('connection-indicator')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', async () => {
      render(<SessionChat {...mockProps} />);
      
      expect(screen.getByRole('log')).toBeInTheDocument(); // Messages container
      expect(screen.getByRole('textbox')).toBeInTheDocument(); // Input field
      expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<SessionChat {...mockProps} />);
      
      const input = screen.getByTestId('chat-input');
      const voiceButton = screen.getByTestId('voice-input-button');
      const sendButton = screen.getByTestId('send-button');
      
      // Tab to input
      await user.tab();
      expect(input).toHaveFocus();
      
      // Tab to voice button (when voice is enabled)
      await user.tab();
      expect(voiceButton).toHaveFocus();
      
      // Tab to send button
      await user.tab();
      expect(sendButton).toHaveFocus();
    });
  });
});