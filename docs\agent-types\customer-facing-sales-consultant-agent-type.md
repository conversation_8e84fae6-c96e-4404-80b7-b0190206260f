# Customer-Facing Sales Consultant Agent Type

A comprehensive specification for creating a customer-facing sales consultant agent type designed for direct customer interaction, product discovery, and sales conversion optimization.

## Table of Contents

1. [Overview](#overview)
2. [Agent Type Specification](#agent-type-specification)
3. [Configuration Schema](#configuration-schema)
4. [Field Values and Defaults](#field-values-and-defaults)
5. [Runtime Context Configuration](#runtime-context-configuration)
6. [Presentation and UI Settings](#presentation-and-ui-settings)
7. [Channel Configuration](#channel-configuration)
8. [Customer Engagement Features](#customer-engagement-features)
9. [E-commerce Integration](#e-commerce-integration)
10. [Trust-Building Configuration](#trust-building-configuration)
11. [Business Type Examples](#business-type-examples)
12. [Implementation Guide](#implementation-guide)
13. [Conversion Optimization](#conversion-optimization)
14. [Best Practices](#best-practices)

---

## Overview

The Customer-Facing Sales Consultant Agent Type is specifically designed for direct customer interaction, focusing on customer engagement, product discovery, and sales conversion. Unlike internal training agents, this agent is optimized for customer-facing scenarios where trust-building, personalized recommendations, and smooth purchasing experiences are paramount.

### Key Features

- **Customer-Centric Design**: Optimized for direct customer interaction and engagement
- **Product Discovery Engine**: Intelligent product/service matching based on customer needs
- **Conversion Optimization**: Advanced techniques to guide customers through the purchase journey
- **Trust-Building Mechanisms**: Transparency, social proof, and credibility features
- **Personalized Recommendations**: AI-driven product suggestions based on customer profile
- **Multi-Channel Customer Support**: Seamless experience across web, mobile, and voice
- **E-commerce Integration**: Direct integration with shopping carts and booking systems
- **Real-time Inventory**: Live product availability and pricing information

### Target Use Cases

1. **Product Discovery**: Help customers find products/services that match their needs
2. **Purchase Guidance**: Guide customers through the buying process step-by-step
3. **Objection Handling**: Address customer concerns and hesitations professionally
4. **Upselling/Cross-selling**: Suggest complementary products and upgrades
5. **Customer Support**: Provide pre-sales and post-sales assistance
6. **Appointment Booking**: Schedule consultations, demos, or service appointments

### Key Differentiators from Internal Sales Training Agents

- **Customer-First Language**: Uses customer-friendly terminology, not sales jargon
- **Transparency Focus**: Emphasizes honest, transparent communication
- **Conversion Optimization**: Built-in features to reduce cart abandonment and increase conversions
- **Trust Signals**: Incorporates reviews, testimonials, and credibility indicators
- **Purchase Facilitation**: Direct integration with payment and booking systems
- **Customer Journey Mapping**: Tracks and optimizes the entire customer experience

---

## Agent Type Specification

### Core Agent Type Definition

```json
{
  "id": "customerFacingSalesConsultant",
  "name": "Customer-Facing Sales Consultant",
  "description": "AI sales consultant optimized for direct customer interaction, product discovery, and conversion optimization with focus on trust-building and personalized recommendations",
  "version": "1.0.0",
  "status": "published",
  "category": "customer_engagement",
  "is_public": true,
  "created_by_org_id": "house-org-uuid",
  "mastra_agent_id": "customer-facing-sales-consultant-v1",
  "mastra_api_base_url": "https://mastra-customer-sales-runtime.example.com",
  "agent_type": "customer_facing"
}
```

### Required Database Fields

Based on the [`agents`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1262) table schema:

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | text | ✅ | Unique identifier: "customerFacingSalesConsultant" |
| `name` | text | ✅ | Display name: "Customer-Facing Sales Consultant" |
| `description` | text | ✅ | Customer-focused agent description |
| `version` | text | ✅ | Version tracking (default: "1.0.0") |
| `status` | agent_status | ✅ | Lifecycle status (default: "published") |
| `configuration` | jsonb | ✅ | Main configuration object |
| `created_by_org_id` | uuid | ✅ | House organization UUID |
| `mastra_agent_id` | text | ✅ | CopilotKit agent identifier |
| `mastra_api_base_url` | text | ✅ | Runtime URL for Mastra agent |
| `agent_type` | text | ✅ | Classification: "customer_facing" |
| `category` | text | ❌ | Agent category: "customer_engagement" |
| `pricing` | jsonb | ❌ | Pricing configuration |
| `voice_config` | jsonb | ❌ | Voice interaction settings |
| `chat_ui_settings` | jsonb | ❌ | Customer chat interface configuration |
| `phone_settings` | jsonb | ❌ | Customer phone support settings |
| `applicable_metrics` | jsonb | ❌ | Customer engagement metrics tracking |

---

## Configuration Schema

### Primary Configuration Schema

The configuration schema defines parameters for customer-facing sales optimization:

```json
{
  "configuration_schema": {
    "type": "object",
    "properties": {
      "business_name": {
        "type": "string",
        "title": "Business Name",
        "description": "Your business name as customers will see it",
        "minLength": 1,
        "maxLength": 100
      },
      "business_type": {
        "type": "string",
        "title": "Business Type",
        "description": "Type of business for customer interaction optimization",
        "enum": [
          "ecommerce_retail",
          "saas_software",
          "professional_services",
          "healthcare_services",
          "real_estate",
          "financial_services",
          "education_training",
          "hospitality_travel",
          "automotive",
          "home_services",
          "b2b_marketplace",
          "subscription_service"
        ]
      },
      "primary_offerings": {
        "type": "array",
        "title": "Primary Products/Services",
        "description": "Main products or services offered to customers",
        "items": {
          "type": "object",
          "properties": {
            "name": {"type": "string"},
            "category": {"type": "string"},
            "price_range": {"type": "string"},
            "key_benefits": {"type": "array", "items": {"type": "string"}},
            "target_audience": {"type": "string"}
          }
        },
        "minItems": 1,
        "maxItems": 20
      },
      "customer_interaction_style": {
        "type": "object",
        "title": "Customer Interaction Style",
        "description": "How the agent should interact with customers",
        "properties": {
          "tone": {
            "type": "string",
            "enum": ["helpful", "friendly", "professional", "enthusiastic", "consultative"],
            "default": "helpful"
          },
          "communication_approach": {
            "type": "string",
            "enum": ["solution_focused", "relationship_building", "educational", "direct", "consultative"],
            "default": "solution_focused"
          },
          "formality_level": {
            "type": "string",
            "enum": ["casual", "friendly_professional", "business_professional", "formal"],
            "default": "friendly_professional"
          },
          "response_style": {
            "type": "string",
            "enum": ["concise", "detailed", "conversational", "structured"],
            "default": "conversational"
          }
        }
      },
      "customer_journey_optimization": {
        "type": "object",
        "title": "Customer Journey Settings",
        "description": "Optimization settings for customer purchase journey",
        "properties": {
          "discovery_approach": {
            "type": "string",
            "enum": ["needs_assessment", "product_showcase", "problem_solving", "benefit_focused"],
            "default": "needs_assessment"
          },
          "objection_handling_style": {
            "type": "string",
            "enum": ["empathetic", "educational", "reassuring", "transparent"],
            "default": "empathetic"
          },
          "urgency_creation": {
            "type": "boolean",
            "default": false,
            "description": "Whether to create appropriate urgency for purchase decisions"
          },
          "social_proof_emphasis": {
            "type": "boolean",
            "default": true,
            "description": "Emphasize customer reviews, testimonials, and social proof"
          }
        }
      },
      "personalization_settings": {
        "type": "object",
        "title": "Personalization Configuration",
        "description": "Settings for personalizing customer interactions",
        "properties": {
          "use_customer_name": {
            "type": "boolean",
            "default": true,
            "description": "Use customer's name in conversations when available"
          },
          "remember_preferences": {
            "type": "boolean",
            "default": true,
            "description": "Remember customer preferences across sessions"
          },
          "behavioral_adaptation": {
            "type": "boolean",
            "default": true,
            "description": "Adapt communication style based on customer behavior"
          },
          "recommendation_engine": {
            "type": "string",
            "enum": ["collaborative_filtering", "content_based", "hybrid", "rule_based"],
            "default": "hybrid"
          }
        }
      },
      "trust_building_features": {
        "type": "object",
        "title": "Trust Building Configuration",
        "description": "Features to build customer trust and credibility",
        "properties": {
          "show_credentials": {
            "type": "boolean",
            "default": true,
            "description": "Display business credentials and certifications"
          },
          "display_reviews": {
            "type": "boolean",
            "default": true,
            "description": "Show customer reviews and ratings"
          },
          "transparency_mode": {
            "type": "string",
            "enum": ["high", "medium", "standard"],
            "default": "high",
            "description": "Level of transparency in pricing and processes"
          },
          "guarantee_information": {
            "type": "boolean",
            "default": true,
            "description": "Highlight guarantees, warranties, and return policies"
          }
        }
      },
      "conversion_optimization": {
        "type": "object",
        "title": "Conversion Optimization Settings",
        "description": "Settings to optimize customer conversion rates",
        "properties": {
          "cart_abandonment_prevention": {
            "type": "boolean",
            "default": true,
            "description": "Proactively address cart abandonment"
          },
          "price_sensitivity_handling": {
            "type": "string",
            "enum": ["value_emphasis", "payment_options", "comparison_focus", "roi_demonstration"],
            "default": "value_emphasis"
          },
          "decision_support_tools": {
            "type": "array",
            "items": {
              "type": "string",
              "enum": ["comparison_charts", "roi_calculator", "sizing_guide", "compatibility_checker"]
            },
            "default": ["comparison_charts"]
          },
          "follow_up_strategy": {
            "type": "string",
            "enum": ["immediate", "gentle_reminder", "value_reinforcement", "limited_time_offer"],
            "default": "value_reinforcement"
          }
        }
      },
      "integration_settings": {
        "type": "object",
        "title": "System Integration Settings",
        "description": "Integration with e-commerce and business systems",
        "properties": {
          "ecommerce_platform": {
            "type": "string",
            "enum": ["shopify", "woocommerce", "magento", "bigcommerce", "custom", "none"],
            "default": "none"
          },
          "inventory_integration": {
            "type": "boolean",
            "default": false,
            "description": "Real-time inventory checking"
          },
          "pricing_integration": {
            "type": "boolean",
            "default": false,
            "description": "Dynamic pricing from business systems"
          },
          "booking_system": {
            "type": "string",
            "enum": ["calendly", "acuity", "custom", "none"],
            "default": "none"
          },
          "payment_processing": {
            "type": "boolean",
            "default": false,
            "description": "Direct payment processing capability"
          }
        }
      },
      "customer_data_handling": {
        "type": "object",
        "title": "Customer Data and Privacy",
        "description": "Customer data handling and privacy settings",
        "properties": {
          "data_collection_consent": {
            "type": "boolean",
            "default": true,
            "description": "Require explicit consent for data collection"
          },
          "privacy_transparency": {
            "type": "string",
            "enum": ["full_disclosure", "summary", "minimal"],
            "default": "full_disclosure"
          },
          "data_retention_policy": {
            "type": "integer",
            "minimum": 30,
            "maximum": 1095,
            "default": 365,
            "description": "Customer data retention period in days"
          },
          "gdpr_compliance": {
            "type": "boolean",
            "default": true,
            "description": "GDPR compliance for EU customers"
          }
        }
      }
    },
    "required": [
      "business_name",
      "business_type",
      "primary_offerings",
      "customer_interaction_style"
    ]
  }
}
```

---

## Field Values and Defaults

### Default Configuration Values

```json
{
  "default_config_values": {
    "business_name": "",
    "business_type": "ecommerce_retail",
    "primary_offerings": [],
    "customer_interaction_style": {
      "tone": "helpful",
      "communication_approach": "solution_focused",
      "formality_level": "friendly_professional",
      "response_style": "conversational"
    },
    "customer_journey_optimization": {
      "discovery_approach": "needs_assessment",
      "objection_handling_style": "empathetic",
      "urgency_creation": false,
      "social_proof_emphasis": true
    },
    "personalization_settings": {
      "use_customer_name": true,
      "remember_preferences": true,
      "behavioral_adaptation": true,
      "recommendation_engine": "hybrid"
    },
    "trust_building_features": {
      "show_credentials": true,
      "display_reviews": true,
      "transparency_mode": "high",
      "guarantee_information": true
    },
    "conversion_optimization": {
      "cart_abandonment_prevention": true,
      "price_sensitivity_handling": "value_emphasis",
      "decision_support_tools": ["comparison_charts"],
      "follow_up_strategy": "value_reinforcement"
    },
    "integration_settings": {
      "ecommerce_platform": "none",
      "inventory_integration": false,
      "pricing_integration": false,
      "booking_system": "none",
      "payment_processing": false
    },
    "customer_data_handling": {
      "data_collection_consent": true,
      "privacy_transparency": "full_disclosure",
      "data_retention_policy": 365,
      "gdpr_compliance": true
    }
  }
}
```

### Business Type Optimizations

#### E-commerce Retail
```json
{
  "business_type": "ecommerce_retail",
  "customer_interaction_style": {
    "tone": "enthusiastic",
    "communication_approach": "solution_focused",
    "response_style": "conversational"
  },
  "conversion_optimization": {
    "cart_abandonment_prevention": true,
    "decision_support_tools": ["comparison_charts", "sizing_guide"],
    "follow_up_strategy": "limited_time_offer"
  }
}
```

#### SaaS Software
```json
{
  "business_type": "saas_software",
  "customer_interaction_style": {
    "tone": "consultative",
    "communication_approach": "educational",
    "response_style": "detailed"
  },
  "conversion_optimization": {
    "decision_support_tools": ["roi_calculator", "comparison_charts"],
    "price_sensitivity_handling": "roi_demonstration"
  }
}
```

#### Professional Services
```json
{
  "business_type": "professional_services",
  "customer_interaction_style": {
    "tone": "professional",
    "communication_approach": "relationship_building",
    "formality_level": "business_professional"
  },
  "trust_building_features": {
    "show_credentials": true,
    "transparency_mode": "high"
  }
}
```

---

## Runtime Context Configuration

### Runtime Context Schema

```json
{
  "runtime_context_schema": {
    "type": "object",
    "properties": {
      "customer_profile": {
        "type": "object",
        "properties": {
          "customer_id": {"type": "string"},
          "name": {"type": "string"},
          "email": {"type": "string"},
          "phone": {"type": "string"},
          "location": {"type": "string"},
          "customer_segment": {"type": "string"},
          "lifetime_value": {"type": "number"},
          "purchase_history": {"type": "array"},
          "preferences": {"type": "object"},
          "interaction_history": {"type": "array"}
        }
      },
      "session_context": {
        "type": "object",
        "properties": {
          "session_id": {"type": "string"},
          "channel": {"type": "string"},
          "device_type": {"type": "string"},
          "referral_source": {"type": "string"},
          "current_page": {"type": "string"},
          "cart_contents": {"type": "array"},
          "browsing_history": {"type": "array"},
          "time_on_site": {"type": "integer"}
        }
      },
      "business_context": {
        "type": "object",
        "properties": {
          "current_promotions": {"type": "array"},
          "inventory_status": {"type": "object"},
          "pricing_rules": {"type": "object"},
          "business_hours": {"type": "object"},
          "seasonal_factors": {"type": "object"},
          "competitive_landscape": {"type": "object"}
        }
      },
      "conversation_state": {
        "type": "object",
        "properties": {
          "stage": {
            "type": "string",
            "enum": ["discovery", "exploration", "consideration", "decision", "purchase", "post_purchase"]
          },
          "identified_needs": {"type": "array"},
          "presented_solutions": {"type": "array"},
          "objections_raised": {"type": "array"},
          "decision_factors": {"type": "array"},
          "next_steps": {"type": "array"}
        }
      },
      "personalization_data": {
        "type": "object",
        "properties": {
          "communication_style_preference": {"type": "string"},
          "information_depth_preference": {"type": "string"},
          "decision_making_style": {"type": "string"},
          "price_sensitivity": {"type": "string"},
          "trust_indicators": {"type": "array"}
        }
      }
    }
  }
}
```

---

## Presentation and UI Settings

### Customer-Facing Presentation Configuration

```json
{
  "presentation_config": {
    "avatar": {
      "type": "friendly_professional",
      "style": "customer_service_representative",
      "color_scheme": "warm_trustworthy",
      "gender_preference": "neutral",
      "approachability": "high"
    },
    "personality": {
      "tone": "helpful_enthusiastic",
      "communication_style": "customer_centric",
      "expertise_level": "knowledgeable_guide",
      "empathy_level": "very_high",
      "patience_level": "unlimited"
    },
    "interface_elements": {
      "show_product_recommendations": true,
      "display_customer_reviews": true,
      "include_price_comparisons": true,
      "show_availability_status": true,
      "display_trust_badges": true,
      "include_guarantee_info": true,
      "show_social_proof": true,
      "display_security_indicators": true
    },
    "branding": {
      "primary_color": "#2563eb",
      "accent_color": "#10b981",
      "trust_color": "#059669",
      "warning_color": "#f59e0b",
      "logo_position": "top_center",
      "custom_styling": true,
      "brand_consistency": "high"
    }
  }
}
```

### Customer Chat UI Settings

```json
{
  "chat_ui_settings": {
    "welcome_message": "Hi there! 👋 I'm here to help you find exactly what you're looking for. Whether you have questions about our products, need recommendations, or want to make a purchase, I'm here to make your experience as smooth as possible. What can I help you with today?",
    "placeholder_text": "Ask me about products, pricing, availability, or anything else...",
    "suggested_prompts": [
      "What products do you recommend for me?",
      "Can you help me compare these options?",
      "What's your return policy?",
      "Do you have any current promotions?",
      "I need help choosing the right size/option",
      "Can you check if this is in stock?"
    ],
    "quick_actions": [
      {
        "label": "Browse Products",
        "action": "product_catalog",
        "icon": "shopping-bag"
      },
      {
        "label": "Get Recommendations",
        "action": "personalized_recommendations",
        "icon": "star"
      },
      {
        "label": "Check Cart",
        "action": "view_cart",
        "icon": "shopping-cart"
      },
      {
        "label": "Track Order",
        "action": "order_tracking",
        "icon": "truck"
      },
      {
        "label": "Contact Support",
        "action": "human_handoff",
        "icon": "phone"
      }
    ],
    "conversation_starters": [
      "I'm looking for something specific but not sure what's available",
      "I saw this product and want to know more about it",
      "I'm comparing a few options and need help deciding",
      "I have questions about shipping and returns"
    ],
    "trust_indicators": [
      "🔒 Secure checkout",
      "📦 Free shipping on orders over $X",
      "↩️ 30-day return policy",
      "⭐ 4.8/5 customer rating",
      "🏆 Award-winning customer service"
    ]
  }
}
```

---

## Channel Configuration

### Customer-Facing Channel Configuration

```json
{
  "available_channels": ["web_chat", "mobile_app", "voice", "phone", "email", "social_media"],
  "channel_specific_config": {
    "web_chat": {
      "proactive_engagement": true,
      "exit_intent_detection": true,
      "cart_abandonment_alerts": true,
      "product_page_assistance": true,
      "checkout_support": true,
      "rich_media_support": true,
      "file_sharing": true,
      "screen_sharing": false
    },
    "mobile_app": {
      "push_notifications": true,
      "location_based_offers": true,
      "camera_integration": true,
      "barcode_scanning": true,
      "voice_search": true,
      "offline_mode": true
    },
    "voice": {
      "natural_conversation": true,
      "product_search_by_voice": true,
      "order_placement": true,
      "customer_service": true,
      "multilingual_support": true,
      "accent_adaptation": true
    },
    "phone": {
      "human_handoff": true,
      "callback_scheduling": true,
      "order_support": true,
      "technical_support": true,
      "complaint_handling": true,
      "satisfaction_surveys": true
    },
    "email": {
      "automated_responses": true,
      "personalized_recommendations": true,
      "order_confirmations": true,
      "shipping_updates": true,
      "follow_up_sequences": true,
      "newsletter_integration": true
    },
    "social_media": {
      "platform_integration": ["facebook", "instagram", "twitter", "whatsapp"],
      "social_commerce": true,
      "influencer_partnerships": false,
      "user_generated_content": true
    }
  }
}
```

---

## Customer Engagement Features

### Advanced Customer Engagement Configuration

```json
{
  "customer_engagement_features": {
    "proactive_assistance": {
      "enabled": true,
      "triggers": [
        "time_on_page_threshold",
        "scroll_behavior",
        "exit_intent",
        "cart_abandonment",
        "product_comparison",
        "pricing_page_visit"
      ],
      "engagement_messages": {
        "browsing_assistance": "I noticed you're browsing our products. Can I help you find something specific?",
        "cart_abandonment": "I see you have items in your cart. Do you have any questions before completing your purchase?",
        "comparison_help": "Comparing products? I can help you understand the differences and find the best fit for your needs.",
        "pricing_inquiry": "Have questions about pricing or payment options? I'm here to help!"
      }
    },
    "personalized_recommendations": {
      "enabled": true,
      "recommendation_types": [
        "similar_products",
        "frequently_bought_together",
        "based_on_browsing_history",
        "trending_products",
        "seasonal_recommendations",
        "price_range_matches"
      ],
      "recommendation_timing": [
        "product_page_view",
        "cart_review",
        "checkout_process",
        "post_purchase",
        "return_visit"
      ]
    },
    "social_proof_integration": {
      "enabled": true,
      "proof_types": [
        "customer_reviews",
        "ratings_display",
        "purchase_notifications",
        "testimonials",
        "expert_endorsements",
        "media_mentions",
        "certification_badges"
      ],
      "display_rules": {
        "minimum_rating": 4.0,
        "recent_reviews_priority": true,
        "verified_purchases_only": true,
        "balanced_feedback": true
      }
    },
    "urgency_and_scarcity": {
      "enabled": false,
      "ethical_guidelines": true,
      "scarcity_indicators": [
        "low_stock_alerts",
        "limited_time_offers",
        "exclusive_deals",
        "seasonal_availability"
      ],
      "urgency_triggers": [
        "sale_ending_soon",
        "price_increase_notification",
        "limited_quantity",
        "high_demand_alert"
      ]
    }
  }
}
```

---

## E-commerce Integration

### E-commerce Platform Integration Configuration

```json
{
  "ecommerce_integration": {
    "supported_platforms": [
      "shopify",
      "woocommerce",
      "magento",
      "bigcommerce",
      "prestashop",
      "opencart",
      "custom_api"
    ],
    "integration_capabilities": {
      "product_catalog_sync": true,
      "inventory_management": true,
      "pricing_updates": true,
      "order_processing": true,
      "customer_data_sync": true,
      "payment_processing": true,
      "shipping_calculation": true,
      "tax_calculation": true
    },
    "real_time_features": {
      "inventory_checking": true,
      "price_updates": true,
      "promotion_application": true,
      "shipping_rates": true,
      "payment_processing": true,
      "order_confirmation": true
    },
    "cart_management": {
      "add_to_cart": true,
      "modify_quantities": true,
      "remove_items": true,
      "save_for_later": true,
      "wishlist_integration": true,
      "cart_sharing": true,
      "abandoned_cart_recovery": true
    },
    "checkout_assistance": {
      "guided_checkout": true,
      "form_completion_help": true,
      "payment_method_guidance": true,
      "shipping_option_explanation": true,
      "error_resolution": true,
      "security_assurance": true
    }
  }
}
```

### Booking System Integration

```json
{
  "booking_integration": {
    "supported_systems": [
      "calendly",
      "acuity_scheduling",
      "bookly",
      "setmore",
      "square_appointments",
      "custom_booking_api"
    ],
    "booking_capabilities": {
      "availability_checking": true,
      "appointment_scheduling": true,
      "service_selection": true,
      "staff_selection": true,
      "time_slot_booking": true,
      "recurring_appointments": true,
      "group_bookings": true
    },
    "customer_experience": {
      "instant_confirmation": true,
      "calendar_integration": true,
      "reminder_notifications": true,
      "rescheduling_options": true,
      "cancellation_handling": true,
      "waitlist_management": true
    }
  }
}
```

---

## Trust-Building Configuration

### Trust and Credibility Features

```json
{
  "trust_building_configuration": {
    "transparency_features": {
      "pricing_transparency": {
        "show_all_costs": true,
        "no_hidden_fees": true,
        "tax_inclusion_clarity": true,
        "shipping_cost_upfront": true,
        "payment_fee_disclosure": true
      },
      "business_transparency": {
        "company_information": true,
        "contact_details": true,
        "business_registration": true,
        "physical_address": true,
        "business_hours": true,
        "team_information": false
      },
      "process_transparency": {
        "order_processing_timeline": true,
        "shipping_information": true,
        "return_process": true,
        "refund_policy": true,
        "data_usage_policy": true
      }
    },
    "credibility_indicators": {
      "certifications": {
        "display_certificates": true,
        "industry_memberships": true,
        "quality_standards": true,
        "security_certifications": true
      },
      "awards_recognition": {
        "industry_awards": true,
        "customer_choice_awards": true,
        "media_recognition": true,
        "expert_endorsements": true
      },
      "social_proof": {
        "customer_count": true,
        "years_in_business": true,
        "successful_projects": true,
        "customer_satisfaction_rate": true
      }
    },
    "security_assurance": {
      "data_protection": {
        "ssl_certificates": true,
        "encryption_standards": true,
        "privacy_policy": true,
        "gdpr_compliance": true,
        "data_breach_protection": true
      },
      "payment_security": {
        "secure_payment_gateways": true,
"pci_compliance": true,
        "fraud_protection": true,
        "secure_tokenization": true,
        "payment_verification": true
      }
    },
    "guarantee_policies": {
      "money_back_guarantee": {
        "enabled": true,
        "duration_days": 30,
        "conditions": "clear",
        "process_simplicity": "high"
      },
      "satisfaction_guarantee": {
        "enabled": true,
        "replacement_policy": true,
        "service_guarantee": true,
        "quality_assurance": true
      },
      "warranty_information": {
        "product_warranties": true,
        "extended_warranties": true,
        "warranty_terms": "clear",
        "claim_process": "simple"
      }
    }
  }
}
```

---

## Business Type Examples

### Example 1: E-commerce Fashion Retailer

```json
{
  "business_configuration": {
    "business_name": "StyleHub Fashion",
    "business_type": "ecommerce_retail",
    "primary_offerings": [
      {
        "name": "Women's Clothing",
        "category": "fashion",
        "price_range": "$25-$200",
        "key_benefits": ["trendy designs", "quality materials", "size inclusive"],
        "target_audience": "women_18_45"
      },
      {
        "name": "Accessories",
        "category": "fashion_accessories",
        "price_range": "$10-$75",
        "key_benefits": ["statement pieces", "versatile", "affordable luxury"],
        "target_audience": "fashion_conscious"
      }
    ],
    "customer_interaction_style": {
      "tone": "enthusiastic",
      "communication_approach": "solution_focused",
      "formality_level": "casual",
      "response_style": "conversational"
    },
    "conversion_optimization": {
      "cart_abandonment_prevention": true,
      "decision_support_tools": ["sizing_guide", "comparison_charts"],
      "follow_up_strategy": "limited_time_offer",
      "price_sensitivity_handling": "value_emphasis"
    },
    "trust_building_features": {
      "display_reviews": true,
      "guarantee_information": true,
      "transparency_mode": "high"
    }
  },
  "specialized_features": {
    "virtual_try_on": true,
    "style_recommendations": true,
    "seasonal_collections": true,
    "size_finder": true,
    "outfit_builder": true
  }
}
```

### Example 2: SaaS Project Management Tool

```json
{
  "business_configuration": {
    "business_name": "ProjectFlow Pro",
    "business_type": "saas_software",
    "primary_offerings": [
      {
        "name": "Team Collaboration Suite",
        "category": "productivity_software",
        "price_range": "$12-$49/user/month",
        "key_benefits": ["increased productivity", "better communication", "project visibility"],
        "target_audience": "business_teams"
      },
      {
        "name": "Enterprise Solution",
        "category": "enterprise_software",
        "price_range": "custom_pricing",
        "key_benefits": ["advanced security", "custom integrations", "dedicated support"],
        "target_audience": "large_organizations"
      }
    ],
    "customer_interaction_style": {
      "tone": "consultative",
      "communication_approach": "educational",
      "formality_level": "business_professional",
      "response_style": "detailed"
    },
    "conversion_optimization": {
      "decision_support_tools": ["roi_calculator", "comparison_charts"],
      "price_sensitivity_handling": "roi_demonstration",
      "follow_up_strategy": "value_reinforcement"
    },
    "integration_settings": {
      "booking_system": "calendly",
      "ecommerce_platform": "custom"
    }
  },
  "specialized_features": {
    "free_trial": true,
    "demo_scheduling": true,
    "feature_comparison": true,
    "roi_calculator": true,
    "integration_showcase": true,
    "case_studies": true
  }
}
```

### Example 3: Professional Dental Services

```json
{
  "business_configuration": {
    "business_name": "Bright Smile Dental",
    "business_type": "healthcare_services",
    "primary_offerings": [
      {
        "name": "General Dentistry",
        "category": "healthcare",
        "price_range": "$100-$500",
        "key_benefits": ["preventive care", "pain relief", "oral health"],
        "target_audience": "families"
      },
      {
        "name": "Cosmetic Dentistry",
        "category": "cosmetic_healthcare",
        "price_range": "$500-$5000",
        "key_benefits": ["beautiful smile", "confidence boost", "modern techniques"],
        "target_audience": "adults_seeking_cosmetic_improvement"
      }
    ],
    "customer_interaction_style": {
      "tone": "professional",
      "communication_approach": "consultative",
      "formality_level": "business_professional",
      "response_style": "detailed"
    },
    "trust_building_features": {
      "show_credentials": true,
      "display_reviews": true,
      "transparency_mode": "high",
      "guarantee_information": true
    },
    "integration_settings": {
      "booking_system": "acuity",
      "payment_processing": true
    }
  },
  "specialized_features": {
    "appointment_scheduling": true,
    "insurance_verification": true,
    "treatment_planning": true,
    "before_after_gallery": true,
    "patient_education": true,
    "emergency_scheduling": true
  }
}
```

### Example 4: Real Estate Agency

```json
{
  "business_configuration": {
    "business_name": "Premier Properties Group",
    "business_type": "real_estate",
    "primary_offerings": [
      {
        "name": "Home Buying Services",
        "category": "real_estate_sales",
        "price_range": "commission_based",
        "key_benefits": ["expert guidance", "market knowledge", "negotiation skills"],
        "target_audience": "home_buyers"
      },
      {
        "name": "Property Management",
        "category": "real_estate_services",
        "price_range": "8-12% monthly rent",
        "key_benefits": ["hassle-free ownership", "tenant screening", "maintenance coordination"],
        "target_audience": "property_investors"
      }
    ],
    "customer_interaction_style": {
      "tone": "professional",
      "communication_approach": "relationship_building",
      "formality_level": "business_professional",
      "response_style": "detailed"
    },
    "trust_building_features": {
      "show_credentials": true,
      "display_reviews": true,
      "transparency_mode": "high"
    },
    "integration_settings": {
      "booking_system": "calendly",
      "ecommerce_platform": "custom"
    }
  },
  "specialized_features": {
    "property_search": true,
    "market_analysis": true,
    "mortgage_calculator": true,
    "virtual_tours": true,
    "neighborhood_information": true,
    "appointment_scheduling": true
  }
}
```

---

## Implementation Guide

### Step 1: Create the Customer-Facing Agent Type

```typescript
const customerFacingSalesConsultantData = {
  id: "customerFacingSalesConsultant",
  name: "Customer-Facing Sales Consultant",
  description: "AI sales consultant optimized for direct customer interaction, product discovery, and conversion optimization with focus on trust-building and personalized recommendations",
  version: "1.0.0",
  status: "draft",
  category: "customer_engagement",
  is_public: true,
  created_by_org_id: "house-org-uuid",
  mastra_agent_id: "customer-facing-sales-consultant-v1",
  mastra_api_base_url: "https://mastra-customer-sales-runtime.example.com",
  agent_type: "customer_facing",
  configuration: {
    configuration_schema: { /* Complete schema from above */ },
    runtime_context_schema: { /* Runtime context schema */ },
    default_config_values: { /* Default values */ },
    presentation_config: { /* Customer-facing presentation */ },
    available_channels: ["web_chat", "mobile_app", "voice", "phone", "email"],
    avatar_type: "friendly_professional",
    capabilities: [
      "product_discovery",
      "personalized_recommendations",
      "objection_handling",
      "purchase_guidance",
      "cart_management",
      "appointment_booking",
      "customer_support",
      "trust_building",
      "conversion_optimization"
    ],
    agent_operational_mode: "customer_facing_interactive"
  }
};

// Create the agent type
const response = await fetch('/functions/v1/agent-types-create', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(customerFacingSalesConsultantData)
});
```

### Step 2: Configure Customer-Facing Pricing

```json
{
  "pricing": {
    "credits_per_completion_token": 0.002,
    "credits_per_prompt_token": 0.001,
    "customer_facing_features": {
      "personalized_recommendations": 0.005,
      "real_time_inventory": 0.003,
      "advanced_search": 0.002,
      "booking_integration": 0.01
    },
    "tier_multipliers": {
      "starter": 1.0,
      "professional": 0.85,
      "enterprise": 0.7
    },
    "volume_discounts": {
      "monthly_interactions_1000": 0.95,
      "monthly_interactions_5000": 0.90,
      "monthly_interactions_10000": 0.85
    }
  }
}
```

### Step 3: Set Up Customer Engagement Metrics

```json
{
  "applicable_metrics": [
    "customer_satisfaction_score",
    "conversion_rate",
    "cart_abandonment_reduction",
    "average_order_value",
    "customer_engagement_time",
    "product_discovery_success",
    "recommendation_acceptance_rate",
    "customer_retention_rate",
    "support_ticket_reduction",
    "sales_attribution"
  ]
}
```

### Step 4: Configure E-commerce Integration

```typescript
// E-commerce platform integration setup
class CustomerSalesAgentEcommerceIntegration {
  async setupShopifyIntegration(agentCloneId: string, shopifyConfig: ShopifyConfig): Promise<void> {
    const integration = {
      platform: "shopify",
      store_url: shopifyConfig.storeUrl,
      access_token: shopifyConfig.accessToken,
      webhook_endpoints: {
        product_updates: `${this.baseUrl}/webhooks/shopify/products`,
        inventory_updates: `${this.baseUrl}/webhooks/shopify/inventory`,
        order_updates: `${this.baseUrl}/webhooks/shopify/orders`
      },
      capabilities: {
        product_catalog_sync: true,
        inventory_checking: true,
        cart_management: true,
        order_processing: true,
        customer_data_sync: true
      }
    };
    
    await this.saveIntegrationConfig(agentCloneId, integration);
    await this.setupWebhooks(shopifyConfig);
  }
  
  async syncProductCatalog(agentCloneId: string): Promise<void> {
    const products = await this.shopifyClient.product.list();
    const formattedProducts = products.map(product => ({
      id: product.id,
      title: product.title,
      description: product.body_html,
      price: product.variants[0].price,
      images: product.images.map(img => img.src),
      availability: product.variants[0].inventory_quantity > 0,
      categories: product.product_type,
      tags: product.tags
    }));
    
    await this.updateAgentProductKnowledge(agentCloneId, formattedProducts);
  }
}
```

### Step 5: Implement Customer Journey Tracking

```typescript
class CustomerJourneyTracker {
  async trackCustomerInteraction(
    agentCloneId: string,
    customerId: string,
    interaction: CustomerInteraction
  ): Promise<void> {
    const journeyData = {
      customer_id: customerId,
      agent_clone_id: agentCloneId,
      timestamp: new Date().toISOString(),
      interaction_type: interaction.type,
      channel: interaction.channel,
      stage: interaction.stage,
      products_discussed: interaction.products,
      objections_raised: interaction.objections,
      recommendations_made: interaction.recommendations,
      outcome: interaction.outcome,
      next_steps: interaction.nextSteps
    };
    
    await this.saveJourneyData(journeyData);
    await this.updateCustomerProfile(customerId, journeyData);
    await this.triggerPersonalizationUpdate(agentCloneId, customerId);
  }
  
  async getCustomerJourneyInsights(customerId: string): Promise<JourneyInsights> {
    const interactions = await this.getCustomerInteractions(customerId);
    
    return {
      total_interactions: interactions.length,
      journey_stage: this.determineCurrentStage(interactions),
      preferred_communication_style: this.analyzeCommStyle(interactions),
      product_interests: this.extractProductInterests(interactions),
      objection_patterns: this.identifyObjectionPatterns(interactions),
      conversion_probability: this.calculateConversionProbability(interactions),
      recommended_next_actions: this.generateNextActions(interactions)
    };
  }
}
```

---

## Conversion Optimization

### Advanced Conversion Optimization Features

```json
{
  "conversion_optimization_advanced": {
    "behavioral_triggers": {
      "exit_intent_detection": {
        "enabled": true,
        "trigger_delay": 2000,
        "offer_types": ["discount", "free_shipping", "extended_trial"],
        "personalization": true
      },
      "time_based_triggers": {
        "browsing_duration": 300000,
        "page_visit_count": 3,
        "return_visitor_recognition": true,
        "session_timeout_prevention": true
      },
      "engagement_triggers": {
        "scroll_depth": 75,
        "product_comparison_behavior": true,
        "price_checking_behavior": true,
        "feature_exploration": true
      }
    },
    "personalization_engine": {
      "recommendation_algorithms": {
        "collaborative_filtering": true,
        "content_based_filtering": true,
        "hybrid_approach": true,
        "real_time_learning": true
      },
      "dynamic_pricing": {
        "enabled": false,
        "factors": ["customer_segment", "purchase_history", "market_conditions"],
        "ethical_guidelines": true
      },
      "content_personalization": {
        "product_descriptions": true,
        "benefit_highlighting": true,
        "use_case_matching": true,
        "social_proof_selection": true
      }
    },
    "objection_handling_system": {
      "common_objections": {
        "price_concerns": {
          "responses": [
            "value_demonstration",
            "payment_options",
            "roi_calculation",
            "comparison_with_alternatives"
          ],
          "supporting_materials": ["case_studies", "testimonials", "guarantees"]
        },
        "trust_concerns": {
          "responses": [
            "security_assurance",
            "credential_display",
            "customer_testimonials",
            "guarantee_explanation"
          ],
          "supporting_materials": ["certifications", "reviews", "policies"]
        },
        "feature_doubts": {
          "responses": [
            "feature_demonstration",
            "use_case_examples",
            "trial_offers",
            "expert_consultation"
          ],
          "supporting_materials": ["demos", "tutorials", "documentation"]
        }
      },
      "objection_detection": {
        "sentiment_analysis": true,
        "keyword_recognition": true,
        "behavioral_indicators": true,
        "hesitation_patterns": true
      }
    },
    "urgency_creation": {
      "ethical_urgency": true,
      "scarcity_indicators": {
        "inventory_based": true,
        "time_based_offers": true,
        "exclusive_access": true,
        "seasonal_availability": true
      },
      "urgency_messaging": {
        "stock_levels": "Only X left in stock",
        "time_sensitive": "Sale ends in X hours",
        "exclusive_offers": "Exclusive offer for you",
        "popular_items": "X people viewed this today"
      }
    }
  }
}
```

### A/B Testing for Customer-Facing Agents

```typescript
class CustomerAgentABTesting {
  async createConversionExperiment(
    agentId: string,
    experimentConfig: ConversionExperiment
  ): Promise<Experiment> {
    const experiment = {
      id: this.generateExperimentId(),
      agent_id: agentId,
      name: experimentConfig.name,
      type: "conversion_optimization",
      variants: experimentConfig.variants,
      traffic_allocation: experimentConfig.trafficAllocation,
      success_metrics: [
        "conversion_rate",
        "average_order_value",
        "customer_satisfaction",
        "engagement_time"
      ],
      duration_days: experimentConfig.duration || 14,
      minimum_sample_size: experimentConfig.minSampleSize || 1000,
      statistical_significance_threshold: 0.95
    };
    
    await this.saveExperiment(experiment);
    await this.activateExperiment(experiment.id);
    
    return experiment;
  }
  
  async analyzeConversionResults(experimentId: string): Promise<ConversionResults> {
    const experiment = await this.getExperiment(experimentId);
    const results = await this.getExperimentResults(experimentId);
    
    const analysis = {
      winning_variant: this.determineWinningVariant(results),
      conversion_lift: this.calculateConversionLift(results),
      statistical_significance: this.calculateSignificance(results),
      customer_satisfaction_impact: this.analyzeSatisfactionImpact(results),
      revenue_impact: this.calculateRevenueImpact(results),
      recommendations: this.generateOptimizationRecommendations(results)
    };
    
    return analysis;
  }
}
```

---

## Best Practices

### Customer-Facing Agent Best Practices

#### 1. Customer-First Communication

**Language Guidelines:**
- Use customer-friendly language, avoid sales jargon
- Focus on benefits rather than features
- Be transparent about pricing and policies
- Acknowledge customer concerns empathetically

**Example Configurations:**
```json
{
  "communication_guidelines": {
    "customer_centric_language": true,
    "benefit_focused_messaging": true,
    "transparent_communication": true,
    "empathetic_responses": true,
    "jargon_avoidance": true
  }
}
```

#### 2. Trust-Building Strategies

**Transparency First:**
```json
{
  "trust_strategies": {
    "upfront_pricing": true,
    "clear_policies": true,
    "honest_recommendations": true,
    "realistic_expectations": true,
    "no_pressure_selling": true
  }
}
```

**Social Proof Integration:**
```json
{
  "social_proof_strategy": {
    "customer_reviews_prominent": true,
    "testimonials_relevant": true,
    "case_studies_applicable": true,
    "expert_endorsements": true,
    "usage_statistics": true
  }
}
```

#### 3. Personalization Without Intrusion

**Respectful Personalization:**
```json
{
  "personalization_ethics": {
    "consent_based_data_use": true,
    "transparent_data_collection": true,
    "customer_control_options": true,
    "privacy_first_approach": true,
    "opt_out_mechanisms": true
  }
}
```

#### 4. Conversion Optimization Ethics

**Ethical Conversion Practices:**
```json
{
  "ethical_conversion": {
    "honest_urgency_only": true,
    "genuine_scarcity_indicators": true,
    "customer_benefit_focus": true,
    "no_dark_patterns": true,
    "transparent_pricing": true,
    "clear_return_policies": true
  }
}
```

### Performance Optimization for Customer-Facing Agents

#### 1. Response Time Optimization

```typescript
class CustomerAgentPerformanceOptimizer {
  async optimizeResponseTimes(agentCloneId: string): Promise<void> {
    const optimizations = {
      // Cache frequently requested product information
      productCaching: {
        enabled: true,
        cache_duration: 300000, // 5 minutes
        preload_popular_products: true
      },
      
      // Optimize recommendation engine
      recommendationOptimization: {
        batch_processing: true,
        precomputed_recommendations: true,
        real_time_fallback: true
      },
      
      // Streamline integration calls
      integrationOptimization: {
        connection_pooling: true,
        request_batching: true,
        timeout_optimization: true
      }
    };
    
    await this.applyOptimizations(agentCloneId, optimizations);
  }
}
```

#### 2. Customer Experience Optimization

```json
{
  "customer_experience_optimization": {
    "proactive_assistance": {
      "smart_timing": true,
      "context_awareness": true,
      "non_intrusive_approach": true
    },
    "seamless_handoffs": {
      "human_agent_integration": true,
      "context_preservation": true,
      "smooth_transitions": true
    },
    "multi_channel_consistency": {
      "unified_customer_profile": true,
      "conversation_continuity": true,
      "preference_synchronization": true
    }
  }
}
```

### Security and Privacy for Customer-Facing Agents

#### 1. Customer Data Protection

```json
{
  "customer_data_protection": {
    "data_minimization": {
      "collect_only_necessary": true,
      "purpose_limitation": true,
      "retention_limits": true
    },
    "encryption_standards": {
      "data_at_rest": "AES-256",
      "data_in_transit": "TLS-1.3",
      "key_management": "HSM"
    },
    "access_controls": {
      "role_based_access": true,
      "audit_logging": true,
      "regular_access_reviews": true
    },
    "privacy_compliance": {
      "gdpr_compliance": true,
      "ccpa_compliance": true,
      "consent_management": true,
      "right_to_deletion": true
    }
  }
}
```

#### 2. Secure Customer Interactions

```typescript
class CustomerInteractionSecurity {
  async validateCustomerInteraction(
    interaction: CustomerInteraction
  ): Promise<SecurityValidation> {
    const validation = {
      input_sanitization: await this.sanitizeInput(interaction.message),
      pii_detection: await this.detectPII(interaction.message),
      threat_assessment: await this.assessThreats(interaction),
      compliance_check: await this.checkCompliance(interaction)
    };
    
    if (validation.pii_detection.detected) {
      await this.handlePIIDetection(interaction, validation.pii_detection);
    }
    
    if (validation.threat_assessment.risk_level > 0.7) {
      await this.escalateSecurityConcern(interaction, validation.threat_assessment);
    }
    
    return validation;
  }
}
```

---

## Conclusion

The Customer-Facing Sales Consultant Agent Type represents a specialized solution designed specifically for direct customer interaction and conversion optimization. This specification provides:

### Key Benefits for Customer-Facing Scenarios

1. **Customer-Centric Design**: Optimized for customer experience rather than internal training
2. **Trust-Building Focus**: Built-in features to establish credibility and transparency
3. **Conversion Optimization**: Advanced techniques to guide customers through purchase journey
4. **Personalization Engine**: AI-driven recommendations based on customer behavior and preferences
5. **E-commerce Integration**: Seamless integration with shopping carts and booking systems
6. **Multi-Channel Support**: Consistent experience across web, mobile, voice, and phone
7. **Privacy-First Approach**: Strong emphasis on customer data protection and privacy

### Implementation Success Factors

1. **Business Type Alignment**: Configure the agent specifically for your business model
2. **Trust Signal Integration**: Implement reviews, testimonials, and credibility indicators
3. **Conversion Flow Optimization**: Map and optimize the entire customer journey
4. **Personalization Balance**: Provide relevant recommendations without being intrusive
5. **Performance Monitoring**: Track customer satisfaction and conversion metrics
6. **Continuous Optimization**: Use A/B testing to improve customer experience

### Key Differentiators from Internal Sales Training Agents

- **Customer-First Language**: Uses terminology customers understand and appreciate
- **Transparency Emphasis**: Focuses on honest, transparent communication
- **Purchase Facilitation**: Direct integration with e-commerce and booking systems
- **Trust Building**: Incorporates social proof and credibility indicators
- **Conversion Focus**: Optimized to reduce friction and increase conversions
- **Privacy Compliance**: Strong emphasis on customer data protection

### Next Steps for Implementation

1. **Choose Business Type**: Select the appropriate business type configuration
2. **Configure Trust Signals**: Set up reviews, testimonials, and credibility features
3. **Integrate E-commerce**: Connect with your shopping cart or booking system
4. **Customize Messaging**: Adapt welcome messages and prompts for your customers
5. **Test Customer Journey**: Validate the entire customer experience flow
6. **Monitor Performance**: Track conversion rates and customer satisfaction
7. **Optimize Continuously**: Use customer feedback and analytics to improve

### Support and Resources

- **Technical Documentation**: Refer to the [Agent Types Complete Guide](./agent-types-complete-guide.md)
- **E-commerce Integration**: See platform-specific integration guides
- **Customer Experience**: Review customer journey optimization best practices
- **Privacy Compliance**: Ensure adherence to applicable privacy regulations

The Customer-Facing Sales Consultant Agent Type is designed to transform your customer interactions with AI-powered intelligence, helping you provide exceptional customer experiences, build trust, and achieve higher conversion rates while maintaining the highest standards of customer privacy and satisfaction.