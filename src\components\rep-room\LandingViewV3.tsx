import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>, AlertCircle, Wifi } from 'lucide-react';

interface LandingViewV3Props {
  onStart: () => void;
  config?: {
    title: string;
    description: string;
    logoUrl?: string;
    primaryColor?: string;
  };
  loading?: boolean;
  error?: string | null;
  canTryDemo?: boolean;
  onTryDemo?: () => void;
}

/**
 * LandingViewV3 - Welcome screen for Rep Room v3
 *
 * Responsibilities:
 * - Display agent introduction and branding
 * - Handle configuration loading with error states
 * - Show "Try Demo Mode" button when backend is unavailable
 * - Include "Start Enhanced Voice Conversation" button to initialize LiveKit
 * - Provide entry point to the enhanced multi-agent conversation
 *
 * This component follows the v2 design pattern but enhanced for v3:
 * - Hero section with agent avatar and title
 * - Description of enhanced agent capabilities
 * - Enhanced voice conversation CTA button
 * - Demo mode fallback option
 * - Responsive design with floating animations
 */
export function LandingViewV3({ 
  onStart, 
  config, 
  loading = false, 
  error = null, 
  canTryDemo = false, 
  onTryDemo 
}: LandingViewV3Props) {
  const [isStarting, setIsStarting] = useState(false);

  // Default configuration
  const defaultConfig = {
    title: 'Enhanced AI Assistant',
    description: 'Welcome to Rep Room V3! Experience our enhanced multi-agent collaborative interface with advanced voice capabilities and real-time presentation updates.',
    logoUrl: null,
    primaryColor: '#4F46E5'
  };

  const displayConfig = config || defaultConfig;

  const handleStart = async () => {
    setIsStarting(true);
    try {
      await onStart();
    } finally {
      setIsStarting(false);
    }
  };

  const handleTryDemo = async () => {
    if (onTryDemo) {
      setIsStarting(true);
      try {
        await onTryDemo();
      } finally {
        setIsStarting(false);
      }
    }
  };

  return (
    <div
      data-testid="landing-view"
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100"
    >
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 text-center max-w-2xl mx-auto px-6">
        {/* Loading State */}
        {loading && (
          <div className="mb-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Configuration</h2>
            <p className="text-gray-600">Preparing your enhanced Rep Room experience...</p>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <AlertCircle className="w-6 h-6 text-red-500 mr-2" />
              <h3 className="text-lg font-semibold text-red-800">Configuration Error</h3>
            </div>
            <p className="text-red-700 text-sm mb-4">{error}</p>
            {canTryDemo && (
              <p className="text-red-600 text-sm">
                Don't worry! You can still try the demo mode below.
              </p>
            )}
          </div>
        )}

        {/* Hero Section */}
        {!loading && (
          <div className="mb-8">
            {/* Agent Avatar */}
            <div className="mx-auto w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mb-6 shadow-lg animate-float">
              {displayConfig.logoUrl ? (
                <img 
                  src={displayConfig.logoUrl} 
                  alt="Agent Avatar" 
                  className="w-20 h-20 rounded-full object-cover"
                />
              ) : (
                <div className="relative">
                  <Bot className="w-12 h-12 text-white" />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <Mic className="w-3 h-3 text-white" />
                  </div>
                </div>
              )}
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {displayConfig.title}
            </h1>

            {/* Description */}
            <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
              {displayConfig.description}
            </p>

            {/* Enhanced Features Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full text-sm font-medium text-blue-800 mb-8">
              <Wifi className="w-4 h-4 mr-2" />
              Enhanced Multi-Agent • Real-time Voice • Live Collaboration
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {!loading && (
          <div className="space-y-4">
            {/* Primary CTA - Start Enhanced Voice Conversation */}
            {!error && (
              <Button
                data-testid="start-button"
                onClick={handleStart}
                disabled={isStarting}
                size="lg"
                className="px-8 py-4 text-lg font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isStarting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Initializing...
                  </>
                ) : (
                  <>
                    <Mic className="w-5 h-5 mr-2" />
                    Start Enhanced Voice Conversation
                  </>
                )}
              </Button>
            )}

            {/* Demo Mode Button */}
            {canTryDemo && onTryDemo && (
              <Button
                data-testid="demo-button"
                onClick={handleTryDemo}
                disabled={isStarting}
                variant="outline"
                size="lg"
                className="px-8 py-4 text-lg font-semibold border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50 rounded-xl shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isStarting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-2"></div>
                    Loading Demo...
                  </>
                ) : (
                  <>
                    <Bot className="w-5 h-5 mr-2" />
                    Try Demo Mode
                  </>
                )}
              </Button>
            )}
          </div>
        )}

        {/* Subtle hint text */}
        {!loading && !error && (
          <p className="text-sm text-gray-500 mt-4">
            Click to begin your enhanced voice conversation with multi-agent collaboration
          </p>
        )}

        {/* Demo mode hint */}
        {!loading && error && canTryDemo && (
          <p className="text-sm text-gray-500 mt-4">
            Demo mode provides a full-featured experience without backend connectivity
          </p>
        )}
      </div>

      {/* Custom animations - using Tailwind's built-in animations */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes blob {
            0% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -50px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
            100% { transform: translate(0px, 0px) scale(1); }
          }
          
          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
          }
          
          .animate-blob { animation: blob 7s infinite; }
          .animate-float { animation: float 3s ease-in-out infinite; }
          .animation-delay-2000 { animation-delay: 2s; }
          .animation-delay-4000 { animation-delay: 4s; }
        `
      }} />
    </div>
  );
}