import React, { useState, useEffect } from 'react';
import { 
  Agent, 
  AgentOrchestrationState, 
  AgentHandoff, 
  OrchestrationEvent,
  SpecialistAgentCapability 
} from '../../../types/rep-room';
import { AgentOrchestrationPanel } from './AgentOrchestrationPanel';
import { SpecialistAgentTrigger } from './SpecialistAgentTrigger';
import { AgentCard } from '../AgentCard';

/**
 * Demo component showing how to use the multi-agent orchestration UI components
 * This can be used as a reference for integration into the main Rep Room interface
 */
export const OrchestrationDemo: React.FC = () => {
  // Mock data for demonstration
  const [agents] = useState<Agent[]>([
    {
      id: 'main-agent',
      name: 'Alex',
      avatar: '',
      type: 'main',
      status: 'ready'
    },
    {
      id: 'seo-specialist',
      name: 'SEO Expert',
      avatar: '',
      type: 'specialist',
      status: 'idle',
      specialization: 'SEO Analysis'
    },
    {
      id: 'content-specialist',
      name: 'Content Writer',
      avatar: '',
      type: 'specialist',
      status: 'working',
      specialization: 'Content Creation'
    }
  ]);

  const [orchestrationState, setOrchestrationState] = useState<AgentOrchestrationState>({
    activeHandoffs: [],
    eventTimeline: [],
    availableSpecialists: agents.filter(a => a.type === 'specialist'),
    specialistCapabilities: [
      {
        id: 'seo-analysis',
        name: 'SEO Analysis',
        description: 'Analyze keywords, competitors, and SEO opportunities',
        icon: 'BarChart',
        category: 'analysis',
        isAvailable: true,
        estimatedDuration: 120
      },
      {
        id: 'content-generation',
        name: 'Content Generation',
        description: 'Create high-quality content based on SEO insights',
        icon: 'FileText',
        category: 'content',
        isAvailable: true,
        estimatedDuration: 180
      },
      {
        id: 'competitor-research',
        name: 'Competitor Research',
        description: 'Research and analyze competitor strategies',
        icon: 'Search',
        category: 'research',
        isAvailable: true,
        estimatedDuration: 90
      }
    ],
    coordinationStatus: 'idle'
  });

  // Simulate agent activity
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate random events
      const eventTypes: Array<'handoff' | 'delegation' | 'activation' | 'completion'> = 
        ['handoff', 'delegation', 'activation', 'completion'];
      const randomEvent = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      const randomAgent = agents[Math.floor(Math.random() * agents.length)];

      const newEvent: OrchestrationEvent = {
        id: `event-${Date.now()}`,
        type: randomEvent,
        agentId: randomAgent.id,
        timestamp: new Date(),
        data: { simulated: true }
      };

      setOrchestrationState(prev => ({
        ...prev,
        eventTimeline: [newEvent, ...prev.eventTimeline].slice(0, 20) // Keep last 20 events
      }));
    }, 5000); // Add new event every 5 seconds

    return () => clearInterval(interval);
  }, [agents]);

  // Handle specialist trigger
  const handleTriggerSpecialist = async (
    agentId: string, 
    capability: string, 
    context?: Record<string, unknown>
  ) => {
    console.log('Triggering specialist:', { agentId, capability, context });
    
    // Simulate activation
    setOrchestrationState(prev => ({
      ...prev,
      coordinationStatus: 'delegating'
    }));

    // Simulate handoff creation
    const handoff: AgentHandoff = {
      id: `handoff-${Date.now()}`,
      fromAgentId: 'main-agent',
      toAgentId: agentId,
      reason: `Delegating ${capability} task`,
      status: 'in-progress',
      startTime: new Date(),
      context
    };

    setTimeout(() => {
      setOrchestrationState(prev => ({
        ...prev,
        activeHandoffs: [...prev.activeHandoffs, handoff],
        coordinationStatus: 'handoff-in-progress'
      }));
    }, 1000);

    // Simulate completion
    setTimeout(() => {
      setOrchestrationState(prev => ({
        ...prev,
        activeHandoffs: prev.activeHandoffs.map(h => 
          h.id === handoff.id 
            ? { ...h, status: 'completed' as const, endTime: new Date() }
            : h
        ),
        coordinationStatus: 'idle'
      }));
    }, 5000);
  };

  const handleAgentSelect = (agentId: string) => {
    console.log('Selected agent:', agentId);
  };

  const mainAgent = agents.find(a => a.type === 'main');
  const activeSpecialists = agents.filter(a => a.type === 'specialist' && a.status !== 'idle');

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Multi-Agent Orchestration Demo
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Agent Cards with Orchestration Features */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-800">Agent Cards</h2>
            
            {/* Main Agent with orchestration features */}
            {mainAgent && (
              <AgentCard
                agent={mainAgent}
                isActive={true}
                showOrchestration={true}
                specialistAgents={activeSpecialists}
                onClick={() => handleAgentSelect(mainAgent.id)}
              />
            )}
            
            {/* Specialist Agents */}
            {agents.filter(a => a.type === 'specialist').map(agent => (
              <AgentCard
                key={agent.id}
                agent={agent}
                showOrchestration={true}
                onClick={() => handleAgentSelect(agent.id)}
              />
            ))}
          </div>

          {/* Orchestration Panel */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Orchestration Panel</h2>
            <AgentOrchestrationPanel
              orchestrationState={orchestrationState}
              agents={agents}
              onAgentSelect={handleAgentSelect}
            />
          </div>

          {/* Specialist Trigger */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Specialist Trigger</h2>
            <SpecialistAgentTrigger
              orchestrationState={orchestrationState}
              conversationContext={{
                topic: 'SEO optimization',
                userQuery: 'How can I improve my website ranking?'
              }}
              onTriggerSpecialist={handleTriggerSpecialist}
            />
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-8 p-6 bg-white rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Integration Guide</h3>
          <div className="prose text-sm text-gray-600">
            <p className="mb-3">
              <strong>AgentOrchestrationPanel:</strong> Shows active agents, handoffs, and coordination timeline. 
              Use this as a sidebar or dashboard component.
            </p>
            <p className="mb-3">
              <strong>SpecialistAgentTrigger:</strong> Allows manual triggering of specialist agents. 
              Integrate into chat interface or as a floating action panel.
            </p>
            <p className="mb-3">
              <strong>AgentCard (Enhanced):</strong> Now supports orchestration features like delegation indicators, 
              handoff animations, and specialist coordination status.
            </p>
            <p>
              <strong>AgentHandoffIndicator:</strong> Can be used standalone or embedded in other components 
              to show agent-to-agent handoffs with animated transitions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};