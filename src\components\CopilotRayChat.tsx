import React, { useState, useRef, useEffect } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotSidebar } from '@copilotkit/react-ui';
import '@copilotkit/react-ui/styles.css';
import { AgentResponseMetadata } from './chat/AgentResponseMetadata';
import { useAgentMetadata } from '../hooks/useAgentMetadata';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface CopilotRayChatProps {
  className?: string;
}

export const CopilotRayChat: React.FC<CopilotRayChatProps> = ({ className = '' }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hello! I'm Ray the Project Manager with Creative Design and Build. I'm ready to help you with project coordination, task management, and team collaboration. How can I assist you today?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get agent metadata for Ray the Project Manager
  const { metadata: agentMetadata } = useAgentMetadata({
    repRoomId: undefined, // This is a standalone agent, not tied to a rep room
    agentCloneId: undefined,
    tenantActivationId: undefined
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/weather-agent-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          conversation: messages.slice(-5).map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;

        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          assistantMessage.content += chunk;
          
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: assistantMessage.content }
                : msg
            )
          );
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setIsConnected(false);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(inputValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputValue);
    }
  };

  return (
    <CopilotKit runtimeUrl="/api/copilot" publicApiKey="disabled">
      <div className={`flex h-screen ${className}`}>
        {/* Main Chat Interface */}
        <div className="flex-1 flex flex-col bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 p-4">
          <div className="max-w-4xl mx-auto w-full flex flex-col h-full">
            {/* Header */}
            <div className="bg-white/10 backdrop-blur-lg rounded-t-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    🤖
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">Ray the Project Manager</h1>
                    <p className="text-white/80 text-sm">
                      Enhanced with CopilotKit • Direct connection to weatherAgent from src/mastra/agents/index.ts
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
                  <span className="text-white/80 text-sm">
                    {isConnected ? 'Connected to Ray the Project Manager (weatherAgent)' : 'Connection Error'}
                  </span>
                </div>
              </div>
            </div>

            {/* Messages Container */}
            <div className="flex-1 bg-white/5 backdrop-blur-lg border-x border-white/20 p-6 overflow-y-auto">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex items-start space-x-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                        message.role === 'user' ? 'bg-blue-500' : 'bg-green-500'
                      }`}>
                        {message.role === 'user' ? 'You' : 'Ray'}
                      </div>
                      <div className={`rounded-2xl p-4 ${
                        message.role === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-white/90 text-gray-800'
                      }`}>
                        <div className="whitespace-pre-wrap text-sm leading-relaxed">
                          {message.content}
                        </div>
                        <div className={`text-xs mt-2 ${
                          message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                        
                        {/* Agent Response Metadata for assistant messages */}
                        {message.role === 'assistant' && (
                          <div className="mt-2">
                            <AgentResponseMetadata
                              repRoomName="Ray's Project Management"
                              agentCloneName="Ray the Project Manager"
                              agentTypeName="Weather Agent (Project Manager)"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="flex items-start space-x-3 max-w-[80%]">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
                        Ray
                      </div>
                      <div className="bg-white/90 rounded-2xl p-4">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Input Area */}
            <div className="bg-white/10 backdrop-blur-lg rounded-b-2xl p-6 border border-white/20">
              <form onSubmit={handleSubmit} className="flex space-x-4">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message to Ray..."
                  className="flex-1 bg-white/20 border border-white/30 rounded-full px-6 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading || !inputValue.trim()}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-8 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-300"
                >
                  {isLoading ? 'Sending...' : 'Send'}
                </button>
              </form>
              <div className="mt-3 text-center">
                <p className="text-white/60 text-xs">
                  Enhanced with CopilotKit for better AI assistance • Powered by Groq Llama 3.3 70B
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CopilotKit Sidebar */}
        <CopilotSidebar
          instructions="You are Ray the Project Manager's assistant. Help users with project management, task coordination, and remodeling projects. You work for Creative Design and Build in San Diego."
          defaultOpen={false}
          labels={{
            title: "Ray's Assistant",
            initial: "How can I help you with your project?",
          }}
        />
      </div>
    </CopilotKit>
  );
};

export default CopilotRayChat;