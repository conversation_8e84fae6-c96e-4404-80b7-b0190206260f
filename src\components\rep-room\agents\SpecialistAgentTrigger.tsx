import React, { useState } from 'react';
import { 
  Bot, 
  Zap, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Search,
  FileText,
  BarChart,
  MessageSquare,
  Settings,
  Loader2
} from 'lucide-react';
import { 
  Agent, 
  SpecialistAgentCapability,
  AgentOrchestrationState 
} from '../../../types/rep-room';

interface SpecialistAgentTriggerProps {
  orchestrationState: AgentOrchestrationState;
  conversationContext?: Record<string, unknown>;
  onTriggerSpecialist: (agentId: string, capability: string, context?: Record<string, unknown>) => Promise<void>;
  className?: string;
  disabled?: boolean;
}

export const SpecialistAgentTrigger: React.FC<SpecialistAgentTriggerProps> = ({
  orchestrationState,
  conversationContext = {},
  onTriggerSpecialist,
  className = '',
  disabled = false
}) => {
  const [loadingAgents, setLoadingAgents] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const { specialistCapabilities, availableSpecialists, coordinationStatus } = orchestrationState;

  // Get icon for capability category
  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'analysis': return BarChart;
      case 'content': return FileText;
      case 'research': return Search;
      case 'communication': return MessageSquare;
      case 'technical': return Settings;
      default: return Bot;
    }
  };

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(specialistCapabilities.map(cap => cap.category)))];

  // Filter capabilities
  const filteredCapabilities = specialistCapabilities.filter(capability => {
    const matchesCategory = selectedCategory === 'all' || capability.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      capability.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      capability.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && capability.isAvailable;
  });

  // Handle specialist trigger
  const handleTriggerSpecialist = async (capability: SpecialistAgentCapability) => {
    if (disabled || loadingAgents.has(capability.id)) return;

    // Find available specialist for this capability
    const specialist = availableSpecialists.find(agent => 
      agent.specialization?.toLowerCase().includes(capability.category.toLowerCase())
    );

    if (!specialist) {
      console.error('No available specialist found for capability:', capability.name);
      return;
    }

    setLoadingAgents(prev => new Set(prev).add(capability.id));

    try {
      await onTriggerSpecialist(specialist.id, capability.id, {
        ...conversationContext,
        capability: capability.name,
        estimatedDuration: capability.estimatedDuration
      });
    } catch (error) {
      console.error('Failed to trigger specialist:', error);
    } finally {
      setLoadingAgents(prev => {
        const newSet = new Set(prev);
        newSet.delete(capability.id);
        return newSet;
      });
    }
  };

  // Format estimated duration
  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m`;
  };

  const isSystemBusy = coordinationStatus !== 'idle';

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Specialist Agents</h3>
          </div>
          <div className="flex items-center space-x-2">
            {isSystemBusy && (
              <div className="flex items-center space-x-1 text-sm text-orange-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>System Busy</span>
              </div>
            )}
            <span className="text-sm text-gray-500">
              {filteredCapabilities.length} available
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-3 mb-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search capabilities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {/* Capabilities Grid */}
        {filteredCapabilities.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {filteredCapabilities.map(capability => {
              const CategoryIcon = getCategoryIcon(capability.category);
              const isLoading = loadingAgents.has(capability.id);
              const isDisabled = disabled || isSystemBusy || !capability.isAvailable;

              return (
                <div
                  key={capability.id}
                  className={`p-4 rounded-lg border transition-all ${
                    isDisabled 
                      ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
                      : 'border-gray-200 bg-white hover:border-purple-300 hover:shadow-md cursor-pointer'
                  }`}
                  onClick={() => !isDisabled && handleTriggerSpecialist(capability)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 p-2 rounded-lg ${
                      isDisabled ? 'bg-gray-100' : 'bg-purple-100'
                    }`}>
                      {isLoading ? (
                        <Loader2 className="w-5 h-5 text-purple-600 animate-spin" />
                      ) : (
                        <CategoryIcon className={`w-5 h-5 ${
                          isDisabled ? 'text-gray-400' : 'text-purple-600'
                        }`} />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className={`font-medium text-sm ${
                        isDisabled ? 'text-gray-400' : 'text-gray-900'
                      }`}>
                        {capability.name}
                      </h4>
                      <p className={`text-xs mt-1 ${
                        isDisabled ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {capability.description}
                      </p>
                      
                      <div className="flex items-center justify-between mt-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          isDisabled 
                            ? 'bg-gray-100 text-gray-400' 
                            : 'bg-purple-100 text-purple-800'
                        }`}>
                          {capability.category}
                        </span>
                        
                        {capability.estimatedDuration && (
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{formatDuration(capability.estimatedDuration)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Loading State */}
                  {isLoading && (
                    <div className="mt-3 p-2 bg-purple-50 rounded-lg">
                      <div className="flex items-center space-x-2 text-sm text-purple-700">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>Activating specialist...</span>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Bot className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">
              {searchTerm || selectedCategory !== 'all' 
                ? 'No specialists match your criteria' 
                : 'No specialist agents available'}
            </p>
          </div>
        )}

        {/* System Status */}
        {isSystemBusy && (
          <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-orange-600" />
              <p className="text-sm text-orange-800">
                System is currently {coordinationStatus.replace('-', ' ')}. 
                Please wait before triggering additional specialists.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};