# Voice Inactivity Timeout Debug Handoff

## Overview
This document provides a complete handoff for debugging the voice inactivity timeout functionality in the Rep Room T1 system. The user reported that the voice inactivity timeout wasn't working - users could remain inactive for multiple minutes without receiving any countdown or disconnection warnings.

## Problem Statement
**Original Issue**: Voice inactivity timeout not functioning in Rep Room session at `http://localhost:8080/rroom-v3/t1/d77rasho4rj`
- Users remain inactive for multiple minutes without timeout warnings
- No countdown or disconnection occurs
- Inactivity management system appears non-functional

## Debugging Approach & Methodology

### 1. Systematic Component Analysis
I followed a systematic debugging approach to isolate the root cause:

1. **Verification Script Analysis** - Ran existing verification scripts to check implementation
2. **Component Inspection** - Examined each component in the inactivity timeout chain
3. **Integration Testing** - Checked how components work together
4. **Root Cause Identification** - Traced the issue to missing voice agent backend

### 2. Architecture Understanding
The voice inactivity timeout system consists of:

- **VoiceInactivityManager** (`src/services/voiceInactivityManager.ts`) - Core timeout logic
- **UnifiedVoiceContext** (`src/contexts/rroom/UnifiedVoiceContext.tsx`) - Integration layer
- **InactivityWarning** (`src/components/voice/InactivityWarning.tsx`) - UI component
- **RepRoomSessionPage** (`src/pages/RepRoomSessionPage.tsx`) - Page integration
- **Voice Agent Backend** - LiveKit cloud agent for voice processing

## Current Status & Findings

### ✅ Components Working Correctly

1. **VoiceInactivityManager Service**
   - ✅ Properly configured with 90-second timeout
   - ✅ 75-second warning threshold implemented
   - ✅ 5-second check interval
   - ✅ Speech and interaction activity tracking

2. **UnifiedVoiceContext Integration**
   - ✅ Inactivity manager properly imported and referenced
   - ✅ State management for time remaining and warning status
   - ✅ Callback handlers for warnings and timeouts
   - ✅ Speech activity integration
   - ✅ 1-second UI update interval

3. **InactivityWarning UI Component**
   - ✅ Component exists and properly implemented
   - ✅ Checks `inactivityWarningActive` state
   - ✅ Displays countdown and progress bar
   - ✅ Only renders when warning is active

4. **RepRoomSessionPage Integration**
   - ✅ InactivityWarning component imported
   - ✅ Component properly rendered in page

### ❌ Root Cause Identified

**Primary Issue**: Voice processing agent was not running
- The `api/voice-processing-agent/` directory only contained a `.env` file
- No actual voice agent implementation was present
- Voice connection couldn't be established, preventing inactivity timeout from functioning

### ✅ Solution Implemented

**Voice Agent Discovery**: Found working LiveKit voice agents in `api/livekit/` directory
- `livekit-cloud-agent-working.js` - Functional LiveKit cloud agent
- `livekit-cloud-agent-simple.js` - Simplified version
- `livekit-cloud-agent.js` - Full-featured version

**Voice Agent Started**: Successfully launched `livekit-cloud-agent-working.js`
- Agent connects to LiveKit cloud service
- Provides STT (Speech-to-Text) via Deepgram
- Generates AI responses via OpenAI
- Handles voice conversations

## Technical Implementation Details

### Voice Inactivity Manager Configuration
```typescript
// src/services/voiceInactivityManager.ts
const config = {
  timeoutSeconds: 90,        // Total timeout duration
  warningSeconds: 75,        // Warning threshold (15 seconds before timeout)
  checkIntervalMs: 5000,     // Check every 5 seconds
  uiUpdateIntervalMs: 1000   // Update UI every second
};
```

### Environment Configuration
```bash
# api/voice-processing-agent/.env
VOICE_IDLE_TIMEOUT_MINUTES=1.5
VOICE_INACTIVITY_TIMEOUT_SECONDS=90
```

### LiveKit Agent Architecture
- **Connection**: Connects to LiveKit cloud at `wss://ng53116-dc2-9mp3kcwz.livekit.cloud`
- **STT**: Uses Deepgram for speech-to-text processing
- **AI**: Uses OpenAI GPT-4o-mini for response generation
- **TTS**: Uses ElevenLabs for text-to-speech (with fallback options)

## Current Running State

### Active Terminals
1. **Terminal 1**: `npm run dev` - Frontend development server
2. **Terminal 2**: `node api/livekit/livekit-cloud-agent-working.js` - Voice agent

### Browser Testing
- Rep Room accessible at: `http://localhost:8080/rroom-v3/t1/d77rasho4rj`
- Voice connection shows "Connecting to voice..." status
- LiveKit integration attempting to establish connection

## Next Steps for Junior Engineer

### Immediate Actions Required

1. **Verify Voice Connection Establishment**
   ```bash
   # Open browser to test URL
   http://localhost:8080/rroom-v3/t1/d77rasho4rj
   
   # Check browser console for connection status
   # Look for successful LiveKit connection logs
   ```

2. **Test Inactivity Timeout Functionality**
   ```bash
   # Once voice connection is established:
   # 1. Connect to voice (click microphone button)
   # 2. Wait 75 seconds without speaking
   # 3. Verify warning appears in top-right corner
   # 4. Wait additional 15 seconds (90 total)
   # 5. Verify automatic disconnection occurs
   ```

3. **Debug Console Commands**
   ```javascript
   // Check inactivity state in browser console
   window.voiceContext?.state
   window.voiceContext?.state?.inactivityTimeRemaining
   window.voiceContext?.state?.inactivityWarningActive
   ```

### Potential Issues to Investigate

1. **LiveKit Connection Problems**
   - Check if LiveKit credentials are properly configured
   - Verify network connectivity to LiveKit cloud
   - Ensure voice token generation is working

2. **Voice Activity Detection**
   - Verify VAD (Voice Activity Detection) is functioning
   - Check if speech activity properly resets timeout
   - Ensure user interactions reset the timer

3. **UI Component Rendering**
   - Confirm InactivityWarning component renders when `inactivityWarningActive` is true
   - Check CSS styling doesn't hide the warning
   - Verify component positioning in top-right corner

### Quick Testing with Shortened Timeout

For faster testing, temporarily modify timeout values:

```typescript
// In src/contexts/rroom/UnifiedVoiceContext.tsx
// Change from:
timeoutSeconds: 90,
warningSeconds: 75,

// To:
timeoutSeconds: 10,
warningSeconds: 5,
```

This allows testing with a 10-second timeout instead of 90 seconds.

### Debug Script Updates Needed

The debug script (`scripts/verification/debug-inactivity-timeout.js`) needs updating:
- Currently checks for voice agent on port 3001 (incorrect)
- Should check for LiveKit agent file existence instead
- Update instructions to reflect LiveKit architecture

### Files to Monitor

**Key Implementation Files:**
- `src/services/voiceInactivityManager.ts` - Core timeout logic
- `src/contexts/rroom/UnifiedVoiceContext.tsx` - Integration layer
- `src/components/voice/InactivityWarning.tsx` - Warning UI
- `src/pages/RepRoomSessionPage.tsx` - Page integration

**Configuration Files:**
- `api/voice-processing-agent/.env` - Timeout configuration
- `api/livekit/livekit-cloud-agent-working.js` - Voice agent implementation

**Debug Tools:**
- `scripts/verification/debug-inactivity-timeout.js` - Debug script (needs updating)
- `scripts/verification/verify-voice-inactivity-timeout.js` - Verification script

## Expected Behavior When Working

1. **Voice Connection**: User connects to voice successfully
2. **Activity Tracking**: System tracks speech and user interactions
3. **Warning Phase**: After 75 seconds of inactivity, yellow warning appears in top-right
4. **Countdown**: Warning shows remaining time (15 seconds)
5. **Timeout**: After 90 seconds total, automatic disconnection occurs
6. **Reset**: Any speech or interaction resets the timer

## Troubleshooting Commands

```bash
# Check if voice agent is running
ps aux | grep livekit-cloud-agent-working

# Restart voice agent if needed
node api/livekit/livekit-cloud-agent-working.js

# Check frontend logs
# Open browser console and look for voice-related logs

# Run debug script (after updating)
node scripts/verification/debug-inactivity-timeout.js

# Run verification script
node scripts/verification/verify-voice-inactivity-timeout.js
```

## Success Criteria

The voice inactivity timeout will be considered working when:
- ✅ Voice connection establishes successfully
- ✅ Inactivity timer starts when voice is connected
- ✅ Warning appears at 75 seconds of inactivity
- ✅ Countdown shows remaining time accurately
- ✅ Automatic disconnection occurs at 90 seconds
- ✅ Speech activity resets the timer
- ✅ User interactions reset the timer

## Contact & Escalation

If issues persist beyond basic troubleshooting:
1. Check LiveKit cloud service status
2. Verify API credentials and environment variables
3. Review browser console for detailed error messages
4. Consider testing with simplified timeout values
5. Escalate to senior engineer if voice connection cannot be established

---

**Document Created**: 2025-01-25 19:51 UTC+3
**Status**: Voice agent running, ready for connection testing
**Next Action**: Verify voice connection establishment and test timeout functionality