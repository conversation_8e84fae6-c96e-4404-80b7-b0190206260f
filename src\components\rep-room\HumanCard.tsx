import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { Human } from '../../types/rep-room';
import { StatusIndicator } from './StatusIndicator';

interface HumanCardProps {
  human: Human;
  className?: string;
}

export const HumanCard: React.FC<HumanCardProps> = ({ human, className = '' }) => {
  const isTalking = human.status === 'talking';
  const isHandUp = human.status === 'hand-up';
  
  // Audio level indicator for talking humans
  const AudioLevelIndicator = () => {
    if (!isTalking) return null;
    
    return (
      <div className="flex items-center space-x-0.5 ml-2" aria-label="Voice activity">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="w-0.5 bg-gradient-to-t from-green-400 to-green-600 rounded-full animate-pulse"
            style={{
              height: `${Math.random() * 12 + 4}px`,
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1s'
            }}
          />
        ))}
      </div>
    );
  };
  
  // Status icon mapping
  const getStatusIcon = () => {
    switch (human.status) {
      case 'talking':
        return <Mic className="w-4 h-4 text-green-600" />;
      case 'listening':
        return <MicOff className="w-4 h-4 text-gray-400" />;
      case 'hand-up':
        return <Hand className="w-4 h-4 text-orange-600" />;
      default:
        return <MicOff className="w-4 h-4 text-gray-400" />;
    }
  };

  // Status text mapping
  const getStatusText = () => {
    switch (human.status) {
      case 'talking':
        return 'Speaking';
      case 'listening':
        return 'Listening';
      case 'hand-up':
        return 'Hand raised';
      default:
        return 'Listening';
    }
  };

  return (
    <div 
      className={`
        border border-gray-200 rounded-lg p-3 bg-white
        hover:shadow-sm transition-all duration-200
        ${className}
      `}
      aria-label={`${human.name} - ${getStatusText()}`}
    >
      <div className="flex items-center space-x-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {human.avatar ? (
            <img 
              src={human.avatar} 
              alt={human.name}
              className="w-10 h-10 rounded-full"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
              <User className="w-5 h-5 text-gray-600" />
            </div>
          )}
        </div>

        {/* Human Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium text-gray-900 truncate text-sm">
              {human.name}
            </h3>
            <StatusIndicator status={human.status} size="sm" />
          </div>
          
          {/* Role Badge (if provided) */}
          {human.role && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mt-1">
              {human.role}
            </span>
          )}

          {/* Status Text */}
          <div className="flex items-center space-x-1 mt-1">
            {getStatusIcon()}
            <span className="text-xs text-gray-500">
              {getStatusText()}
            </span>
            <AudioLevelIndicator />
          </div>
        </div>

        {/* Hand Raise Indicator */}
        {isHandUp && (
          <div className="flex-shrink-0">
            <div className="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center animate-pulse">
              <Hand className="w-3 h-3 text-orange-600" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};