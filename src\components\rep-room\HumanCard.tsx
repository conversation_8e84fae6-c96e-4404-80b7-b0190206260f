import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Wifi, WifiOff } from 'lucide-react';
import { Human } from '../../types/rep-room';
import { StatusIndicator } from './StatusIndicator';
import { useUnifiedVoice } from '../../contexts/rroom/UnifiedVoiceContext';

interface HumanCardProps {
  human: Human;
  className?: string;
}

export const HumanCard: React.FC<HumanCardProps> = ({ human, className = '' }) => {
  // Get real-time voice state from UnifiedVoiceContext
  const { state: voiceState } = useUnifiedVoice();
  
  // Determine real-time status based on LiveKit participant state
  const isConnected = voiceState.isConnected;
  const isTalking = voiceState.isSpeaking;
  const isListening = voiceState.isListening && !voiceState.isSpeaking;
  const isHandUp = human.status === 'hand-up'; // Keep hand-up from original status
  
  // Override status with real-time data when voice is connected
  // Map to valid StatusIndicator types
  const realTimeStatus = isConnected ? (
    isTalking ? 'talking' :
    isListening ? 'listening' :
    'online'  // Use 'online' instead of 'connected'
  ) : 'offline'; // Use 'offline' instead of 'disconnected'
  
  const effectiveStatus = isConnected ? realTimeStatus : human.status;
  
  // Audio level indicator for talking humans - now uses real-time data
  const AudioLevelIndicator = () => {
    if (!isTalking && !voiceState.vadActive) return null;
    
    return (
      <div className="flex items-center space-x-0.5 ml-2" aria-label="Voice activity">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className={`w-0.5 rounded-full animate-pulse ${
              isTalking
                ? 'bg-gradient-to-t from-green-400 to-green-600'
                : 'bg-gradient-to-t from-blue-400 to-blue-600'
            }`}
            style={{
              height: `${Math.random() * 12 + 4}px`,
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1s'
            }}
          />
        ))}
      </div>
    );
  };
  
  // Status icon mapping with real-time data
  const getStatusIcon = () => {
    switch (effectiveStatus) {
      case 'talking':
        return <Mic className="w-4 h-4 text-green-600" />;
      case 'listening':
        return <Mic className="w-4 h-4 text-blue-600" />;
      case 'online':
        return <Wifi className="w-4 h-4 text-gray-600" />;
      case 'offline':
        return <WifiOff className="w-4 h-4 text-red-400" />;
      case 'hand-up':
        return <Hand className="w-4 h-4 text-orange-600" />;
      default:
        return <MicOff className="w-4 h-4 text-gray-400" />;
    }
  };

  // Status text mapping with real-time data
  const getStatusText = () => {
    if (!isConnected) {
      return 'Not connected';
    }
    
    switch (effectiveStatus) {
      case 'talking':
        return 'Speaking';
      case 'listening':
        return 'Listening';
      case 'online':
        return 'Connected';
      case 'offline':
        return 'Disconnected';
      case 'hand-up':
        return 'Hand raised';
      default:
        return 'Ready';
    }
  };

  return (
    <div 
      className={`
        border border-gray-200 rounded-lg p-3 bg-white
        hover:shadow-sm transition-all duration-200
        ${className}
      `}
      aria-label={`${human.name} - ${getStatusText()}`}
    >
      <div className="flex items-center space-x-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {human.avatar ? (
            <img 
              src={human.avatar} 
              alt={human.name}
              className="w-10 h-10 rounded-full"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
              <User className="w-5 h-5 text-gray-600" />
            </div>
          )}
        </div>

        {/* Human Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium text-gray-900 truncate text-sm">
              {human.name}
            </h3>
            <StatusIndicator status={effectiveStatus} size="sm" />
          </div>
          
          {/* Role Badge (if provided) */}
          {human.role && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mt-1">
              {human.role}
            </span>
          )}

          {/* Status Text */}
          <div className="flex items-center space-x-1 mt-1">
            <div
              className="cursor-help"
              title={
                !isConnected
                  ? 'User is not connected to the voice session'
                  : effectiveStatus === 'talking'
                    ? 'User is currently speaking and being transcribed'
                    : effectiveStatus === 'listening'
                      ? 'User is connected and ready to speak'
                      : effectiveStatus === 'online'
                        ? 'User is connected to the voice session'
                        : effectiveStatus === 'hand-up'
                          ? 'User has raised their hand for attention'
                          : 'User status unknown'
              }
            >
              {getStatusIcon()}
            </div>
            <span
              className="text-xs text-gray-500 cursor-help"
              title={
                !isConnected
                  ? 'This user needs to connect to participate in voice conversations'
                  : effectiveStatus === 'talking'
                    ? 'Speech is being detected and processed in real-time'
                    : effectiveStatus === 'listening'
                      ? 'Microphone is active and monitoring for speech'
                      : effectiveStatus === 'online'
                        ? 'Connected and ready to participate in voice conversation'
                        : effectiveStatus === 'hand-up'
                          ? 'User is requesting attention or has a question'
                          : 'Current participation status in the voice session'
              }
            >
              {getStatusText()}
            </span>
            <AudioLevelIndicator />
          </div>
        </div>

        {/* Hand Raise Indicator */}
        {isHandUp && (
          <div className="flex-shrink-0">
            <div className="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center animate-pulse">
              <Hand className="w-3 h-3 text-orange-600" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};