import React from 'react';
import { useUnifiedVoice } from '../../contexts/rroom/UnifiedVoiceContext';

interface InactivityWarningProps {
  className?: string;
}

/**
 * InactivityWarning Component
 * 
 * Displays a warning when the user is approaching the 90-second inactivity timeout.
 * Shows remaining time and provides visual feedback about the impending disconnection.
 */
export function InactivityWarning({ className = '' }: InactivityWarningProps) {
  const { state } = useUnifiedVoice();
  
  // Don't show if not connected or no warning active
  if (!state.isConnected || !state.inactivityWarningActive) {
    return null;
  }

  const remainingTime = state.inactivityTimeRemaining;
  const isUrgent = remainingTime <= 10; // Last 10 seconds are urgent

  return (
    <div 
      className={`
        fixed top-4 right-4 z-50 
        bg-yellow-50 border-l-4 border-yellow-400 
        p-4 rounded-lg shadow-lg max-w-sm
        ${isUrgent ? 'animate-pulse bg-red-50 border-red-400' : ''}
        ${className}
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {isUrgent ? (
            <svg 
              className="h-5 w-5 text-red-400" 
              viewBox="0 0 20 20" 
              fill="currentColor"
              aria-hidden="true"
            >
              <path 
                fillRule="evenodd" 
                d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" 
                clipRule="evenodd" 
              />
            </svg>
          ) : (
            <svg 
              className="h-5 w-5 text-yellow-400" 
              viewBox="0 0 20 20" 
              fill="currentColor"
              aria-hidden="true"
            >
              <path 
                fillRule="evenodd" 
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" 
                clipRule="evenodd" 
              />
            </svg>
          )}
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-sm font-medium ${isUrgent ? 'text-red-800' : 'text-yellow-800'}`}>
            {isUrgent ? 'Disconnecting Soon!' : 'Inactivity Warning'}
          </h3>
          <div className={`mt-1 text-sm ${isUrgent ? 'text-red-700' : 'text-yellow-700'}`}>
            <p>
              {isUrgent 
                ? `Voice session will disconnect in ${remainingTime} seconds due to inactivity.`
                : `You'll be disconnected in ${remainingTime} seconds due to inactivity.`
              }
            </p>
            <p className="mt-1 text-xs">
              Speak or interact to stay connected.
            </p>
          </div>
        </div>
      </div>
      
      {/* Progress bar showing remaining time */}
      <div className="mt-3">
        <div className="flex justify-between text-xs text-gray-600 mb-1">
          <span>Time remaining</span>
          <span>{remainingTime}s</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-1000 ${
              isUrgent ? 'bg-red-500' : 'bg-yellow-500'
            }`}
            style={{ 
              width: `${Math.max(0, (remainingTime / 90) * 100)}%` 
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default InactivityWarning;