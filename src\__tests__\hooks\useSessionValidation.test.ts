import { renderHook, act, waitFor } from '@testing-library/react';
import { useSessionValidation, SessionValidationConfig, SessionValidationResult } from '../../hooks/useSessionValidation';

// Mock Supabase
const mockSupabaseClient = {
  from: jest.fn(),
  rpc: jest.fn()
};

const mockSelect = jest.fn();
const mockEq = jest.fn();
const mockSingle = jest.fn();

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient)
}));

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';

// Mock console methods
const originalConsoleLog = console.log;
const mockConsoleLog = jest.fn();

describe('useSessionValidation', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
    
    // Mock console.log
    console.log = mockConsoleLog;
    
    // Setup default Supabase mock chain
    mockSupabaseClient.from.mockReturnValue({
      select: mockSelect
    });
    
    mockSelect.mockReturnValue({
      eq: mockEq
    });
    
    mockEq.mockReturnValue({
      eq: mockEq,
      single: mockSingle
    });
    
    // Default successful response
    mockSingle.mockResolvedValue({
      data: {
        session_id: 'test12345678',
        slug: 'test-session',
        status: 'active',
        created_at: '2023-01-01T00:00:00Z',
        expires_at: '2025-01-01T00:00:00Z',
        max_participants: 10,
        password_hash: null,
        metadata: {},
        rep_room_participants: []
      },
      error: null
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    console.log = originalConsoleLog;
  });

  describe('Initial State', () => {
    it('should initialize with default validation state', () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      expect(result.current.validationResult).toEqual({
        isValid: false,
        exists: false,
        hasAccess: false,
        isExpired: false,
        requiresPassword: false,
        error: null
      });

      // Hook starts validation immediately when sessionId and slug are provided
      expect(result.current.isValidating).toBe(true);
      expect(result.current.canJoinSession).toBe(false);
      expect(result.current.errorMessage).toBe(null);
    });

    it('should start validation when sessionId and slug are provided', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      // Should start validating
      expect(result.current.isValidating).toBe(true);

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('rep_room_sessions');
    });
  });

  describe('Session ID Validation', () => {
    it('should reject invalid session ID format', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'invalid',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.error).toBe('Invalid session ID format');
      expect(result.current.errorMessage).toBe('The session link appears to be invalid. Please check the URL and try again.');
    });

    it('should accept valid session ID format', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('rep_room_sessions');
    });

    it('should handle missing sessionId or slug', async () => {
      const config: SessionValidationConfig = {
        sessionId: '',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      // Hook should not start validation when sessionId is missing
      expect(result.current.isValidating).toBe(false);
      expect(result.current.validationResult.error).toBe(null);
      
      // Supabase should not be called
      expect(mockSupabaseClient.from).not.toHaveBeenCalled();
    });
  });

  describe('Session Existence', () => {
    it('should handle session not found', async () => {
      mockSingle.mockResolvedValueOnce({
        data: null,
        error: { message: 'No rows returned' }
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.exists).toBe(false);
      expect(result.current.validationResult.error).toBe('Session not found');
      expect(result.current.errorMessage).toBe('This session does not exist or has been deleted.');
    });

    it('should handle existing session', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.exists).toBe(true);
      expect(result.current.sessionData).toBeDefined();
      expect(result.current.sessionData?.id).toBe('test12345678');
    });
  });

  describe('Session Expiration', () => {
    it('should detect expired session', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: '2020-01-01T00:00:00Z', // Expired
          max_participants: 10,
          password_hash: null,
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.exists).toBe(true);
      expect(result.current.validationResult.isExpired).toBe(true);
      expect(result.current.validationResult.error).toBe('Session has expired');
      expect(result.current.errorMessage).toBe('This session has expired and is no longer available.');
    });

    it('should handle session without expiration', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: 10,
          password_hash: null,
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isExpired).toBe(false);
    });
  });

  describe('Session Status', () => {
    it('should reject inactive session', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'paused',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration so status check happens first
          max_participants: 10,
          password_hash: null,
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.exists).toBe(true);
      expect(result.current.validationResult.error).toBe('Session is paused');
    });

    it('should accept active session', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No participant limit
          password_hash: null, // No password
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(true);
      expect(result.current.sessionData?.status).toBe('active');
    });
  });

  describe('Participant Limits', () => {
    it('should reject when session is full for new user', async () => {
      mockSingle
        .mockResolvedValueOnce({
          data: {
            session_id: 'test12345678',
            slug: 'test-session',
            status: 'active',
            created_at: '2023-01-01T00:00:00Z',
            expires_at: null, // No expiration so participant limit check happens
            max_participants: 2,
            password_hash: null,
            metadata: {},
            rep_room_participants: [{ id: 1 }, { id: 2 }] // Full
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: null, // User not already a participant
          error: null
        });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session',
        userId: 'new-user-123'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.error).toBe('Session is full');
      expect(result.current.errorMessage).toBe('This session has reached its maximum number of participants.');
    });

    it('should allow existing participant when session is full', async () => {
      mockSingle
        .mockResolvedValueOnce({
          data: {
            session_id: 'test12345678',
            slug: 'test-session',
            status: 'active',
            created_at: '2023-01-01T00:00:00Z',
            expires_at: null, // No expiration
            max_participants: 2,
            password_hash: null,
            metadata: {},
            rep_room_participants: [{ id: 1 }, { id: 2 }] // Full
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: { participant_id: 'existing-user-123' }, // User is already a participant
          error: null
        });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session',
        userId: 'existing-user-123'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(true);
    });

    it('should allow when session has no participant limit', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No limit
          password_hash: null,
          metadata: {},
          rep_room_participants: [{ id: 1 }, { id: 2 }, { id: 3 }]
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(true);
    });
  });

  describe('Password Protection', () => {
    it('should require password when session is password protected', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No participant limit
          password_hash: 'hashed-password',
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
        // No password provided
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.requiresPassword).toBe(true);
      expect(result.current.validationResult.error).toBe('Session requires password');
      expect(result.current.errorMessage).toBe('This session is password protected. Please enter the password to join.');
    });

    it('should validate correct password', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No participant limit
          password_hash: 'hashed-password',
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: true, // Password is valid
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session',
        password: 'correct-password'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(true);
      expect(result.current.validationResult.requiresPassword).toBe(true);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('validate_session_password', {
        session_id: 'test12345678',
        password_input: 'correct-password'
      });
    });

    it('should reject incorrect password', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No participant limit
          password_hash: 'hashed-password',
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: false, // Password is invalid
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session',
        password: 'wrong-password'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.requiresPassword).toBe(true);
      expect(result.current.validationResult.error).toBe('Invalid password');
      expect(result.current.errorMessage).toBe('The password you entered is incorrect. Please try again.');
    });
  });

  describe('Rate Limiting', () => {
    it('should prevent rapid successive validations', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      // Wait for initial validation
      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      const initialCallCount = mockSupabaseClient.from.mock.calls.length;

      // Try to revalidate immediately
      act(() => {
        result.current.revalidate();
      });

      // Should be rate limited
      expect(mockConsoleLog).toHaveBeenCalledWith('[SessionValidation] Rate limited, skipping validation');
      expect(mockSupabaseClient.from.mock.calls.length).toBe(initialCallCount);
    });

    it('should allow validation after rate limit period', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      // Wait for initial validation
      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      const initialCallCount = mockSupabaseClient.from.mock.calls.length;

      // Advance time past rate limit
      act(() => {
        jest.advanceTimersByTime(1100); // 1.1 seconds
      });

      // Try to revalidate
      act(() => {
        result.current.revalidate();
      });

      // Should allow validation
      expect(mockSupabaseClient.from.mock.calls.length).toBeGreaterThan(initialCallCount);
    });
  });

  describe('Callback Functions', () => {
    it('should call onValidationChange callback', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No participant limit
          password_hash: null, // No password
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const mockOnValidationChange = jest.fn();
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session',
        onValidationChange: mockOnValidationChange
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(mockOnValidationChange).toHaveBeenCalledWith(
        expect.objectContaining({
          isValid: true,
          exists: true,
          hasAccess: true
        })
      );
    });

    it('should call onError callback when validation fails', async () => {
      const mockOnError = jest.fn();
      mockSingle.mockResolvedValueOnce({
        data: null,
        error: { message: 'Session not found' }
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session',
        onError: mockOnError
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(mockOnError).toHaveBeenCalledWith('Session not found');
    });
  });

  describe('Utility Functions', () => {
    it('should correctly determine canJoinSession', async () => {
      mockSingle.mockResolvedValueOnce({
        data: {
          session_id: 'test12345678',
          slug: 'test-session',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          expires_at: null, // No expiration
          max_participants: null, // No participant limit
          password_hash: null, // No password
          metadata: {},
          rep_room_participants: []
        },
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.canJoinSession).toBe(true);
    });

    it('should provide user-friendly error messages', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'invalid',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.errorMessage).toBe('The session link appears to be invalid. Please check the URL and try again.');
    });
  });

  describe('Error Handling', () => {
    it('should handle Supabase errors gracefully', async () => {
      mockSingle.mockRejectedValueOnce(new Error('Network error'));

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.isValid).toBe(false);
      expect(result.current.validationResult.error).toBe('Network error');
    });

    it('should handle non-Error exceptions', async () => {
      mockSingle.mockRejectedValueOnce('String error');

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      expect(result.current.validationResult.error).toBe('Validation failed');
    });
  });

  describe('Revalidation', () => {
    it('should revalidate when dependencies change', async () => {
      // Set up successful session data for both calls
      mockSingle
        .mockResolvedValue({
          data: {
            session_id: 'test12345678',
            slug: 'test-session',
            status: 'active',
            created_at: '2023-01-01T00:00:00Z',
            expires_at: null,
            max_participants: null,
            password_hash: 'hashed-password',
            metadata: {},
            rep_room_participants: []
          },
          error: null
        });

      // Mock password validation for the second call
      mockSupabaseClient.rpc.mockResolvedValue({
        data: true,
        error: null
      });

      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result, rerender } = renderHook(
        (props) => useSessionValidation(props),
        { initialProps: config }
      );

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      const initialCallCount = mockSupabaseClient.from.mock.calls.length;

      // Advance time to bypass rate limiting
      act(() => {
        jest.advanceTimersByTime(1100); // 1.1 seconds
      });

      // Change password - this should trigger revalidation
      act(() => {
        rerender({
          ...config,
          password: 'new-password'
        });
      });

      // Wait for the new validation to complete
      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      await waitFor(() => {
        expect(mockSupabaseClient.from.mock.calls.length).toBeGreaterThan(initialCallCount);
      });
    });

    it('should provide manual revalidation function', async () => {
      const config: SessionValidationConfig = {
        sessionId: 'test12345678',
        slug: 'test-session'
      };

      const { result } = renderHook(() => useSessionValidation(config));

      await waitFor(() => {
        expect(result.current.isValidating).toBe(false);
      });

      // Advance time to bypass rate limiting
      act(() => {
        jest.advanceTimersByTime(1100);
      });

      const initialCallCount = mockSupabaseClient.from.mock.calls.length;

      act(() => {
        result.current.revalidate();
      });

      expect(mockSupabaseClient.from.mock.calls.length).toBeGreaterThan(initialCallCount);
    });
  });
});