// Rep Room UI Components
export { StatusIndicator } from './StatusIndicator';
export { AgentCard } from './AgentCard';
export { HumanCard } from './HumanCard';
export { ParticipantsPanel } from './ParticipantsPanel';
export { ChatMessage } from './ChatMessage';
export { ConversationFlow } from './ConversationFlow';
export { PresentationArea } from './PresentationArea';
export { RepRoomInterface, createDemoRepRoomState } from './RepRoomInterface';

// Enhanced Voice Feedback Components
export { VoiceControls } from './VoiceControls';
export { AudioVisualizer } from './AudioVisualizer';

// Re-export types for convenience
export type {
  Agent,
  Human,
  ChatMessage as ChatMessageType,
  PresentationContent,
  RepRoomState,
  AgentStatus,
  HumanStatus,
  AgentType,
  MessageType,
  ContentType
} from '../../types/rep-room';

// Voice feedback component types
export type { ConnectionStatus, VoiceActivityLevel } from './VoiceControls';
export type { AudioVisualizerProps } from './AudioVisualizer';