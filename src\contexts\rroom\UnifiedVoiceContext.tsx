import React, { createContext, useContext, useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Room, RoomEvent, RemoteTrack, Track, ConnectionState, Participant, RemoteParticipant } from 'livekit-client';
import { useMicVAD } from '@ricky0123/vad-react';
import {
  ParticipantInfo,
  ParticipantState,
  AllDataChannelMessages,
  isValidDataChannelMessage,
  createParticipantListUpdate,
  createSystemEventMessage,
  createVoiceActivityMessage,
  createInterruptionMessage
} from '../../types/DataChannelMessages';

// Voice configuration interface (from database config)
interface VoiceConfig {
  enabled: boolean;
  provider: string;
  sttProvider: string;
  ttsProvider: string;
  voiceId: string;
}

// Event data types for type safety
type EventData = string | number | boolean | Record<string, unknown>;

// Message interface for AG-UI events
export interface VoiceMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isComplete: boolean;
}

// Voice state interface (enhanced for Phase 4)
export interface UnifiedVoiceState {
  isConnecting: boolean;
  isListening: boolean;
  isSpeaking: boolean;
  connectionError: string | null;
  isAgentSpeaking: boolean;
  vadActive: boolean;
  visibleMessages: VoiceMessage[];
  // Phase 4: Enhanced participant tracking
  participants: ParticipantInfo[];
  participantStates: Record<string, ParticipantState>;
  currentUser?: ParticipantInfo;
  totalParticipants: number;
}

// Voice controls interface (simplified for LiveKit-only)
export interface UnifiedVoiceControls {
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  sendInterruption: () => void;
  // Event system for bridge communication
  on: (event: string, callback: (data: EventData) => void) => void;
  off: (event: string, callback: (data: EventData) => void) => void;
}

// Context interface
export interface UnifiedVoiceContextType {
  state: UnifiedVoiceState;
  controls: UnifiedVoiceControls;
  isVoiceActive: boolean;
}

// Create context
const UnifiedVoiceContext = createContext<UnifiedVoiceContextType | null>(null);

// Hook to use the context
export function useUnifiedVoice(): UnifiedVoiceContextType {
  const context = useContext(UnifiedVoiceContext);
  if (!context) {
    throw new Error('useUnifiedVoice must be used within a UnifiedVoiceProvider');
  }
  return context;
}

// Provider props
interface UnifiedVoiceProviderProps {
  children: React.ReactNode;
  voiceConfig: VoiceConfig;
  sessionId?: string;
}

/**
 * UnifiedVoiceProvider - Ultra-low-latency LiveKit-only voice service
 *
 * Phase 3 Refactoring: Simplified to only handle LiveKit connection
 * All voice processing (STT/TTS) is now handled by the backend agent
 *
 * Responsibilities:
 * - LiveKit room connection management
 * - Client-side VAD for interruption detection
 * - Data channel communication for interruptions
 * - WebRTC audio constraints optimization
 *
 * Removed from frontend:
 * - STT processing (moved to backend agent)
 * - TTS processing (moved to backend agent)
 * - Audio buffering and chunking
 * - Complex voice state management
 */
export function UnifiedVoiceProvider({
  children,
  voiceConfig,
  sessionId
}: UnifiedVoiceProviderProps) {
  // LiveKit room instance
  const [room, setRoom] = useState<Room | null>(null);
  const [connectionState, setConnectionState] = useState<ConnectionState>(ConnectionState.Disconnected);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isAgentSpeaking, setIsAgentSpeaking] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionAttempts, setConnectionAttempts] = useState(0);
  const [lastConnectionAttempt, setLastConnectionAttempt] = useState<number>(0);

  // Message state management for AG-UI events
  const [visibleMessages, setVisibleMessages] = useState<VoiceMessage[]>([]);
  const [activeMessages, setActiveMessages] = useState<Map<string, VoiceMessage>>(new Map());

  // Phase 4: Enhanced participant tracking state
  const [participants, setParticipants] = useState<ParticipantInfo[]>([]);
  const [participantStates, setParticipantStates] = useState<Record<string, ParticipantState>>({});
  const [currentUser, setCurrentUser] = useState<ParticipantInfo | undefined>();

  // Event system for bridge communication
  const eventListeners = useRef<Map<string, Set<(data: EventData) => void>>>(new Map());

  // Client-side VAD for interruption detection - FIXED: Updated for Silero model compatibility with proper feeds configuration
  const vad = useMicVAD({
    startOnLoad: false,
    onSpeechStart: () => {
      console.log('[UnifiedVoice] VAD: User started speaking');
      emitEvent('user_speech_start', true);
      // Send interruption signal to backend agent
      if (room && isAgentSpeaking) {
        sendInterruption();
      }
    },
    onSpeechEnd: () => {
      console.log('[UnifiedVoice] VAD: User stopped speaking');
      emitEvent('user_speech_end', true);
    },
    onVADMisfire: () => {
      console.log('[UnifiedVoice] VAD: Misfire detected');
    },
    // CRITICAL FIX: Use legacy model for better compatibility with React wrapper
    workletURL: '/vad.worklet.bundle.min.js',
    modelURL: '/silero_vad.onnx', // Use legacy model for compatibility
    // Custom model fetcher with enhanced error handling
    modelFetcher: async (modelURL: string) => {
      console.log('[UnifiedVoice] Fetching VAD model from:', modelURL);
      try {
        const response = await fetch(modelURL);
        if (!response.ok) {
          throw new Error(`Failed to fetch model: ${response.status} ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        console.log('[UnifiedVoice] VAD model loaded successfully, size:', arrayBuffer.byteLength);
        return arrayBuffer;
      } catch (error) {
        console.error('[UnifiedVoice] Failed to fetch VAD model:', error);
        throw error;
      }
    },
    // FIXED: Enhanced ORT config for Silero VAD model with proper state handling
    ortConfig: (ort: { env?: { logLevel?: string; debug?: boolean } }) => {
      console.log('[UnifiedVoice] Configuring ONNX Runtime for VAD');
      
      // Suppress ONNX Runtime verbose logging
      if (ort && ort.env) {
        ort.env.logLevel = 'error'; // Only show errors, suppress warnings
        ort.env.debug = false;
      }
      
      console.log('[UnifiedVoice] VAD Model URL: /silero_vad.onnx');
      console.log('[UnifiedVoice] VAD Worklet URL: /vad.worklet.bundle.min.js');
      // Enhanced config for Silero model compatibility
      return {
        executionProviders: ['wasm'],
        graphOptimizationLevel: 'all',
        executionMode: 'sequential',
        enableProfiling: false,
        enableMemPattern: true,
        enableCpuMemArena: true,
        logSeverityLevel: 3, // 0=Verbose, 1=Info, 2=Warning, 3=Error, 4=Fatal
        logVerbosityLevel: 0,
        // CRITICAL: Configure session options for Silero model state handling
        sessionOptions: {
          enableCpuMemArena: true,
          enableMemPattern: true,
          executionMode: 'sequential',
          graphOptimizationLevel: 'all',
          logSeverityLevel: 3, // Suppress warnings at session level too
          logVerbosityLevel: 0
        },
        // Additional options for better performance and state management
        extra: {
          session: {
            use_env_allocators: true,
            use_device_allocator_for_initializers: true,
            // CRITICAL: Enable state persistence for Silero VAD
            enable_cpu_mem_arena: true,
            enable_mem_pattern: true
          }
        }
      };
    },
    // CRITICAL FIX: Use legacy model frameSamples: 512
    frameSamples: 512,
    // Optimized parameters for Silero model
    positiveSpeechThreshold: 0.5,
    negativeSpeechThreshold: 0.35,
    redemptionFrames: 8,
    preSpeechPadFrames: 1,
    minSpeechFrames: 3,
    // Additional options for model state handling
    submitUserSpeechOnPause: true
  });

  // Event system implementation
  const emitEvent = useCallback((event: string, data: EventData) => {
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[UnifiedVoice] Error in event listener for ${event}:`, error);
        }
      });
    }
  }, []);

  const addEventListener = useCallback((event: string, callback: (data: EventData) => void) => {
    if (!eventListeners.current.has(event)) {
      eventListeners.current.set(event, new Set());
    }
    eventListeners.current.get(event)!.add(callback);
  }, []);

  const removeEventListener = useCallback((event: string, callback: (data: EventData) => void) => {
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        eventListeners.current.delete(event);
      }
    }
  }, []);

  // Phase 4: Participant management helper functions
  const createParticipantInfo = useCallback((participant: Participant): ParticipantInfo => {
    const isAgent = participant.metadata ? JSON.parse(participant.metadata).isAgent === true : false;
    return {
      id: participant.identity,
      name: participant.name || participant.identity,
      email: participant.metadata ? JSON.parse(participant.metadata).email : undefined,
      isAgent,
      joinedAt: Date.now(),
      lastSeen: Date.now()
    };
  }, []);

  const createParticipantState = useCallback((participant: Participant): ParticipantState => {
    // Check if participant has audio tracks and if they're muted
    const audioTrack = participant.audioTrackPublications.values().next().value;
    const isMuted = audioTrack ? audioTrack.isMuted : true;
    
    return {
      id: participant.identity,
      isMuted,
      isSpeaking: false, // Will be updated by audio level events
      isConnected: participant.connectionQuality !== 'lost',
      audioLevel: 0,
      lastActivity: Date.now()
    };
  }, []);

  const updateParticipantList = useCallback(() => {
    if (!room || !sessionId) return;

    const allParticipants = [room.localParticipant, ...Array.from(room.remoteParticipants.values())];
    
    const participantInfos = allParticipants.map(createParticipantInfo);
    const participantStateMap: Record<string, ParticipantState> = {};
    
    allParticipants.forEach(participant => {
      participantStateMap[participant.identity] = createParticipantState(participant);
    });

    setParticipants(participantInfos);
    setParticipantStates(participantStateMap);

    // Set current user info
    const currentUserInfo = createParticipantInfo(room.localParticipant);
    setCurrentUser(currentUserInfo);

    // Send participant list update via data channel
    try {
      const updateMessage = createParticipantListUpdate(sessionId, participantInfos, participantStateMap);
      room.localParticipant.publishData(
        new TextEncoder().encode(JSON.stringify(updateMessage)),
        { reliable: true }
      );
      console.log('[UnifiedVoice] Participant list update sent:', updateMessage);
    } catch (error) {
      console.error('[UnifiedVoice] Failed to send participant list update:', error);
    }

    // Emit event for external listeners
    emitEvent('participant_list_updated', {
      participants: participantInfos,
      participantStates: participantStateMap,
      totalCount: participantInfos.length
    });
  }, [room, sessionId, createParticipantInfo, createParticipantState, emitEvent]);

  // Send interruption signal via data channel (enhanced with structured messaging)
  const sendInterruption = useCallback(() => {
    if (!room || !sessionId) {
      console.warn('[UnifiedVoice] Cannot send interruption - not connected to room or no session ID');
      return;
    }

    try {
      const interruptionMessage = createInterruptionMessage(
        sessionId,
        room.localParticipant.identity,
        'User speech detected'
      );
      
      room.localParticipant.publishData(
        new TextEncoder().encode(JSON.stringify(interruptionMessage)),
        { reliable: true }
      );
      
      console.log('[UnifiedVoice] Structured interruption signal sent:', interruptionMessage);
    } catch (error) {
      console.error('[UnifiedVoice] Failed to send interruption signal:', error);
    }
  }, [room, sessionId]);

  // Connect to LiveKit room with proper retry logic and VAD error handling
  const connect = useCallback(async () => {
    // Prevent multiple simultaneous connection attempts
    if (isConnecting || (room && connectionState === ConnectionState.Connected)) {
      console.log('[UnifiedVoice] Connection already in progress or established');
      return;
    }

    // Implement connection retry backoff (max 3 attempts with 5 second intervals)
    const now = Date.now();
    const MIN_RETRY_INTERVAL = 5000; // 5 seconds
    const MAX_ATTEMPTS = 3;

    if (connectionAttempts >= MAX_ATTEMPTS) {
      console.log('[UnifiedVoice] Max connection attempts reached, stopping retries');
      setConnectionError('Max connection attempts reached. Please refresh the page to try again.');
      return;
    }

    if (now - lastConnectionAttempt < MIN_RETRY_INTERVAL) {
      console.log('[UnifiedVoice] Too soon to retry connection, waiting...');
      return;
    }

    try {
      console.log(`[UnifiedVoice] Connecting to LiveKit room (attempt ${connectionAttempts + 1}/${MAX_ATTEMPTS})...`);
      setIsConnecting(true);
      setConnectionError(null);
      setConnectionState(ConnectionState.Connecting);
      setLastConnectionAttempt(now);
      setConnectionAttempts(prev => prev + 1);

      // Get voice token from Supabase edge function (matching backend implementation)
      const { createClient } = await import('@supabase/supabase-js');
      
      // Import environment utilities
      const { voiceEnv } = await import('../../utils/env');
      
      // Create Supabase client
      const supabase = createClient(voiceEnv.supabase.url, voiceEnv.supabase.anonKey);
      
      // Get current user for authentication
      const { data: { user } } = await supabase.auth.getUser();
      
      // Extract rep room ID from URL and use sessionId from props
      const pathParts = window.location.pathname.split('/');
      
      // For /rroom-v3/t1/session-123 format
      let repRoomId = 't1';
      
      if (window.location.pathname.includes('/rroom-v3/')) {
        const rRoomIndex = pathParts.findIndex(part => part === 'rroom-v3');
        if (rRoomIndex !== -1 && pathParts[rRoomIndex + 1]) {
          repRoomId = pathParts[rRoomIndex + 1]; // e.g., 't1'
        }
      } else if (window.location.pathname.includes('/rroom/')) {
        // Legacy format support
        repRoomId = window.location.pathname.split('/rroom/')[1]?.split('/')[0] || 't1';
      }
      
      // CRITICAL FIX: Prioritize sessionId prop from RepRoomSessionPage
      const currentSessionId = sessionId || (() => {
        console.log('[UnifiedVoice] No sessionId prop provided, attempting URL extraction...');
        if (window.location.pathname.includes('/rroom-v3/')) {
          const rRoomIndex = pathParts.findIndex(part => part === 'rroom-v3');
          if (rRoomIndex !== -1 && pathParts[rRoomIndex + 2]) {
            const extractedSessionId = pathParts[rRoomIndex + 2];
            console.log('[UnifiedVoice] Extracted sessionId from URL:', extractedSessionId);
            return extractedSessionId;
          }
        }
        console.warn('[UnifiedVoice] No sessionId found in URL or props');
        return null;
      })();
      
      // Validate that we have a sessionId
      if (!currentSessionId) {
        throw new Error('No session ID available for voice token request. Session ID is required for Rep Room v3.');
      }
      
      const tokenRequest = {
        rep_room_id: repRoomId,
        session_id: currentSessionId, // CRITICAL: Pass session ID to voice token function
        participant_name: user?.email || 'Anonymous User'
      };
      
      console.log('[UnifiedVoice] Token request details:', {
        ...tokenRequest,
        sessionIdSource: sessionId ? 'props' : 'url_extraction',
        expectedRoomName: `rrs-${repRoomId}-${currentSessionId}`,
        urlPath: window.location.pathname
      });
      
      // Emit debug event for token request
      emitEvent('debug:token_request', tokenRequest);
      
      // Call the edge function to get LiveKit token
      const { data: tokenResponse, error: tokenError } = await supabase.functions.invoke('rep-room-voice-token', {
        body: tokenRequest
      });
      
      if (tokenError || !tokenResponse?.success) {
        const errorMsg = `Failed to get LiveKit token: ${tokenError?.message || tokenResponse?.error?.message || 'Unknown error'}`;
        emitEvent('debug:token_error', { error: errorMsg, tokenError, tokenResponse });
        throw new Error(errorMsg);
      }
      
      const { token, room_name, livekit_url } = tokenResponse.data;
      console.log('[UnifiedVoice] LiveKit token received:', { room_name, livekit_url });
      
      // Emit debug event for successful token
      emitEvent('debug:token_success', { token: '***', room_name, livekit_url, full_response: tokenResponse.data });
      
      const wsUrl = livekit_url;

      // Create new room instance
      const newRoom = new Room({
        // Optimized WebRTC settings for ultra-low latency
        adaptiveStream: true,
        dynacast: true,
        audioCaptureDefaults: {
          autoGainControl: true,
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000,
          channelCount: 1
        }
      });

      // Set up event listeners
      newRoom.on(RoomEvent.Connected, () => {
        console.log('[UnifiedVoice] Connected to LiveKit room');
        setConnectionState(ConnectionState.Connected);
        setConnectionError(null);
        setIsConnecting(false);
        setConnectionAttempts(0); // Reset attempts on successful connection
        
        // Emit debug event for connection
        emitEvent('debug:room_connected', {
          room_name: newRoom.name,
          state: newRoom.state,
          metadata: newRoom.metadata,
          participants: newRoom.numParticipants
        });
        
        // Phase 4: Initialize participant list on connection
        updateParticipantList();
        
        // Start VAD when connected with enhanced error handling
        try {
          console.log('[UnifiedVoice] Attempting to start VAD...');
          console.log('[UnifiedVoice] VAD Configuration:', {
            modelURL: '/silero_vad.onnx',
            workletURL: '/vad.worklet.bundle.min.js'
          });
          vad.start();
          console.log('[UnifiedVoice] VAD started successfully');
        } catch (vadError) {
          console.error('[UnifiedVoice] VAD failed to start:', vadError);
          console.error('[UnifiedVoice] VAD Error Details:', {
            message: vadError instanceof Error ? vadError.message : String(vadError),
            stack: vadError instanceof Error ? vadError.stack : undefined,
            name: vadError instanceof Error ? vadError.name : undefined
          });
          
          // Check if this is the specific model loading error
          if (vadError instanceof Error && (vadError.message.includes('silero_vad') || vadError.message.includes('state'))) {
            console.error('[UnifiedVoice] 🚨 VAD Model Loading Error Detected!');
            console.error('[UnifiedVoice] Model file should be available at: /silero_vad_v5.onnx');
            console.error('[UnifiedVoice] Error details:', vadError.message);
            console.error('[UnifiedVoice] Check that the VAD setup script has run: npm run setup-vad');
            console.error('[UnifiedVoice] Check that the file is accessible via HTTP');
            console.error('[UnifiedVoice] Try running: npm run setup-vad');
          }
          
          console.log('[UnifiedVoice] Continuing without VAD - voice features will be limited');
          // Don't set connection error for VAD failures - just log and continue
          // The connection is still valid, just without client-side VAD
        }
      });
      newRoom.on(RoomEvent.Disconnected, (reason) => {
        console.log('[UnifiedVoice] Disconnected from LiveKit room');
        console.log('[UnifiedVoice] DISCONNECT REASON:', reason);
        console.trace('[UnifiedVoice] DISCONNECT EVENT TRACE:');
        setConnectionState(ConnectionState.Disconnected);
        setIsAgentSpeaking(false);
        setIsConnecting(false);
        
        // Stop VAD when disconnected
        try {
          vad.pause();
        } catch (vadError) {
          console.error('[UnifiedVoice] Error stopping VAD:', vadError);
        }
      });

      newRoom.on(RoomEvent.ConnectionStateChanged, (state) => {
        console.log('[UnifiedVoice] Connection state changed:', state);
        setConnectionState(state);
        
        if (state === ConnectionState.Disconnected || state === ConnectionState.Reconnecting) {
          setIsConnecting(false);
        }
      });

      newRoom.on(RoomEvent.TrackSubscribed, (track: RemoteTrack) => {
        if (track.kind === Track.Kind.Audio) {
          console.log('[UnifiedVoice] Agent audio track subscribed');
          setIsAgentSpeaking(true);
          
          // Attach audio track to audio element for playback
          const audioElement = document.createElement('audio');
          audioElement.autoplay = true;
          track.attach(audioElement);
        }
      });

      newRoom.on(RoomEvent.TrackUnsubscribed, (track: RemoteTrack) => {
        if (track.kind === Track.Kind.Audio) {
          console.log('[UnifiedVoice] Agent audio track unsubscribed');
          setIsAgentSpeaking(false);
        }
      });

      // Phase 4: Enhanced participant event handling
      newRoom.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
        console.log('[UnifiedVoice] Participant connected:', participant.identity);
        
        // Update participant list
        updateParticipantList();
        
        // Send system event message
        if (sessionId) {
          try {
            const participantInfo = createParticipantInfo(participant);
            const systemMessage = createSystemEventMessage(
              sessionId,
              'participant_joined',
              participantInfo,
              `${participantInfo.name} joined the session`
            );
            
            newRoom.localParticipant.publishData(
              new TextEncoder().encode(JSON.stringify(systemMessage)),
              { reliable: true }
            );
            
            console.log('[UnifiedVoice] Participant joined system message sent:', systemMessage);
          } catch (error) {
            console.error('[UnifiedVoice] Failed to send participant joined message:', error);
          }
        }
        
        // Emit event for external listeners
        emitEvent('participant_joined', {
          participant: createParticipantInfo(participant),
          totalParticipants: newRoom.numParticipants
        });
      });

      newRoom.on(RoomEvent.ParticipantDisconnected, (participant: RemoteParticipant) => {
        console.log('[UnifiedVoice] Participant disconnected:', participant.identity);
        
        // Update participant list
        updateParticipantList();
        
        // Send system event message
        if (sessionId) {
          try {
            const participantInfo = createParticipantInfo(participant);
            const systemMessage = createSystemEventMessage(
              sessionId,
              'participant_left',
              participantInfo,
              `${participantInfo.name} left the session`
            );
            
            newRoom.localParticipant.publishData(
              new TextEncoder().encode(JSON.stringify(systemMessage)),
              { reliable: true }
            );
            
            console.log('[UnifiedVoice] Participant left system message sent:', systemMessage);
          } catch (error) {
            console.error('[UnifiedVoice] Failed to send participant left message:', error);
          }
        }
        
        // Emit event for external listeners
        emitEvent('participant_left', {
          participant: createParticipantInfo(participant),
          totalParticipants: newRoom.numParticipants
        });
      });

      // Track mute/unmute events for participant state updates
      newRoom.on(RoomEvent.TrackMuted, (publication, participant) => {
        if (publication.kind === Track.Kind.Audio) {
          console.log('[UnifiedVoice] Participant muted:', participant.identity);
          
          // Update participant state
          setParticipantStates(prev => ({
            ...prev,
            [participant.identity]: {
              ...prev[participant.identity],
              isMuted: true,
              lastActivity: Date.now()
            }
          }));
          
          // Send voice activity message
          if (sessionId) {
            try {
              const voiceActivityMessage = createVoiceActivityMessage(
                sessionId,
                participant.identity,
                'muted'
              );
              
              newRoom.localParticipant.publishData(
                new TextEncoder().encode(JSON.stringify(voiceActivityMessage)),
                { reliable: true }
              );
            } catch (error) {
              console.error('[UnifiedVoice] Failed to send mute activity message:', error);
            }
          }
        }
      });

      newRoom.on(RoomEvent.TrackUnmuted, (publication, participant) => {
        if (publication.kind === Track.Kind.Audio) {
          console.log('[UnifiedVoice] Participant unmuted:', participant.identity);
          
          // Update participant state
          setParticipantStates(prev => ({
            ...prev,
            [participant.identity]: {
              ...prev[participant.identity],
              isMuted: false,
              lastActivity: Date.now()
            }
          }));
          
          // Send voice activity message
          if (sessionId) {
            try {
              const voiceActivityMessage = createVoiceActivityMessage(
                sessionId,
                participant.identity,
                'unmuted'
              );
              
              newRoom.localParticipant.publishData(
                new TextEncoder().encode(JSON.stringify(voiceActivityMessage)),
                { reliable: true }
              );
            } catch (error) {
              console.error('[UnifiedVoice] Failed to send unmute activity message:', error);
            }
          }
        }
      });

      newRoom.on(RoomEvent.DataReceived, (payload: Uint8Array) => {
        try {
          const data = JSON.parse(new TextDecoder().decode(payload));
          console.log('[UnifiedVoice] Data received:', data);
          
          // Phase 4: Enhanced structured message handling
          if (isValidDataChannelMessage(data)) {
            console.log('[UnifiedVoice] Processing structured data channel message:', data.type);
            
            // Handle new structured message types
            switch (data.type) {
              case 'participant_list_update':
                console.log('[UnifiedVoice] Participant list update received:', data);
                if (data.content) {
                  setParticipants(data.content.participants);
                  setParticipantStates(data.content.participantStates);
                  emitEvent('participant_list_updated', data.content);
                }
                break;
                
              case 'system_event':
                console.log('[UnifiedVoice] System event received:', data);
                emitEvent('system_event', {
                  event: data.event,
                  participant: data.content?.participant,
                  message: data.content?.message
                });
                break;
                
              case 'voice_activity':
                console.log('[UnifiedVoice] Voice activity received:', data);
                if (data.content) {
                  // Update participant state based on voice activity
                  setParticipantStates(prev => ({
                    ...prev,
                    [data.content.participantId]: {
                      ...prev[data.content.participantId],
                      isSpeaking: data.content.activity === 'speaking_start',
                      isMuted: data.content.activity === 'muted',
                      audioLevel: data.content.audioLevel || 0,
                      lastActivity: Date.now()
                    }
                  }));
                  
                  emitEvent('voice_activity', data.content);
                }
                break;
                
              case 'chat_message':
                console.log('[UnifiedVoice] Chat message received:', data);
                if (data.content) {
                  // Add chat message to visible messages
                  const chatMessage: VoiceMessage = {
                    id: data.messageId || `chat_${Date.now()}`,
                    role: data.content.sender.isAgent ? 'assistant' : 'user',
                    content: data.content.text,
                    timestamp: data.timestamp,
                    isComplete: true
                  };
                  
                  setVisibleMessages(prev => [...prev, chatMessage]);
                  emitEvent('chat_message_received', data.content);
                }
                break;
                
              case 'transcription':
                console.log('[UnifiedVoice] Transcription message received:', data);
                if (data.content && !data.content.isInterim) {
                  // Add final transcription to visible messages
                  const transcriptMessage: VoiceMessage = {
                    id: data.messageId || `transcript_${Date.now()}`,
                    role: data.content.speaker.isAgent ? 'assistant' : 'user',
                    content: data.content.text,
                    timestamp: data.timestamp,
                    isComplete: true
                  };
                  
                  setVisibleMessages(prev => [...prev, transcriptMessage]);
                  emitEvent('transcription_received', data.content);
                }
                break;
                
              case 'interruption':
                console.log('[UnifiedVoice] Interruption acknowledgment received:', data);
                emitEvent('interruption_acknowledged', data.content);
                break;
                
              case 'heartbeat':
                // Handle heartbeat messages silently
                if (data.content?.status === 'ping') {
                  // Respond with pong if needed
                  console.log('[UnifiedVoice] Heartbeat ping received');
                }
                break;
                
              case 'error':
                console.error('[UnifiedVoice] Error message received:', data);
                emitEvent('error_received', data.content);
                break;
                
              default:
                // Fall through to legacy message handling
                console.log('[UnifiedVoice] Unknown structured message type, checking legacy handlers:', data.type);
                break;
            }
          }
          
          // Handle legacy AG-UI data channel events with proper switch statement
          switch (data.type) {
            case 'TEXT_MESSAGE_START':
            case 'TextMessageStart':
              console.log('[UnifiedVoice] AG-UI TEXT_MESSAGE_START received:', data);
              {
                const messageId = data.content?.id || data.id;
                const messageRole = data.content?.role || data.role || 'assistant';
                
                // Create new message and add to active messages
                const newMessage: VoiceMessage = {
                  id: messageId,
                  role: messageRole as 'user' | 'assistant',
                  content: '',
                  timestamp: Date.now(),
                  isComplete: false
                };
                
                setActiveMessages(prev => new Map(prev.set(messageId, newMessage)));
                
                emitEvent('ag_ui_message_start', {
                  id: messageId,
                  role: messageRole
                });
              }
              break;
              
            case 'TEXT_MESSAGE_CONTENT':
            case 'TextMessageContent':
              console.log('[UnifiedVoice] AG-UI TEXT_MESSAGE_CONTENT received:', data);
              {
                const messageId = data.content?.id || data.id;
                const delta = data.content?.delta || data.delta || data.text || '';
                
                // Update active message content
                setActiveMessages(prev => {
                  const updated = new Map(prev);
                  const existingMessage = updated.get(messageId);
                  if (existingMessage) {
                    updated.set(messageId, {
                      ...existingMessage,
                      content: existingMessage.content + delta
                    });
                  }
                  return updated;
                });
                
                emitEvent('ag_ui_message_content', {
                  id: messageId,
                  delta
                });
              }
              break;
              
            case 'TEXT_MESSAGE_END':
            case 'TextMessageEnd':
              console.log('[UnifiedVoice] AG-UI TEXT_MESSAGE_END received:', data);
              {
                const messageId = data.content?.id || data.id;
                
                // Move message from active to visible messages
                setActiveMessages(prev => {
                  const updated = new Map(prev);
                  const completedMessage = updated.get(messageId);
                  if (completedMessage) {
                    const finalMessage: VoiceMessage = {
                      ...completedMessage,
                      isComplete: true
                    };
                    
                    // Add to visible messages
                    setVisibleMessages(prevMessages => [...prevMessages, finalMessage]);
                    updated.delete(messageId);
                  }
                  return updated;
                });
                
                emitEvent('ag_ui_message_end', {
                  id: messageId
                });
              }
              break;
              
            case 'USER_TRANSCRIPT_FINAL':
              console.log('[UnifiedVoice] AG-UI USER_TRANSCRIPT_FINAL received:', data);
              {
                const transcriptText = data.content?.text || data.text || '';
                const timestamp = data.timestamp || Date.now();
                
                // Create user message from transcript
                const userMessage: VoiceMessage = {
                  id: `user_${timestamp}`,
                  role: 'user',
                  content: transcriptText,
                  timestamp,
                  isComplete: true
                };
                
                // Add to visible messages
                setVisibleMessages(prevMessages => [...prevMessages, userMessage]);
                
                emitEvent('user_transcript_final', {
                  text: transcriptText,
                  timestamp
                });
              }
              break;
              
            // Legacy message types for backward compatibility
            case 'transcript':
              console.log('[UnifiedVoice] Legacy transcript received:', data);
              emitEvent('final_transcript', data.text || '');
              break;
              
            case 'agent_speaking_start':
              console.log('[UnifiedVoice] Agent speaking start received');
              setIsAgentSpeaking(true);
              break;
              
            case 'agent_speaking_end':
              console.log('[UnifiedVoice] Agent speaking end received');
              setIsAgentSpeaking(false);
              break;
              
            case 'interruption':
              console.log('[UnifiedVoice] Interruption acknowledgment received');
              // Handle interruption acknowledgment from backend
              break;
              
            default:
              // Only log unknown types that aren't expected AG-UI events
              if (!['heartbeat', 'ping', 'pong', 'status'].includes(data.type)) {
                console.log('[UnifiedVoice] Unhandled data message type:', data.type, data);
              }
              break;
          }
        } catch (error) {
          console.error('[UnifiedVoice] Failed to parse data message:', error);
        }
      });

      // Connect to room
      await newRoom.connect(wsUrl, token);
      setRoom(newRoom);

      // CRITICAL FIX: Create and publish local audio track for transcription
      try {
        console.log('[UnifiedVoice] Creating local audio track...');
        
        // Import Track source enum
        const { Track } = await import('livekit-client');
        
        // Request microphone access with optimized settings
        const tracks = await newRoom.localParticipant.createTracks({
          audio: {
            autoGainControl: true,
            echoCancellation: true,
            noiseSuppression: true,
            sampleRate: 16000,
            channelCount: 1
          }
        });
        
        // Find the audio track
        const audioTrack = tracks.find(track => track.kind === Track.Kind.Audio);
        
        if (audioTrack) {
          // Publish the audio track to the room
          await newRoom.localParticipant.publishTrack(audioTrack, {
            name: 'microphone',
            source: Track.Source.Microphone
          });
        } else {
          throw new Error('Failed to create audio track');
        }
        
        console.log('[UnifiedVoice] Local audio track created and published successfully');
        console.log('[UnifiedVoice] Audio track details:', {
          kind: audioTrack.kind,
          isMuted: audioTrack.isMuted,
          source: audioTrack.source
        });
        
      } catch (audioError) {
        console.error('[UnifiedVoice] Failed to create/publish audio track:', audioError);
        console.error('[UnifiedVoice] This will prevent voice transcription from working');
        
        // Check for common permission issues
        if (audioError instanceof Error) {
          if (audioError.name === 'NotAllowedError') {
            console.error('[UnifiedVoice] 🚨 MICROPHONE PERMISSION DENIED!');
            console.error('[UnifiedVoice] User needs to grant microphone access for voice transcription');
            setConnectionError('Microphone permission denied. Please allow microphone access and try again.');
          } else if (audioError.name === 'NotFoundError') {
            console.error('[UnifiedVoice] 🚨 NO MICROPHONE FOUND!');
            console.error('[UnifiedVoice] No microphone device available');
            setConnectionError('No microphone found. Please connect a microphone and try again.');
          } else {
            console.error('[UnifiedVoice] Audio track error details:', {
              name: audioError.name,
              message: audioError.message,
              stack: audioError.stack
            });
            setConnectionError(`Audio setup failed: ${audioError.message}`);
          }
        }
        
        // Don't disconnect the room for audio errors - connection is still valid
        // Just log the issue and continue without audio publishing
      }

      console.log('[UnifiedVoice] Successfully connected to LiveKit room');
    } catch (error) {
      console.error('[UnifiedVoice] Failed to connect to LiveKit room:', error);
      setConnectionError(error instanceof Error ? error.message : 'Connection failed');
      setConnectionState(ConnectionState.Disconnected);
      setIsConnecting(false);
      
      // Clean up room on connection failure
      if (room) {
        try {
          console.trace('[UnifiedVoice] CLEANUP DISCONNECT TRACE - Connection failed:');
          await room.disconnect();
        } catch (cleanupError) {
          console.error('[UnifiedVoice] Error cleaning up failed room:', cleanupError);
        }
        setRoom(null);
      }
    }
  }, [isConnecting, room, connectionState, connectionAttempts, lastConnectionAttempt, voiceConfig, vad, emitEvent]);

  // Disconnect from LiveKit room
  const disconnect = useCallback(async () => {
    if (!room) {
      console.log('[UnifiedVoice] No room to disconnect from');
      return;
    }

    try {
      console.log('[UnifiedVoice] Disconnecting from LiveKit room...');
      console.trace('[UnifiedVoice] DISCONNECT TRACE - Called from:');
      
      // Stop VAD
      vad.pause();
      
      // Disconnect room
      await room.disconnect();
      setRoom(null);
      setConnectionState(ConnectionState.Disconnected);
      setIsAgentSpeaking(false);
      setConnectionError(null);
      
      console.log('[UnifiedVoice] Successfully disconnected from LiveKit room');
    } catch (error) {
      console.error('[UnifiedVoice] Error disconnecting from LiveKit room:', error);
    }
  }, [room, vad]);

  // Refs for cleanup access without triggering re-renders
  const roomRef = useRef(room);
  const vadRef = useRef(vad);
  
  // Update refs when values change
  roomRef.current = room;
  vadRef.current = vad;

  // Cleanup on unmount with enhanced debugging - FIXED: Remove vadListening from dependencies
  useEffect(() => {
    console.log('[UnifiedVoice] useEffect cleanup setup - Dependencies:', {
      hasRoom: !!room,
      vadListening: vad.listening,
      voiceConfigEnabled: voiceConfig.enabled,
      voiceConfigProvider: voiceConfig.provider,
      timestamp: new Date().toISOString()
    });
    
    return () => {
      console.log('[UnifiedVoice] 🚨 CLEANUP TRIGGERED - Component unmounting!');
      console.log('[UnifiedVoice] Cleanup reason analysis:', {
        hasRoom: !!roomRef.current,
        vadListening: vadRef.current.listening,
        voiceConfigEnabled: voiceConfig.enabled,
        timestamp: new Date().toISOString()
      });
      console.trace('[UnifiedVoice] CLEANUP ON UNMOUNT TRACE:');
      
      if (roomRef.current) {
        console.log('[UnifiedVoice] Disconnecting room due to component unmount');
        roomRef.current.disconnect();
      }
      vadRef.current.pause();
    };
  }, [voiceConfig.enabled]); // CRITICAL FIX: Only depend on stable voiceConfig.enabled, not the changing vad object

  // Map state to unified interface - use useMemo to prevent unnecessary re-renders
  const state: UnifiedVoiceState = useMemo(() => ({
    isConnecting: isConnecting || connectionState === ConnectionState.Connecting,
    isListening: vad.listening && connectionState === ConnectionState.Connected,
    isSpeaking: vad.userSpeaking,
    connectionError,
    isAgentSpeaking,
    vadActive: vad.listening,
    visibleMessages,
    // Phase 4: Enhanced participant tracking
    participants,
    participantStates,
    currentUser,
    totalParticipants: participants.length
  }), [
    isConnecting,
    connectionState,
    vad.listening,
    vad.userSpeaking,
    connectionError,
    isAgentSpeaking,
    visibleMessages,
    participants,
    participantStates,
    currentUser
  ]);

  // Map controls to unified interface - use useMemo to prevent unnecessary re-renders
  const controls: UnifiedVoiceControls = useMemo(() => ({
    connect,
    disconnect,
    sendInterruption,
    on: addEventListener,
    off: removeEventListener
  }), [connect, disconnect, sendInterruption, addEventListener, removeEventListener]);

  // Track voice active state
  const isVoiceActive = connectionState === ConnectionState.Connected && voiceConfig.enabled;

  // Context value - use useMemo to prevent unnecessary re-renders
  const contextValue: UnifiedVoiceContextType = useMemo(() => ({
    state,
    controls,
    isVoiceActive
  }), [state, controls, isVoiceActive]);

  return (
    <UnifiedVoiceContext.Provider value={contextValue}>
      <div data-testid="voice-provider">
        {children}
      </div>
    </UnifiedVoiceContext.Provider>
  );
}