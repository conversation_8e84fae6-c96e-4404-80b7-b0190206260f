import React from 'react';
import { Bo<PERSON>, ArrowRight, Users, Zap } from 'lucide-react';
import { Agent, AgentHandoff } from '../../types/rep-room';
import { StatusIndicator } from './StatusIndicator';
import { AgentHandoffIndicator } from './agents/AgentHandoffIndicator';

interface AgentCardProps {
  agent: Agent;
  isActive?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
  className?: string;
  size?: 'default' | 'compact';
  // Orchestration features
  activeHandoff?: AgentHandoff;
  handoffTarget?: Agent;
  isDelegating?: boolean;
  specialistAgents?: Agent[];
  showOrchestration?: boolean;
}

export const AgentCard: React.FC<AgentCardProps> = ({
  agent,
  isActive = false,
  isSelected = false,
  onClick,
  className = '',
  size = 'default',
  // Orchestration props
  activeHandoff,
  handoffTarget,
  isDelegating = false,
  specialistAgents = [],
  showOrchestration = false
}) => {
  const isMainAgent = agent.type === 'main';
  const isSpeaking = agent.status === 'speaking';
  const isDelegatingStatus = agent.status === 'delegating' || isDelegating;
  
  // Dynamic sizing based on agent type and size prop
  const sizeClasses = size === 'compact'
    ? 'p-2 min-h-[60px]'
    : isMainAgent
      ? 'p-4 min-h-[120px]'
      : 'p-3 min-h-[80px]';
  
  // Active/Selected state styling with orchestration states
  const stateClasses = (isActive || isSelected)
    ? 'border-blue-500 bg-blue-50'
    : isDelegatingStatus
      ? 'border-purple-500 bg-purple-50'
      : activeHandoff
        ? 'border-orange-500 bg-orange-50'
        : 'border-gray-200 hover:border-gray-300';
  
  // Base card styling
  const baseClasses = `
    border-2 rounded-lg transition-all duration-200 cursor-pointer
    hover:shadow-md bg-white
  `;

  // Enhanced audio visualization bars for speaking agents
  const AudioBars = () => {
    if (!isSpeaking) return null;
    
    return (
      <div className="flex items-center space-x-1 mt-2" aria-label="Audio activity">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="w-1 bg-gradient-to-t from-green-400 to-green-600 rounded-full animate-pulse"
            style={{
              height: `${Math.random() * 20 + 6}px`,
              animationDelay: `${i * 0.15}s`,
              animationDuration: '0.8s'
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <div 
      className={`${baseClasses} ${sizeClasses} ${stateClasses} ${className}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick?.();
        }
      }}
      aria-label={`${agent.name} - ${agent.status}`}
    >
      <div className="flex items-start space-x-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {agent.avatar ? (
            <img 
              src={agent.avatar} 
              alt={agent.name}
              className={`rounded-full ${isMainAgent ? 'w-12 h-12' : 'w-8 h-8'}`}
            />
          ) : (
            <div className={`rounded-full bg-blue-100 flex items-center justify-center ${isMainAgent ? 'w-12 h-12' : 'w-8 h-8'}`}>
              <Bot className={`text-blue-600 ${isMainAgent ? 'w-6 h-6' : 'w-4 h-4'}`} />
            </div>
          )}
        </div>

        {/* Agent Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className={`font-medium text-gray-900 truncate ${isMainAgent ? 'text-base' : 'text-sm'}`}>
              {agent.name}
            </h3>
            <StatusIndicator status={agent.status} size={isMainAgent ? 'md' : 'sm'} />
          </div>
          
          {/* Agent Type Badge */}
          <div className="flex items-center space-x-2 mt-1">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              isMainAgent 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {isMainAgent ? 'Main Agent' : agent.specialization || 'Specialist'}
            </span>
          </div>

          {/* Status Text */}
          <p className="text-sm text-gray-500 mt-1 capitalize">
            {agent.status === 'speaking' ? 'Speaking...' :
             agent.status === 'thinking' ? 'Thinking...' :
             agent.status === 'working' ? 'Working...' :
             agent.status === 'delegating' ? 'Delegating...' :
             agent.status === 'ready' ? 'Ready' : 'Idle'}
          </p>

          {/* Audio Visualization */}
          <AudioBars />

          {/* Orchestration Features */}
          {showOrchestration && (
            <div className="mt-3 space-y-2">
              {/* Active Handoff Indicator */}
              {activeHandoff && handoffTarget && (
                <div className="p-2 bg-orange-50 rounded-lg border border-orange-200">
                  <AgentHandoffIndicator
                    handoff={activeHandoff}
                    fromAgent={agent}
                    toAgent={handoffTarget}
                    showDetails={false}
                    animated={true}
                  />
                </div>
              )}

              {/* Delegation Indicator */}
              {isDelegatingStatus && (
                <div className="flex items-center space-x-2 p-2 bg-purple-50 rounded-lg border border-purple-200">
                  <Zap className="w-4 h-4 text-purple-600 animate-pulse" />
                  <span className="text-sm text-purple-800 font-medium">
                    Delegating to specialist...
                  </span>
                </div>
              )}

              {/* Active Specialist Agents */}
              {specialistAgents.length > 0 && isMainAgent && (
                <div className="space-y-1">
                  <div className="flex items-center space-x-1">
                    <Users className="w-3 h-3 text-gray-500" />
                    <span className="text-xs text-gray-600 font-medium">
                      Active Specialists ({specialistAgents.length})
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {specialistAgents.map(specialist => (
                      <div
                        key={specialist.id}
                        className="flex items-center space-x-1 px-2 py-1 bg-green-100 rounded-full"
                      >
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                        <span className="text-xs text-green-800 font-medium">
                          {specialist.specialization || specialist.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Coordination Status for Specialists */}
              {!isMainAgent && agent.status !== 'idle' && (
                <div className="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                  <span className="text-sm text-blue-800">
                    Coordinating with main agent
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};