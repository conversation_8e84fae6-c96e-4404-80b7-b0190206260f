import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotChat } from '@copilotkit/react-ui';
import '@copilotkit/react-ui/styles.css';
import { RepRoom } from '../../types/rep-rooms';
import { VoiceConfig, VoiceState, VoiceError, DEFAULT_NARRATION_CONFIG } from '../../types/voice';
import { VoiceSettings, DEFAULT_VOICE_SETTINGS } from '../../types/voice-config';
import { Button } from '../ui/button';
import { MessageSquare, X, ChevronDown, ChevronUp } from 'lucide-react';
import { toast } from 'sonner';
import {
  RepRoomVoiceInterface,
  VoiceStatusIndicator,
  InterimTranscriptDisplay,
  VoiceMessageSync
} from './voice';
import { useAgentResponseInterceptor } from '../../hooks/useAgentResponseInterceptor';
import { useAgentMetadata } from '../../hooks/useAgentMetadata';
import { useCopilotMessageMetadata } from '../../hooks/useCopilotMessageMetadata';
import './voice/voice-animations.css';

interface RepRoomChatInterfaceProps {
  repRoom: RepRoom;
  onClose?: () => void;
}

/**
 * RepRoomChatInterface - CopilotKit-based chat interface for rep rooms
 * Replaces mock responses with real agent interactions while preserving all visual customization
 */
export function RepRoomChatInterface({ repRoom, onClose }: RepRoomChatInterfaceProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState('⏳ Initializing...');
  const [isHeaderExpanded, setIsHeaderExpanded] = useState(true);
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings | null>(null);
  const [voiceState, setVoiceState] = useState<VoiceState>('disconnected');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [autoNarrationEnabled, setAutoNarrationEnabled] = useState(true);

  // Ref for CopilotKit container to inject metadata
  const copilotContainerRef = useRef<HTMLDivElement>(null);

  // Extract rep room configuration
  const agentName = repRoom.user_agent_clone?.name || 'AI Assistant';
  const agentId = repRoom.user_agent_clone_id;
  const themeColor = repRoom.settings?.appearance?.theme_color || '#3B82F6';
  const avatarUrl = repRoom.settings?.appearance?.avatar_url || repRoom.user_agent_clone?.avatar_url;

  // Get agent metadata for display
  const { metadata: agentMetadata } = useAgentMetadata({
    repRoomId: repRoom.id,
    agentCloneId: repRoom.user_agent_clone_id,
    tenantActivationId: undefined // Will be fetched from the database
  });

  // Inject metadata into CopilotKit messages
  useCopilotMessageMetadata(copilotContainerRef, agentMetadata ? {
    repRoomName: agentMetadata.repRoomName || repRoom.title,
    agentCloneName: agentMetadata.agentCloneName || agentName,
    agentTypeName: agentMetadata.agentTypeName || 'AI Assistant'
  } : null);

  // Voice message synchronization
  const voiceMessageSync = useMemo(() => {
    return new VoiceMessageSync();
  }, []);

  // Initialize header expansion state from rep room settings
  useEffect(() => {
    if (repRoom?.settings?.header?.expanded_by_default !== undefined) {
      setIsHeaderExpanded(repRoom.settings.header.expanded_by_default);
    }
  }, [repRoom]);

  // Hide powered by text
  useEffect(() => {
    const hidePoweredByText = () => {
      // Find and hide any elements containing "powered by" text
      const elements = document.querySelectorAll('*');
      elements.forEach(element => {
        if (element.textContent?.toLowerCase().includes('powered by')) {
          const htmlElement = element as HTMLElement;
          htmlElement.style.display = 'none';
          htmlElement.style.visibility = 'hidden';
          htmlElement.style.opacity = '0';
          htmlElement.style.height = '0';
          htmlElement.style.padding = '0';
          htmlElement.style.margin = '0';
        }
      });
    };

    // Run immediately and then periodically to catch dynamically added content
    hidePoweredByText();
    const interval = setInterval(hidePoweredByText, 1000);

    return () => clearInterval(interval);
  }, []);

  // Convert VoiceSettings to VoiceConfig for backward compatibility
  const convertToVoiceConfig = useCallback((settings: VoiceSettings): VoiceConfig => {
    return {
      enabled: true,
      interaction_mode: 'S2S', // Always use Speech-to-Speech for Phase 3
      tts_config: {
        provider: settings.tts.provider,
        voice_id: settings.tts.voice_id,
        speed: 1.0,
        pitch: 0,
        volume: 1.0
      },
      stt_config: {
        provider: settings.stt.provider,
        language: settings.stt.language,
        continuous: true,
        interim_results: settings.stt.interim_results
      },
      livekit_config: {
        room_prefix: 'rep-room',
        participant_name_prefix: 'visitor',
        max_session_duration: settings.usage_controls.max_session_duration_minutes * 60
      }
    };
  }, []);

  // Create adapted voice config for the interface
  const voiceConfig = useMemo(() => {
    return voiceSettings ? convertToVoiceConfig(voiceSettings) : null;
  }, [voiceSettings, convertToVoiceConfig]);

  // Set voice configuration based on rep room settings
  useEffect(() => {
    const voiceEnabled = repRoom.settings?.behavior?.voice_input_enabled || false;
    
    if (voiceEnabled) {
      // Create voice config using the new Phase 3 VoiceSettings format
      const voiceConfig: VoiceSettings = {
        ...DEFAULT_VOICE_SETTINGS,
        // Override with rep room specific settings if available
        tts: {
          ...DEFAULT_VOICE_SETTINGS.tts,
          // Use ElevenLabs for high-quality TTS
          provider: 'elevenlabs',
          voice_id: 'pNInz6obpgDQGcFmaJgB', // Adam voice
          voice_name: 'Adam',
          model: 'eleven_multilingual_v2'
        },
        stt: {
          ...DEFAULT_VOICE_SETTINGS.stt,
          // Use Deepgram for low-latency STT
          provider: 'deepgram',
          model: 'nova-2',
          language: 'en-US',
          interim_results: true
        },
        interaction: {
          ...DEFAULT_VOICE_SETTINGS.interaction,
          auto_start_listening: true,
          interrupt_agent_enabled: true
        }
      };
      
      console.log('Voice enabled for rep room with Phase 3 config:', repRoom.public_slug);
      setVoiceSettings(voiceConfig);
    } else {
      console.log('Voice disabled for rep room:', repRoom.public_slug);
      setVoiceSettings(null);
    }
  }, [repRoom.settings?.behavior?.voice_input_enabled, repRoom.public_slug]);

  // CopilotKit configuration - using the same working setup as clone chat
  const copilotConfig = useMemo(() => {
    return {
      runtimeUrl: 'https://seo-expert.mastra.cloud/copilotkit',
      agent: 'keywordResearchAgent'
    };
  }, []);

  // Generate enhanced agent instructions from rep room context
  const agentInstructions = useMemo(() => {
    let instructions = `You are ${agentName}, an AI assistant in a rep room environment.`;
    
    if (voiceConfig) {
      instructions += ` You are equipped with voice capabilities and can engage in full voice conversations with users.`;
      instructions += ' You can have natural voice conversations with users using speech-to-speech interaction.';
    }
    
    // Add behavior settings if available
    if (repRoom.settings?.behavior) {
      const behavior = repRoom.settings.behavior;
      
      if (behavior.greeting_message) {
        instructions += ` Your greeting message is: "${behavior.greeting_message}"`;
      }
      
      if (behavior.suggested_prompts && behavior.suggested_prompts.length > 0) {
        instructions += ` You can suggest these prompts to users: ${behavior.suggested_prompts.join(', ')}.`;
      }
      
      if (behavior.memory_persistence_enabled) {
        instructions += ' You have memory persistence enabled and should remember previous conversations.';
      }
    }
    
    // Add agent-specific instructions if available
    const agentClone = repRoom.user_agent_clone as { instructions?: string } | null;
    if (agentClone?.instructions) {
      instructions += ` Additional instructions: ${agentClone.instructions}`;
    }
    
    // Add custom instructions from rep room settings
    if (repRoom.intro_text) {
      instructions += ` ${repRoom.intro_text}`;
    }
    
    instructions += ' Be helpful, friendly, and provide valuable assistance to users visiting this rep room.';
    
    return instructions;
  }, [agentName, repRoom, voiceConfig]);

  const handleVoiceStateChange = useCallback((state: VoiceState) => {
    setVoiceState(state);
    voiceMessageSync.updateVoiceState(state);
  }, [voiceMessageSync]);

  const handleTranscriptUpdate = useCallback((transcript: string, isFinal: boolean) => {
    if (isFinal) {
      // Clear interim transcript and sync to chat
      setInterimTranscript('');
      voiceMessageSync.syncSTTToChat(transcript, true, {
        confidence: 1.0,
        language: 'en-US'
      });
    } else {
      // Show interim transcript
      setInterimTranscript(transcript);
    }
  }, [voiceMessageSync]);

  const handleVoiceError = useCallback((error: VoiceError) => {
    console.error('Voice error:', error);
    toast.error(`Voice Error: ${error.message}`);
  }, []);

  // Agent response narration
  const agentResponseInterceptor = useAgentResponseInterceptor(
    {
      enabled: !!voiceConfig,
      voiceState,
      autoNarration: autoNarrationEnabled,
      skipCodeBlocks: DEFAULT_NARRATION_CONFIG.skipCodeBlocks,
      skipTechnicalContent: DEFAULT_NARRATION_CONFIG.skipTechnicalContent,
      maxChunkLength: DEFAULT_NARRATION_CONFIG.maxChunkLength,
      agentName
    },
    {
      onNarrationStart: (messageId, text) => {
        console.log('Narration started:', messageId, text.substring(0, 50) + '...');
        toast.info('🔊 Narrating agent response');
      },
      onNarrationComplete: (messageId) => {
        console.log('Narration completed:', messageId);
      },
      onNarrationError: (messageId, error) => {
        console.error('Narration error:', messageId, error);
        toast.error('Narration failed');
      },
      onUserInterruption: () => {
        console.log('User interrupted narration');
      }
    }
  );

  // Create narration state and controls for voice controls
  const narrationState = useMemo(() => ({
    isNarrating: agentResponseInterceptor.isNarrating,
    isPaused: voiceState === 'narrating_paused',
    currentMessageId: agentResponseInterceptor.currentMessageId,
    queueLength: agentResponseInterceptor.queueLength,
    autoNarrationEnabled
  }), [
    agentResponseInterceptor.isNarrating,
    agentResponseInterceptor.currentMessageId,
    agentResponseInterceptor.queueLength,
    voiceState,
    autoNarrationEnabled
  ]);

  const narrationControls = useMemo(() => ({
    pauseNarration: agentResponseInterceptor.pauseNarration,
    resumeNarration: agentResponseInterceptor.resumeNarration,
    skipCurrent: agentResponseInterceptor.skipCurrent,
    clearQueue: agentResponseInterceptor.clearQueue,
    toggleAutoNarration: () => {
      setAutoNarrationEnabled(!autoNarrationEnabled);
      agentResponseInterceptor.toggleAutoNarration();
    }
  }), [
    agentResponseInterceptor.pauseNarration,
    agentResponseInterceptor.resumeNarration,
    agentResponseInterceptor.skipCurrent,
    agentResponseInterceptor.clearQueue,
    agentResponseInterceptor.toggleAutoNarration,
    autoNarrationEnabled
  ]);

  // Test connection and initialize
  useEffect(() => {
    const initializeChat = async () => {
      try {
        setConnectionStatus('🔗 Testing connection...');
        
        // Test endpoint connectivity
        const response = await fetch(copilotConfig.runtimeUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        });
        
        console.log('Rep room chat connection test:', {
          status: response.status,
          repRoomId: repRoom.id,
          agent: copilotConfig.agent
        });
        
        if (response.ok) {
          setConnectionStatus('✅ Connected');
          setError(null);
        } else {
          setConnectionStatus('⚠️ Connection Warning');
          console.warn('Connection test returned non-OK status:', response.status);
        }
      } catch (error) {
        console.error('Rep room chat connection test failed:', error);
        setConnectionStatus('❌ Connection Error');
        setError('Failed to connect to chat service. Chat may still work.');
      } finally {
        // Initialize after a brief delay
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      }
    };

    initializeChat();
  }, [copilotConfig.runtimeUrl, repRoom.id]);

  // Error boundary fallback
  if (error && error.includes('Failed to connect')) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
            <X className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Chat Unavailable</h3>
          <p className="text-sm text-gray-600 mb-4">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              size="sm"
            >
              Retry
            </Button>
            {onClose && (
              <Button onClick={onClose} variant="ghost" size="sm">
                Close
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold mb-2">Initializing Chat...</h3>
          <p className="text-sm text-gray-600 mb-4">
            Setting up your conversation with {agentName}
          </p>
          <div className="text-xs text-gray-500 space-y-1">
            <div>Agent: {agentName}</div>
            <div>Rep Room: {repRoom.title}</div>
            <div>Status: {connectionStatus}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Rep Room Header */}
      <div className="bg-white/90 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-10">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div 
                className="w-12 h-12 rounded-full flex items-center justify-center border-2"
                style={{ borderColor: themeColor }}
              >
                {avatarUrl ? (
                  <img
                    src={avatarUrl}
                    alt={agentName}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-lg font-semibold" style={{ color: themeColor }}>
                    {agentName.charAt(0)}
                  </span>
                )}
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Chat with {agentName}
                </h1>
                <p className="text-sm text-gray-500">
                  {voiceConfig ? 'Voice & Text Chat' : 'Text Chat'}
                </p>
                {isHeaderExpanded && repRoom.settings?.header?.welcome_message && (
                  <p className="text-sm text-gray-600 mt-1">
                    {repRoom.settings.header.welcome_message}
                  </p>
                )}
              </div>
            </div>
            
            {/* Voice Interface Controls */}
            {voiceConfig && (
              <RepRoomVoiceInterface
                repRoom={repRoom}
                voiceConfig={voiceConfig}
                voiceSettings={voiceSettings}
                onVoiceStateChange={handleVoiceStateChange}
                onTranscriptUpdate={handleTranscriptUpdate}
                onError={handleVoiceError}
                className="ml-4"
                narrationState={narrationState}
                narrationControls={narrationControls}
                showNarrationControls={true}
              />
            )}
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsHeaderExpanded(!isHeaderExpanded)}
              >
                {isHeaderExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
              {onClose && (
                <Button onClick={onClose} variant="ghost" size="sm">
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          
          {/* Expandable Header Content */}
          {isHeaderExpanded && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <p className="text-sm text-gray-600">
                {repRoom.settings?.header?.welcome_message}
              </p>
              {voiceConfig?.enabled && voiceState !== 'disconnected' && (
                <div className="mt-2">
                  <VoiceStatusIndicator state={voiceState} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* CopilotKit Chat Interface */}
      <div className="flex-1 flex flex-col" ref={copilotContainerRef}>
        <CopilotKit
          runtimeUrl={copilotConfig.runtimeUrl}
          agent={copilotConfig.agent}
          publicApiKey="disabled" // Disable branding by providing a dummy public API key
        >
          <div className="flex-1 flex flex-col">
            <CopilotChat
              instructions={agentInstructions}
              labels={{
                title: agentName,
                initial: voiceConfig?.enabled
                  ? "Hi! You can chat with me using text or voice."
                  : repRoom.settings?.behavior?.greeting_message ||
                    `Hi! I'm ${agentName}. How can I help you today?`
              }}
              className="flex-1"
            />
          </div>
        </CopilotKit>
        
        {/* Interim Transcript Overlay */}
        <InterimTranscriptDisplay
          interimTranscript={interimTranscript}
          finalTranscript=""
          isListening={voiceState === 'listening'}
          isProcessing={voiceState === 'processing'}
        />
      </div>

      {/* Voice Status Bar */}
      {voiceConfig?.enabled && voiceState !== 'disconnected' && (
        <div className="voice-status-bar bg-gray-50 border-t border-gray-200 p-3">
          <div className="flex items-center justify-between">
            <VoiceStatusIndicator state={voiceState} />
            
            {voiceConfig.interaction_mode === 'TTS_ONLY' && (
              <div className="text-sm text-gray-500">
                Type your message and I'll respond with voice
              </div>
            )}
            
            {voiceConfig.interaction_mode === 'S2S' && voiceState === 'connected' && (
              <div className="text-sm text-gray-500">
                Speak naturally or type your message
              </div>
            )}
          </div>
        </div>
      )}

      {/* Suggested Prompts (if enabled and no messages yet) */}
      {repRoom.settings?.behavior?.suggested_prompts_enabled && 
       repRoom.settings.behavior.suggested_prompts.length > 0 && (
        <div className="p-4 bg-gray-50 border-t">
          <p className="text-sm text-gray-600 text-center mb-2">
            Try asking:
          </p>
          <div className="flex flex-wrap gap-2 justify-center">
            {repRoom.settings.behavior.suggested_prompts.map((prompt, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => {
                  // This would trigger the prompt in CopilotChat
                  // Implementation depends on CopilotKit's API for programmatic messages
                  toast.info(`Suggested: ${prompt}`);
                }}
                className="text-sm"
                style={{ borderColor: themeColor, color: themeColor }}
              >
                {prompt}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="text-center py-2 bg-white/90">
        <p className="text-xs text-gray-500">
          Powered by DreamCrew Agentic Platform
        </p>
      </div>
    </div>
  );
}

export default RepRoomChatInterface;