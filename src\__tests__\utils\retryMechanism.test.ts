/**
 * Unit Tests for retryMechanism utility
 * Following TDD methodology - Red-Green-Refactor cycle
 */

import {
  withRetry,
  fetchWithRetry,
  CircuitBreaker,
  getCircuitBreaker,
  createRequestKey,
  clearRequestCache,
  calculateDelay,
  defaultRetryCondition,
  retryDatabaseOperation,
  retryLiveKitOperation,
  retryVoiceTokenRequest,
  healthCheck,
  type RetryConfig,
  type CircuitBreakerConfig,
  type CircuitBreakerState
} from '../../utils/retryMechanism';

// Mock global fetch
global.fetch = jest.fn();
global.performance = {
  now: jest.fn(() => Date.now())
} as Partial<Performance> as Performance;

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
});

describe('retryMechanism', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clearRequestCache();
  });

  describe('calculateDelay - Exponential Backoff', () => {
    it('should return initial delay for first attempt', () => {
      const delay = calculateDelay(1, 1000, 30000, 2, false);
      expect(delay).toBe(1000);
    });

    it('should apply exponential increase with 2x multiplier', () => {
      const delay1 = calculateDelay(1, 1000, 30000, 2, false);
      const delay2 = calculateDelay(2, 1000, 30000, 2, false);
      const delay3 = calculateDelay(3, 1000, 30000, 2, false);
      
      expect(delay1).toBe(1000);
      expect(delay2).toBe(2000);
      expect(delay3).toBe(4000);
    });

    it('should cap delay at maximum delay (30000ms)', () => {
      const delay = calculateDelay(10, 1000, 30000, 2, false);
      expect(delay).toBe(30000);
    });

    it('should apply jitter (0-100ms random) when enabled', () => {
      // Mock Math.random to return predictable values
      const originalRandom = Math.random;
      Math.random = jest.fn(() => 0.5);

      const delayWithoutJitter = calculateDelay(1, 1000, 30000, 2, false);
      const delayWithJitter = calculateDelay(1, 1000, 30000, 2, true);
      
      expect(delayWithoutJitter).toBe(1000);
      expect(delayWithJitter).toBe(1000); // 0.5 random gives no offset
      
      // Test with different random value
      Math.random = jest.fn(() => 0.8);
      const delayWithPositiveJitter = calculateDelay(1, 1000, 30000, 2, true);
      expect(delayWithPositiveJitter).toBeGreaterThan(1000);
      
      Math.random = originalRandom;
    });

    it('should never return negative delay with jitter', () => {
      const originalRandom = Math.random;
      Math.random = jest.fn(() => 0); // Minimum random value
      
      const delay = calculateDelay(1, 100, 30000, 2, true);
      expect(delay).toBeGreaterThanOrEqual(0);
      
      Math.random = originalRandom;
    });
  });

  describe('CircuitBreaker - Circuit Breaker Pattern', () => {
    let circuitBreaker: CircuitBreaker;

    beforeEach(() => {
      circuitBreaker = new CircuitBreaker({
        failureThreshold: 5,
        resetTimeout: 60000
      });
    });

    it('should start in closed state', () => {
      expect(circuitBreaker.getState()).toBe('closed');
    });

    it('should open circuit after 5 consecutive failures', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Service error'));

      // Execute 5 failing requests
      for (let i = 0; i < 5; i++) {
        try {
          await circuitBreaker.execute(failingFn);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(circuitBreaker.getState()).toBe('open');
    });

    it('should stay open for cooldown period (60000ms)', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Service error'));
      
      // Trigger circuit to open
      for (let i = 0; i < 5; i++) {
        try {
          await circuitBreaker.execute(failingFn);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(circuitBreaker.getState()).toBe('open');

      // Should still be open immediately
      await expect(circuitBreaker.execute(jest.fn())).rejects.toThrow('Circuit breaker is open');
      expect(circuitBreaker.getState()).toBe('open');
    });

    it('should transition to half-open after cooldown period', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Service error'));
      const successFn = jest.fn().mockResolvedValue('success');
      
      // Trigger circuit to open
      for (let i = 0; i < 5; i++) {
        try {
          await circuitBreaker.execute(failingFn);
        } catch (error) {
          // Expected to fail
        }
      }

      expect(circuitBreaker.getState()).toBe('open');

      // Manually advance time by modifying lastFailureTime
      (circuitBreaker as unknown as { lastFailureTime: number }).lastFailureTime = Date.now() - 61000;
      
      // Next request should transition to half-open
      const result = await circuitBreaker.execute(successFn);
      expect(result).toBe('success');
      expect(circuitBreaker.getState()).toBe('half-open');
    });

    it('should close circuit after successful request in half-open state', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Service error'));
      const successFn = jest.fn().mockResolvedValue('success');
      
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        try {
          await circuitBreaker.execute(failingFn);
        } catch (error) {
          // Expected to fail
        }
      }

      // Manually set time to trigger half-open
      (circuitBreaker as unknown as { lastFailureTime: number }).lastFailureTime = Date.now() - 61000;
      
      // Execute 3 successful requests to close circuit
      await circuitBreaker.execute(successFn);
      expect(circuitBreaker.getState()).toBe('half-open');
      
      await circuitBreaker.execute(successFn);
      expect(circuitBreaker.getState()).toBe('half-open');
      
      await circuitBreaker.execute(successFn);
      expect(circuitBreaker.getState()).toBe('closed');
    });

    it('should reset circuit breaker state', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Service error'));
      
      // Trigger some failures
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(failingFn);
        } catch (error) {
          // Expected to fail
        }
      }

      circuitBreaker.reset();
      expect(circuitBreaker.getState()).toBe('closed');
    });
  });

  describe('Request Deduplication', () => {
    beforeEach(() => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK'
      });
    });

    it('should deduplicate identical requests within 5 seconds', async () => {
      const url = 'https://api.example.com/data';
      const options = { method: 'GET' };

      // Start two identical requests simultaneously
      const promise1 = fetchWithRetry(url, options);
      const promise2 = fetchWithRetry(url, options);

      await Promise.all([promise1, promise2]);

      // Fetch should only be called once due to deduplication
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    it('should generate correct cache key from request parameters', () => {
      const key1 = createRequestKey('https://api.example.com/data', { method: 'GET' });
      const key2 = createRequestKey('https://api.example.com/data', { method: 'POST', body: JSON.stringify({ id: 1 }) });
      const key3 = createRequestKey('https://api.example.com/other', { method: 'GET' });

      expect(key1).toBe('GET:https://api.example.com/data:');
      expect(key2).toBe('POST:https://api.example.com/data:"{\\"id\\":1}"');
      expect(key3).toBe('GET:https://api.example.com/other:');
      expect(key1).not.toBe(key2);
      expect(key1).not.toBe(key3);
    });

    it('should not deduplicate different requests', async () => {
      const url1 = 'https://api.example.com/data1';
      const url2 = 'https://api.example.com/data2';

      await Promise.all([
        fetchWithRetry(url1),
        fetchWithRetry(url2)
      ]);

      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    it('should clear request cache', async () => {
      const url = 'https://api.example.com/data';
      
      // Start a request
      const promise1 = fetchWithRetry(url);
      
      // Clear cache
      clearRequestCache();
      
      // Start another identical request
      const promise2 = fetchWithRetry(url);

      await Promise.all([promise1, promise2]);

      // Should make two separate requests since cache was cleared
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Retry Logic', () => {
    it('should return immediately on successful requests', async () => {
      const successFn = jest.fn().mockResolvedValue('success');
      
      const result = await withRetry(successFn);
      
      expect(result).toBe('success');
      expect(successFn).toHaveBeenCalledTimes(1);
    });

    it('should retry up to maxRetries parameter', async () => {
      const failingFn = jest.fn()
        .mockRejectedValueOnce(new Error('500'))
        .mockRejectedValueOnce(new Error('500'))
        .mockResolvedValue('success');

      const result = await withRetry(failingFn, { 
        maxAttempts: 3,
        initialDelay: 1 // Very small delay for test
      });
      
      expect(result).toBe('success');
      expect(failingFn).toHaveBeenCalledTimes(3);
    });

    it('should use custom shouldRetry function', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Custom error'));
      const customRetryCondition = jest.fn().mockReturnValue(false);

      await expect(withRetry(failingFn, { 
        maxAttempts: 3,
        retryCondition: customRetryCondition 
      })).rejects.toThrow('Custom error');

      expect(failingFn).toHaveBeenCalledTimes(1);
      expect(customRetryCondition).toHaveBeenCalledWith(expect.any(Error), 1);
    });

    it('should execute onRetry callback', async () => {
      const failingFn = jest.fn()
        .mockRejectedValueOnce(new Error('500'))
        .mockResolvedValue('success');
      
      const onRetry = jest.fn();

      await withRetry(failingFn, { 
        maxAttempts: 2,
        onRetry,
        initialDelay: 1
      });

      expect(onRetry).toHaveBeenCalledWith(expect.any(Error), 1);
    });
    it('should execute onFailure callback when all retries exhausted', async () => {
      const failingFn = jest.fn().mockRejectedValue(new Error('Persistent error'));
      const onFailure = jest.fn();

      await expect(withRetry(failingFn, {
        maxAttempts: 1,
        onFailure,
        initialDelay: 1
      })).rejects.toThrow('Persistent error');

      expect(onFailure).toHaveBeenCalledWith(expect.any(Error), 1);
    });
  });

  describe('Error Handling', () => {
    describe('defaultRetryCondition', () => {
      it('should retry on network errors', () => {
        const networkError = new Error('fetch failed');
        expect(defaultRetryCondition(networkError, 1)).toBe(true);
      });

      it('should retry on 500+ status codes', () => {
        const serverError = new Error('500');
        expect(defaultRetryCondition(serverError, 1)).toBe(true);
        
        const badGateway = new Error('502');
        expect(defaultRetryCondition(badGateway, 1)).toBe(true);
        
        const serviceUnavailable = new Error('503');
        expect(defaultRetryCondition(serviceUnavailable, 1)).toBe(true);
        
        const gatewayTimeout = new Error('504');
        expect(defaultRetryCondition(gatewayTimeout, 1)).toBe(true);
      });

      it('should not retry on 400-499 status codes', () => {
        const badRequest = new Error('400');
        expect(defaultRetryCondition(badRequest, 1)).toBe(false);
        
        const unauthorized = new Error('401');
        expect(defaultRetryCondition(unauthorized, 1)).toBe(false);
        
        const forbidden = new Error('403');
        expect(defaultRetryCondition(forbidden, 1)).toBe(false);
      });

      it('should not retry on 404 after first attempt', () => {
        const notFound = new Error('404');
        expect(defaultRetryCondition(notFound, 1)).toBe(false);
        expect(defaultRetryCondition(notFound, 2)).toBe(false);
      });

      it('should retry on timeout errors', () => {
        const timeoutError = new Error('timeout');
        timeoutError.name = 'TimeoutError';
        expect(defaultRetryCondition(timeoutError, 1)).toBe(true);
        
        const abortError = new Error('Request aborted');
        abortError.name = 'AbortError';
        expect(defaultRetryCondition(abortError, 1)).toBe(true);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle concurrent requests properly', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK'
      });

      const url = 'https://api.example.com/data';
      const requests = Array(5).fill(null).map(() => fetchWithRetry(url));
      
      await Promise.all(requests);
      
      // Should only make one request due to deduplication
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    it('should handle request cancellation', async () => {
      const slowFn = jest.fn(() => new Promise(resolve => setTimeout(resolve, 10000)));
      
      await expect(withRetry(slowFn, { timeout: 1 })).rejects.toThrow('TimeoutError');
    });

    it('should handle invalid parameters gracefully', async () => {
      const fn = jest.fn().mockResolvedValue('success');
      
      // Test with zero maxAttempts - this causes undefined to be thrown
      // because the loop never executes and lastError is never assigned
      await expect(withRetry(fn, { maxAttempts: 0 })).rejects.toBeUndefined();
      expect(fn).toHaveBeenCalledTimes(0);
    });

    it('should handle memory cleanup after request completion', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK'
      });

      const url = 'https://api.example.com/data';
      
      await fetchWithRetry(url);
      
      // Start another request with same URL - should not be deduplicated
      await fetchWithRetry(url);
      
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Specialized Retry Functions', () => {
    describe('retryDatabaseOperation', () => {
      it('should retry on database connection errors', async () => {
        const dbFn = jest.fn()
          .mockRejectedValueOnce(new Error('connection failed'))
          .mockResolvedValue('db result');

        const result = await retryDatabaseOperation(dbFn);
        
        expect(result).toBe('db result');
        expect(dbFn).toHaveBeenCalledTimes(2);
      });

      it('should retry on PGRST errors', async () => {
        const dbFn = jest.fn()
          .mockRejectedValueOnce(new Error('PGRST error'))
          .mockResolvedValue('db result');

        const result = await retryDatabaseOperation(dbFn);
        
        expect(result).toBe('db result');
        expect(dbFn).toHaveBeenCalledTimes(2);
      });
    });

    describe('retryLiveKitOperation', () => {
      it('should retry on WebRTC connection errors', async () => {
        const liveKitFn = jest.fn()
          .mockRejectedValueOnce(new Error('webrtc connection failed'))
          .mockResolvedValue('livekit result');

        const result = await retryLiveKitOperation(liveKitFn);
        
        expect(result).toBe('livekit result');
        expect(liveKitFn).toHaveBeenCalledTimes(2);
      });

      it('should retry on signaling errors', async () => {
        const liveKitFn = jest.fn()
          .mockRejectedValueOnce(new Error('signaling error'))
          .mockResolvedValue('livekit result');

        const result = await retryLiveKitOperation(liveKitFn);
        
        expect(result).toBe('livekit result');
        expect(liveKitFn).toHaveBeenCalledTimes(2);
      });
    });

    describe('retryVoiceTokenRequest', () => {
      it('should not retry on authentication errors', async () => {
        const voiceFn = jest.fn().mockRejectedValue(new Error('401 Unauthorized'));

        await expect(retryVoiceTokenRequest(voiceFn)).rejects.toThrow('401 Unauthorized');
        
        expect(voiceFn).toHaveBeenCalledTimes(1);
      });

      it('should retry on server errors', async () => {
        const voiceFn = jest.fn()
          .mockRejectedValueOnce(new Error('500 Internal Server Error'))
          .mockResolvedValue('voice token');

        const result = await retryVoiceTokenRequest(voiceFn);
        
        expect(result).toBe('voice token');
        expect(voiceFn).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status for successful requests', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK'
      });

      const result = await healthCheck('https://api.example.com/health');
      
      expect(result.healthy).toBe(true);
      expect(result.latency).toBeGreaterThanOrEqual(0);
      expect(result.error).toBeUndefined();
    });

    it('should return unhealthy status for failed requests', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      const result = await healthCheck('https://api.example.com/health');
      
      expect(result.healthy).toBe(false);
      expect(result.latency).toBeGreaterThanOrEqual(0);
      expect(result.error).toBe('HTTP 500');
    });

    it('should handle timeout in health check', async () => {
      (global.fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 10000))
      );

      const result = await healthCheck('https://api.example.com/health', 1);
      
      expect(result.healthy).toBe(false);
      expect(result.error).toBe('Health check timeout');
    });
  });

  describe('getCircuitBreaker', () => {
    it('should return same circuit breaker instance for same service', () => {
      const cb1 = getCircuitBreaker('test-service');
      const cb2 = getCircuitBreaker('test-service');
      
      expect(cb1).toBe(cb2);
    });

    it('should return different circuit breaker instances for different services', () => {
      const cb1 = getCircuitBreaker('service-1');
      const cb2 = getCircuitBreaker('service-2');
      
      expect(cb1).not.toBe(cb2);
    });

    it('should use custom config for new circuit breakers', () => {
      const customConfig = { failureThreshold: 10 };
      const cb = getCircuitBreaker('custom-service', customConfig);
      
      expect(cb).toBeInstanceOf(CircuitBreaker);
    });
  });
});