import React, { useState } from 'react';
import { VoiceControls, AudioVisualizer, ConnectionStatus, VoiceActivityLevel } from './index';

/**
 * Demo component showing how to use the enhanced voice feedback system
 * This demonstrates the integration of VoiceControls and AudioVisualizer components
 */
export const VoiceFeedbackDemo: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('connecting');
  const [voiceActivity, setVoiceActivity] = useState<VoiceActivityLevel>('silent');

  const handleConnectionStatusChange = (status: ConnectionStatus) => {
    setConnectionStatus(status);
    console.log('Connection status changed:', status);
  };

  const handleVoiceActivityChange = (level: VoiceActivityLevel) => {
    setVoiceActivity(level);
    console.log('Voice activity changed:', level);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Enhanced Voice Feedback System Demo
        </h1>
        <p className="text-gray-600">
          Demonstrating the new VoiceControls and AudioVisualizer components
        </p>
      </div>

      {/* Status Display */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">Current Status</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Connection:</span>
            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
              connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
              connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
              connectionStatus === 'error' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {connectionStatus}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Voice Activity:</span>
            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
              voiceActivity === 'high' ? 'bg-red-100 text-red-800' :
              voiceActivity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              voiceActivity === 'low' ? 'bg-green-100 text-green-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {voiceActivity}
            </span>
          </div>
        </div>
      </div>

      {/* Voice Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Voice Controls</h2>
        <VoiceControls
          onConnectionStatusChange={handleConnectionStatusChange}
          onVoiceActivityChange={handleVoiceActivityChange}
          showAdvancedControls={true}
          className="max-w-md mx-auto"
        />
      </div>

      {/* Audio Visualizer Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Audio Visualizer</h2>
        <AudioVisualizer
          showSpeakerNames={true}
          height={120}
          maxBars={32}
          className="w-full"
        />
      </div>

      {/* Compact Audio Visualizer */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Compact Audio Visualizer</h2>
        <AudioVisualizer
          showSpeakerNames={false}
          height={60}
          maxBars={16}
          barWidth={3}
          className="w-full"
        />
      </div>

      {/* Integration Example */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-blue-900 mb-3">Integration Example</h2>
        <p className="text-blue-800 text-sm mb-4">
          Here's how these components work together in a real rep room interface:
        </p>
        
        <div className="bg-white rounded-lg p-4 space-y-4">
          {/* Mini voice controls */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Voice Controls</span>
            <VoiceControls
              onConnectionStatusChange={handleConnectionStatusChange}
              onVoiceActivityChange={handleVoiceActivityChange}
              showAdvancedControls={false}
              className="scale-75"
            />
          </div>
          
          {/* Mini visualizer */}
          <div>
            <span className="text-sm font-medium text-gray-700 block mb-2">Live Audio Activity</span>
            <AudioVisualizer
              showSpeakerNames={true}
              height={50}
              maxBars={12}
              barWidth={2}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">Usage Instructions</h2>
        <div className="space-y-2 text-sm text-gray-700">
          <p><strong>VoiceControls:</strong> Provides microphone toggle, speaker controls, push-to-talk, and connection status.</p>
          <p><strong>AudioVisualizer:</strong> Shows real-time frequency analysis and speaker identification.</p>
          <p><strong>Integration:</strong> Both components can be used together in the ParticipantsPanel or separately.</p>
          <p><strong>Callbacks:</strong> Use the callback props to monitor connection and voice activity changes.</p>
        </div>
      </div>
    </div>
  );
};