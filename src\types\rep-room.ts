// Rep Room UI Type Definitions

export type AgentStatus = "speaking" | "thinking" | "ready" | "working" | "idle" | "delegating";
export type HumanStatus = "talking" | "listening" | "hand-up";
export type AgentType = "main" | "specialist";
export type MessageType = "human" | "agent";
export type ContentType = "keyword-analysis" | "competitor-analysis" | "content-generator" | "default";

// Agent Orchestration Types
export type HandoffStatus = "pending" | "in-progress" | "completed" | "failed";
export type OrchestrationEventType = "handoff" | "delegation" | "activation" | "completion";

export interface AgentHandoff {
  id: string;
  fromAgentId: string;
  toAgentId: string;
  reason: string;
  status: HandoffStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  context?: Record<string, unknown>;
}

export interface OrchestrationEvent {
  id: string;
  type: OrchestrationEventType;
  agentId: string;
  timestamp: Date;
  data: Record<string, unknown>;
  handoffId?: string;
}

export interface SpecialistAgentCapability {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  isAvailable: boolean;
  estimatedDuration?: number;
}

export interface AgentOrchestrationState {
  activeHandoffs: AgentHandoff[];
  eventTimeline: OrchestrationEvent[];
  availableSpecialists: Agent[];
  specialistCapabilities: SpecialistAgentCapability[];
  coordinationStatus: "idle" | "coordinating" | "delegating" | "handoff-in-progress";
}

export interface BaseParticipant {
  id: string;
  name: string;
  avatar: string;
}

export interface Agent extends BaseParticipant {
  type: AgentType;
  status: AgentStatus;
  specialization?: string; // For specialist agents
}

export interface Human extends BaseParticipant {
  status: HumanStatus;
  role?: string;
}

export interface ChatMessage {
  id: string;
  sender: string;
  content: string;
  timestamp: Date;
  type: MessageType;
  isTyping?: boolean;
  avatar?: string;
  error?: string;
  failed?: boolean;
  retryCount?: number;
}

export interface PresentationContent {
  type: ContentType;
  title: string;
  data?: string | Record<string, unknown>;
  loading?: boolean;
  generatedBy?: string; // Agent ID that generated this content
}

export interface RepRoomState {
  activeAgent: string | null;
  presentationContent: PresentationContent;
  messages: ChatMessage[];
  isVoiceActive: boolean;
  participants: {
    mainAgent: Agent;
    humans: Human[];
    specialists: Agent[];
  };
  sessionInfo: {
    title: string;
    sessionId: string;
    slug: string;
  };
}

export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  disabled?: boolean;
}

// Status color mapping with enhanced gradients
export const STATUS_COLORS = {
  // Agent statuses
  speaking: "bg-gradient-to-r from-green-400 to-green-600",
  ready: "bg-gradient-to-r from-green-500 to-green-600",
  thinking: "bg-gradient-to-r from-yellow-400 to-yellow-500",
  working: "bg-gradient-to-r from-blue-400 to-blue-500",
  delegating: "bg-gradient-to-r from-purple-400 to-purple-600",
  idle: "bg-gray-300",
  
  // Human statuses
  talking: "bg-gradient-to-r from-green-400 to-green-600",
  listening: "bg-gray-300",
  "hand-up": "bg-gradient-to-r from-orange-400 to-orange-500",
  
  // Additional states
  online: "bg-gradient-to-r from-green-400 to-green-600",
  offline: "bg-gray-400",
} as const;

// Enhanced animation classes for status indicators
export const STATUS_ANIMATIONS = {
  speaking: "animate-pulse",
  talking: "animate-pulse",
  working: "animate-spin",
  thinking: "animate-bounce",
  delegating: "animate-pulse",
  ready: "",
  idle: "",
  listening: "",
  "hand-up": "animate-pulse",
  online: "",
  offline: "",
} as const;