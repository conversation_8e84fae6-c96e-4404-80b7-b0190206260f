# Customer-Facing Sales Agent Configuration Template

A comprehensive template for configuring customer-facing sales agents based on the Monkey Wrench Plumbing example. This template shows the actual configurable fields that businesses can adjust for their sales agent, with content (success stories, reviews, products/services) uploaded separately to a knowledge base.

## Table of Contents

1. [Overview](#overview)
2. [Template Structure](#template-structure)
3. [Bot Basic Information](#bot-basic-information)
4. [<PERSON><PERSON> Attributes](#bot-attributes)
5. [Business Information](#business-information)
6. [Guardrails](#guardrails)
7. [Instructions and Conversation Stages](#instructions-and-conversation-stages)
8. [Knowledge Base Content](#knowledge-base-content)
9. [Industry Examples](#industry-examples)
10. [Implementation Guide](#implementation-guide)

---

## Overview

This template provides the configurable fields for creating customer-facing sales agents. The agent leverages uploaded content from a knowledge base to build trust and drive sales through:

- **Customer success stories** with before/after photos
- **Testimonial videos** (transcribed content)
- **Google and Yelp reviews**
- **Product/service specifications**
- **Customer profile information** via CRM integration

### Key Concept: Configuration vs. Content

- **Configuration Fields** (this template): Define how the agent behaves, its personality, and operational parameters
- **Knowledge Base Content** (uploaded separately): Success stories, reviews, product specs, and testimonials that the agent references during conversations

---

## Template Structure

### Complete Agent Configuration Template

```json
{
  "id": "customerSalesAgent",
  "name": "Customer Sales Consultant",
  "description": "AI sales consultant that builds trust through customer success stories, testimonials, and reviews while providing product expertise",
  "version": "1.0.0",
  "status": "published",
  "category": "sales_support",
  "is_public": false,
  "agent_type": "task_specific",
  "mastra_agent_id": "customer-sales-consultant-v1",
  "mastra_api_base_url": "https://mastra-runtime.example.com",
  "configuration": {
    "configuration_schema": {
      "type": "object",
      "properties": {
        // Bot Basic Information
        "bot_name": {
          "type": "string",
          "title": "Bot Name",
          "description": "The name customers will see for your sales agent",
          "default": "Sales Assistant",
          "maxLength": 50
        },
        "bot_type": {
          "type": "string",
          "title": "Bot Type",
          "description": "Type of sales agent",
          "enum": ["sales_consultant", "product_advisor", "service_specialist"],
          "default": "sales_consultant"
        },
        "voice_settings": {
          "type": "object",
          "title": "Voice Configuration",
          "properties": {
            "enabled": {
              "type": "boolean",
              "title": "Enable Voice",
              "default": true
            },
            "voice_type": {
              "type": "string",
              "title": "Voice Type",
              "enum": ["professional_male", "professional_female", "friendly_male", "friendly_female"],
              "default": "professional_female"
            },
            "speech_rate": {
              "type": "number",
              "title": "Speech Rate",
              "minimum": 0.5,
              "maximum": 2.0,
              "default": 1.0
            }
          }
        },
        "domain": {
          "type": "string",
          "title": "Business Domain",
          "description": "Your website domain for brand consistency",
          "format": "uri",
          "default": ""
        },
        
        // Bot Attributes
        "role": {
          "type": "string",
          "title": "Agent Role",
          "description": "Primary role of the sales agent",
          "enum": ["sales_consultant", "product_specialist", "customer_advisor", "technical_consultant"],
          "default": "sales_consultant"
        },
        "objective": {
          "type": "string",
          "title": "Primary Objective",
          "description": "Main goal of the sales agent",
          "enum": ["lead_generation", "conversion_optimization", "customer_education", "relationship_building"],
          "default": "conversion_optimization"
        },
        "deliverables": {
          "type": "array",
          "title": "Key Deliverables",
          "description": "What the agent should accomplish in conversations",
          "items": {
            "type": "string",
            "enum": ["product_recommendations", "quote_generation", "appointment_scheduling", "objection_handling", "trust_building"]
          },
          "default": ["product_recommendations", "trust_building"]
        },
        "profile": {
          "type": "object",
          "title": "Agent Profile",
          "properties": {
            "expertise_level": {
              "type": "string",
              "enum": ["beginner", "intermediate", "expert", "specialist"],
              "default": "expert"
            },
            "experience_years": {
              "type": "string",
              "enum": ["1-2 years", "3-5 years", "5-10 years", "10+ years"],
              "default": "5-10 years"
            },
            "specialization": {
              "type": "string",
              "title": "Area of Specialization",
              "default": ""
            }
          }
        },
        "language_rules": {
          "type": "object",
          "title": "Communication Rules",
          "properties": {
            "tone": {
              "type": "string",
              "enum": ["professional", "friendly", "enthusiastic", "consultative", "helpful"],
              "default": "friendly"
            },
            "formality_level": {
              "type": "string",
              "enum": ["casual", "friendly_professional", "business_professional", "formal"],
              "default": "friendly_professional"
            },
            "response_style": {
              "type": "string",
              "enum": ["concise", "detailed", "conversational", "structured"],
              "default": "conversational"
            }
          }
        },
        "personality": {
          "type": "object",
          "title": "Personality Traits",
          "properties": {
            "empathy_level": {
              "type": "string",
              "enum": ["low", "medium", "high", "very_high"],
              "default": "high"
            },
            "patience_level": {
              "type": "string",
              "enum": ["standard", "high", "unlimited"],
              "default": "high"
            },
            "enthusiasm": {
              "type": "string",
              "enum": ["reserved", "moderate", "enthusiastic", "very_enthusiastic"],
              "default": "moderate"
            }
          }
        },
        
        // Business Information
        "business_name": {
          "type": "string",
          "title": "Business Name",
          "description": "Your company name as customers will see it",
          "minLength": 1,
          "maxLength": 100,
          "default": ""
        },
        "business_description": {
          "type": "string",
          "title": "Business Description",
          "description": "Brief description of your business",
          "maxLength": 500,
          "default": ""
        },
        "industry": {
          "type": "string",
          "title": "Industry",
          "description": "Your company's industry sector",
          "enum": ["home_services", "technology", "healthcare", "finance", "retail", "real_estate", "automotive", "professional_services", "other"],
          "default": "home_services"
        },
        "primary_services": {
          "type": "array",
          "title": "Primary Services/Products",
          "description": "Main services or products you offer",
          "items": {
            "type": "string"
          },
          "maxItems": 10,
          "default": []
        },
        "service_area": {
          "type": "string",
          "title": "Service Area",
          "description": "Geographic area you serve (e.g., 'Greater Chicago Area', 'Nationwide')",
          "default": ""
        },
        "contact_info": {
          "type": "object",
          "title": "Contact Information",
          "properties": {
            "phone": {
              "type": "string",
              "title": "Contact Phone",
              "pattern": "^[\\+]?[1-9]?[0-9]{7,15}$",
              "default": ""
            },
            "email": {
              "type": "string",
              "title": "Contact Email",
              "format": "email",
              "default": ""
            },
            "business_hours": {
              "type": "string",
              "title": "Business Hours",
              "default": "Mon-Fri 9AM-5PM"
            }
          }
        },
        
        // Guardrails
        "restrictions": {
          "type": "object",
          "title": "Agent Restrictions",
          "properties": {
            "no_pricing_without_consultation": {
              "type": "boolean",
              "title": "Require Consultation for Pricing",
              "default": true
            },
            "no_guarantees_without_assessment": {
              "type": "boolean",
              "title": "No Guarantees Without Assessment",
              "default": true
            },
            "must_verify_customer_info": {
              "type": "boolean",
              "title": "Verify Customer Information",
              "default": true
            },
            "escalate_complex_technical": {
              "type": "boolean",
              "title": "Escalate Complex Technical Questions",
              "default": true
            }
          }
        },
        "limitations": {
          "type": "object",
          "title": "Agent Limitations",
          "properties": {
            "cannot_process_payments": {
              "type": "boolean",
              "title": "Cannot Process Payments",
              "default": true
            },
            "cannot_schedule_without_availability": {
              "type": "boolean",
              "title": "Cannot Schedule Without Checking Availability",
              "default": true
            },
            "must_disclose_ai_nature": {
              "type": "boolean",
              "title": "Must Disclose AI Nature",
              "default": true
            }
          }
        },
        
        // Instructions and Conversation Stages
        "conversation_flow": {
          "type": "object",
          "title": "Conversation Flow Configuration",
          "properties": {
            "greeting_style": {
              "type": "string",
              "enum": ["warm_welcome", "professional_introduction", "casual_greeting", "problem_focused"],
              "default": "warm_welcome"
            },
            "discovery_approach": {
              "type": "string",
              "enum": ["needs_assessment", "problem_identification", "goal_exploration", "situation_analysis"],
              "default": "needs_assessment"
            },
            "presentation_method": {
              "type": "string",
              "enum": ["story_driven", "feature_focused", "benefit_oriented", "solution_based"],
              "default": "story_driven"
            },
            "objection_handling": {
              "type": "string",
              "enum": ["empathetic_listening", "educational_response", "evidence_based", "consultative_approach"],
              "default": "empathetic_listening"
            },
            "closing_technique": {
              "type": "string",
              "enum": ["soft_close", "assumptive_close", "alternative_choice", "consultation_booking"],
              "default": "consultation_booking"
            }
          }
        },
        
        // Integration Settings
        "crm_integration": {
          "type": "object",
          "title": "CRM Integration",
          "properties": {
            "enabled": {
              "type": "boolean",
              "title": "Enable CRM Integration",
              "default": true
            },
            "customer_lookup_endpoint": {
              "type": "string",
              "title": "Customer Lookup API Endpoint",
              "default": ""
            },
            "api_key_name": {
              "type": "string",
              "title": "API Key Header Name",
              "default": "X-API-Key"
            }
          }
        }
      },
      "required": ["business_name", "industry", "contact_info"]
    },
    
    "default_config_values": {
      "bot_name": "Sales Assistant",
      "bot_type": "sales_consultant",
      "voice_settings": {
        "enabled": true,
        "voice_type": "professional_female",
        "speech_rate": 1.0
      },
      "domain": "",
      "role": "sales_consultant",
      "objective": "conversion_optimization",
      "deliverables": ["product_recommendations", "trust_building"],
      "profile": {
        "expertise_level": "expert",
        "experience_years": "5-10 years",
        "specialization": ""
      },
      "language_rules": {
        "tone": "friendly",
        "formality_level": "friendly_professional",
        "response_style": "conversational"
      },
      "personality": {
        "empathy_level": "high",
        "patience_level": "high",
        "enthusiasm": "moderate"
      },
      "business_name": "",
      "business_description": "",
      "industry": "home_services",
      "primary_services": [],
      "service_area": "",
      "contact_info": {
        "phone": "",
        "email": "",
        "business_hours": "Mon-Fri 9AM-5PM"
      },
      "restrictions": {
        "no_pricing_without_consultation": true,
        "no_guarantees_without_assessment": true,
        "must_verify_customer_info": true,
        "escalate_complex_technical": true
      },
      "limitations": {
        "cannot_process_payments": true,
        "cannot_schedule_without_availability": true,
        "must_disclose_ai_nature": true
      },
      "conversation_flow": {
        "greeting_style": "warm_welcome",
        "discovery_approach": "needs_assessment",
        "presentation_method": "story_driven",
        "objection_handling": "empathetic_listening",
        "closing_technique": "consultation_booking"
      },
      "crm_integration": {
        "enabled": true,
        "customer_lookup_endpoint": "",
        "api_key_name": "X-API-Key"
      }
    },
    
    "available_channels": ["chat", "voice", "rep_room"],
    "capabilities": [
      "customer_consultation",
      "product_recommendation",
      "quote_generation",
      "appointment_scheduling",
      "objection_handling",
      "trust_building",
      "crm_integration"
    ],
    "agent_operational_mode": "interactive_user_facing"
  }
}
```

---

## Bot Basic Information

### Name and Identity
- **Bot Name**: The customer-facing name for your sales agent
- **Bot Type**: Classification of the agent's primary function
- **Voice Settings**: Voice configuration for phone and voice interactions
- **Domain**: Your business website for brand consistency

### Example Configuration
```json
{
  "bot_name": "Mike",
  "bot_type": "sales_consultant",
  "voice_settings": {
    "enabled": true,
    "voice_type": "professional_male",
    "speech_rate": 1.0
  },
  "domain": "https://monkeywrenchplumbing.com"
}
```

---

## Bot Attributes

### Role and Expertise
- **Role**: Primary function (sales consultant, product specialist, etc.)
- **Objective**: Main goal (lead generation, conversion, education)
- **Deliverables**: Key outcomes the agent should achieve
- **Profile**: Experience level and specialization area

### Communication Style
- **Language Rules**: Tone, formality, and response style
- **Personality**: Empathy, patience, and enthusiasm levels

### Example Configuration
```json
{
  "role": "sales_consultant",
  "objective": "conversion_optimization",
  "deliverables": ["product_recommendations", "trust_building", "appointment_scheduling"],
  "profile": {
    "expertise_level": "expert",
    "experience_years": "10+ years",
    "specialization": "Emergency plumbing and home repairs"
  },
  "language_rules": {
    "tone": "helpful",
    "formality_level": "friendly_professional",
    "response_style": "conversational"
  },
  "personality": {
    "empathy_level": "very_high",
    "patience_level": "unlimited",
    "enthusiasm": "moderate"
  }
}
```

---

## Business Information

### Company Details
- **Business Name**: Your company name as customers see it
- **Business Description**: Brief overview of your business
- **Industry**: Sector classification for optimization
- **Primary Services**: Main offerings (configured, not content)

### Contact and Service Area
- **Service Area**: Geographic coverage
- **Contact Info**: Phone, email, and business hours

### Example Configuration
```json
{
  "business_name": "Monkey Wrench Plumbing",
  "business_description": "Professional plumbing services with 15+ years of experience serving the Greater Chicago area",
  "industry": "home_services",
  "primary_services": [
    "Emergency Plumbing",
    "Drain Cleaning",
    "Water Heater Installation",
    "Bathroom Remodeling",
    "Pipe Repair"
  ],
  "service_area": "Greater Chicago Area",
  "contact_info": {
    "phone": "******-PLUMBER",
    "email": "<EMAIL>",
    "business_hours": "24/7 Emergency Service, Regular Hours: Mon-Fri 7AM-6PM"
  }
}
```

---

## Guardrails

### Restrictions (What the Agent Must Do)
- **No Pricing Without Consultation**: Require assessment before pricing
- **No Guarantees Without Assessment**: Must evaluate before promising outcomes
- **Must Verify Customer Info**: Confirm customer details
- **Escalate Complex Technical**: Hand off complex questions to experts

### Limitations (What the Agent Cannot Do)
- **Cannot Process Payments**: Must direct to proper payment channels
- **Cannot Schedule Without Availability**: Must check calendar systems
- **Must Disclose AI Nature**: Transparency about being an AI assistant

### Example Configuration
```json
{
  "restrictions": {
    "no_pricing_without_consultation": true,
    "no_guarantees_without_assessment": true,
    "must_verify_customer_info": true,
    "escalate_complex_technical": true
  },
  "limitations": {
    "cannot_process_payments": true,
    "cannot_schedule_without_availability": true,
    "must_disclose_ai_nature": true
  }
}
```

---

## Instructions and Conversation Stages

### Conversation Flow Configuration
- **Greeting Style**: How the agent opens conversations
- **Discovery Approach**: Method for understanding customer needs
- **Presentation Method**: How solutions are presented
- **Objection Handling**: Approach to addressing concerns
- **Closing Technique**: Method for moving toward next steps

### Example Configuration
```json
{
  "conversation_flow": {
    "greeting_style": "warm_welcome",
    "discovery_approach": "problem_identification",
    "presentation_method": "story_driven",
    "objection_handling": "empathetic_listening",
    "closing_technique": "consultation_booking"
  }
}
```

---

## Knowledge Base Content

### Content Types (Uploaded Separately)

The agent accesses these content types from the knowledge base during conversations:

#### 1. Customer Success Stories
```json
{
  "title": "Emergency Pipe Burst Resolution",
  "customer_name": "Sarah Johnson",
  "project_type": "Emergency Plumbing",
  "description": "Responded to emergency pipe burst at 2AM, prevented water damage",
  "before_photo_url": "https://example.com/photos/burst-before.jpg",
  "after_photo_url": "https://example.com/photos/burst-after.jpg",
  "project_value": "$850",
  "completion_time": "2 hours",
  "customer_quote": "They saved our home! Professional, fast, and fair pricing."
}
```

#### 2. Testimonial Videos
```json
{
  "customer_name": "Mike Thompson",
  "video_url": "https://example.com/videos/testimonial-1.mp4",
  "transcript": "Monkey Wrench Plumbing came out the same day I called. The technician was professional, explained everything clearly, and fixed my water heater perfectly. I'll definitely call them again!",
  "project_type": "Water Heater Repair",
  "rating": 5,
  "date_recorded": "2024-11-15"
}
```

#### 3. Reviews
```json
{
  "reviewer_name": "Amanda K.",
  "rating": 5,
  "review_text": "Outstanding service! Fixed our kitchen sink leak quickly and professionally. Fair pricing and excellent communication throughout.",
  "date": "2024-12-10",
  "project_type": "Sink Repair",
  "verified": true
}
```

#### 4. Product/Service Specifications
```json
{
  "name": "Tankless Water Heater Installation",
  "category": "Water Heater Services",
  "description": "Professional installation of high-efficiency tankless water heaters",
  "features": [
    "Energy efficient operation",
    "Endless hot water supply",
    "Space-saving design",
    "10-year warranty"
  ],
  "benefits": [
    "Lower energy bills",
    "Never run out of hot water",
    "More storage space",
    "Long-term reliability"
  ],
  "price_range": "$2,500 - $4,500",
  "warranty": "10-year manufacturer + 2-year installation warranty",
  "installation_time": "4-6 hours",
  "maintenance_requirements": "Annual inspection recommended"
}
```

### How the Agent Uses Knowledge Base Content

The agent pulls from uploaded content to:
- **Share relevant success stories** based on customer's situation
- **Reference customer reviews** that match the project type
- **Provide detailed specifications** when customers ask about products/services
- **Build trust** through testimonials and social proof
- **Answer technical questions** using product documentation

---

## Industry Examples

### Example 1: Home Services (Monkey Wrench Plumbing)

```json
{
  "business_name": "Monkey Wrench Plumbing",
  "industry": "home_services",
  "bot_name": "Mike",
  "role": "sales_consultant",
  "objective": "conversion_optimization",
  "primary_services": [
    "Emergency Plumbing",
    "Drain Cleaning", 
    "Water Heater Installation",
    "Bathroom Remodeling",
    "Pipe Repair"
  ],
  "language_rules": {
    "tone": "helpful",
    "formality_level": "friendly_professional",
    "response_style": "conversational"
  },
  "conversation_flow": {
    "greeting_style": "warm_welcome",
    "discovery_approach": "problem_identification",
    "presentation_method": "story_driven",
    "objection_handling": "empathetic_listening",
    "closing_technique": "consultation_booking"
  }
}
```

### Example 2: Professional Services (Legal Firm)

```json
{
  "business_name": "Smith & Associates Law",
  "industry": "professional_services",
  "bot_name": "Legal Assistant",
  "role": "customer_advisor",
  "objective": "relationship_building",
  "primary_services": [
    "Personal Injury Law",
    "Family Law",
    "Estate Planning",
    "Business Law"
  ],
  "language_rules": {
    "tone": "professional",
    "formality_level": "business_professional",
    "response_style": "structured"
  },
  "conversation_flow": {
    "greeting_style": "professional_introduction",
    "discovery_approach": "situation_analysis",
    "presentation_method": "solution_based",
    "objection_handling": "educational_response",
    "closing_technique": "consultation_booking"
  }
}
```

### Example 3: Retail (Electronics Store)

```json
{
  "business_name": "TechHub Electronics",
  "industry": "retail",
  "bot_name": "Tech Advisor",
  "role": "product_specialist",
  "objective": "customer_education",
  "primary_services": [
    "Smartphones",
    "Laptops",
    "Gaming Equipment",
    "Smart Home Devices",
    "Tech Support"
  ],
  "language_rules": {
    "tone": "enthusiastic",
    "formality_level": "casual",
    "response_style": "detailed"
  },
  "conversation_flow": {
    "greeting_style": "casual_greeting",
    "discovery_approach": "needs_assessment",
    "presentation_method": "feature_focused",
    "objection_handling": "evidence_based",
    "closing_technique": "alternative_choice"
  }
}
```

---

## Implementation Guide

### Step 1: Configure Basic Information
1. Set your business name, industry, and contact information
2. Choose your bot's name and personality
3. Configure voice settings if using phone/voice channels

### Step 2: Define Agent Attributes
1. Select the agent's role and primary objective
2. Set expertise level and specialization
3. Configure communication style and personality traits

### Step 3: Set Up Guardrails
1. Define what the agent must do (restrictions)
2. Specify what the agent cannot do (limitations)
3. Configure escalation rules for complex situations

### Step 4: Configure Conversation Flow
1. Choose greeting and discovery approaches
2. Set presentation and objection handling methods
3. Define closing techniques

### Step 5: Upload Knowledge Base Content
1. **Success Stories**: Upload customer success stories with photos
2. **Testimonials**: Add video testimonials with transcripts
3. **Reviews**: Import Google/Yelp reviews
4. **Product Specs**: Upload detailed product/service information

### Step 6: Test and Optimize
1. Test conversations across different scenarios
2. Monitor customer interactions and feedback
3. Adjust configuration based on performance
4. Update knowledge base content regularly

### Key Success Factors

1. **Authentic Content**: Use real customer stories and reviews
2. **Regular Updates**: Keep knowledge base content current
3. **Proper Configuration**: Align agent personality with brand
4. **Clear Guardrails**: Set appropriate restrictions and limitations
5. **Continuous Optimization**: Monitor and improve based on results

---

## Conclusion

This configuration template provides the framework for creating effective customer-facing sales agents. The key is to:

1. **Configure the agent's behavior** using the fields in this template
2. **Upload rich content** to the knowledge base for the agent to reference
3. **Test thoroughly** across different customer scenarios
4. **Optimize continuously** based on customer feedback and performance

The agent will automatically pull from your uploaded content (success stories, reviews, product specs) during conversations to build trust and provide relevant information to customers, while following the behavioral guidelines you've configured.

Remember: This template defines HOW your agent behaves - the success stories, reviews, and product information that make your agent effective are uploaded separately to the knowledge base that the agent accesses during conversations.