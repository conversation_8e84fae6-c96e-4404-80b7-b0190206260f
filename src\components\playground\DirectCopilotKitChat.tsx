import React from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotChat } from '@copilotkit/react-ui';
import '@copilotkit/react-ui/styles.css';
import { Bot } from 'lucide-react';

interface DirectCopilotKitChatProps {
  agentId?: string;
  agentName?: string;
  className?: string;
}

/**
 * Direct CopilotKit integration component that uses the fixed Supabase Edge Function
 * This uses the proven runtime handler that properly bridges CopilotKit to Mastra
 */
export function DirectCopilotKitChat({
  agentId = 'crewChiefAgent',
  agentName = 'Crew Chief Agent',
  className = ''
}: DirectCopilotKitChatProps) {
  // Use the fixed Supabase Edge Function that properly handles GraphQL format
  const runtimeUrl = 'https://kjkehonxatogcwrybslr.supabase.co/functions/v1/copilotkit-runtime-handler';

  console.log('DirectCopilotKitChat configuration:', {
    agentId,
    agentName,
    runtimeUrl,
    bridgeType: 'fixed-supabase-edge-function'
  });

  const welcomeMessage = `Hello! I'm ${agentName}. I'm your AI assistant ready to help you with various tasks.

To get started:
• Ask me questions about your projects
• Request help with planning, analysis, or documentation
• I can generate code, documents, and provide insights
• All substantial outputs will appear as interactive content

What would you like to work on today?`;

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <CopilotKit
        runtimeUrl={runtimeUrl}
        agent={agentId}
        publicApiKey="disabled" // Disable branding
        headers={{
          'Content-Type': 'application/json',
          'X-Agent-ID': agentId,
        }}
      >
        <div className="flex-1 flex flex-col">
          <CopilotChat
            labels={{
              title: agentName,
              initial: welcomeMessage,
            }}
            className="h-full"
          />
        </div>
      </CopilotKit>
    </div>
  );
}

/**
 * Standalone Direct CopilotKit component for testing
 * This can be used independently to test the direct integration
 */
export function StandaloneDirectCopilotKitChat() {
  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between py-3">
            <div className="flex items-center gap-3">
              <Bot className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold">Direct CopilotKit Integration</h1>
              <span className="text-sm text-muted-foreground">Using Fixed Supabase Edge Function</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <DirectCopilotKitChat
          agentId="crewChiefAgent"
          agentName="Crew Chief Agent"
          className="h-full"
        />
      </div>

      {/* Footer */}
      <footer className="border-t bg-card/80 backdrop-blur supports-[backdrop-filter]:bg-card/80">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Direct CopilotKit Integration</span>
              <span>•</span>
              <span>Runtime: Fixed Supabase Edge Function → Mastra</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500" />
              <span>Direct Connection</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}