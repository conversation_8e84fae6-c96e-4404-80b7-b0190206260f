import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// LiveKit webhook event types
interface LiveKitWebhookEvent {
  event: 'room_started' | 'room_finished' | 'participant_joined' | 'participant_left'
  room: {
    sid: string
    name: string
    metadata?: string
    creation_time: number
    turn_password?: string
    enabled_codecs?: string[]
    max_participants?: number
    empty_timeout?: number
  }
  participant?: {
    sid: string
    identity: string
    name?: string
    metadata?: string
    joined_at: number
    hidden?: boolean
  }
  track?: {
    sid: string
    name?: string
    type: 'audio' | 'video' | 'data'
    source: 'camera' | 'microphone' | 'screen_share' | 'screen_share_audio' | 'unknown'
  }
  created_at: number
  id: string
}

// Session state for database storage
interface SessionState {
  session_id: string
  room_name: string
  rep_room_id: string
  status: 'active' | 'ended'
  participant_count: number
  agent_joined: boolean
  created_at: string
  updated_at: string
  metadata?: Record<string, any>
}

// Participant state for database storage
interface ParticipantState {
  session_id: string
  identity: string
  participant_sid: string
  name?: string
  joined_at: string
  left_at?: string
  is_agent: boolean
  metadata?: Record<string, any>
}

// Helper function to verify webhook signature
async function verifyWebhookSignature(
  body: string,
  signature: string,
  secret: string
): Promise<boolean> {
  try {
    // LiveKit uses HMAC-SHA256 for webhook signatures
    const key = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['verify']
    )

    // Extract signature from header (format: "sha256=<signature>")
    const expectedSignature = signature.replace('sha256=', '')
    const expectedBytes = new Uint8Array(
      expectedSignature.match(/.{1,2}/g)?.map(byte => parseInt(byte, 16)) || []
    )

    const isValid = await crypto.subtle.verify(
      'HMAC',
      key,
      expectedBytes,
      new TextEncoder().encode(body)
    )

    return isValid
  } catch (error) {
    console.error('Webhook signature verification error:', error)
    return false
  }
}

// Helper function to parse room metadata
function parseRoomMetadata(metadataString?: string): Record<string, any> | null {
  if (!metadataString) return null
  
  try {
    return JSON.parse(metadataString)
  } catch (error) {
    console.error('Failed to parse room metadata:', error)
    return null
  }
}

// Helper function to extract session info from room name
function extractSessionInfo(roomName: string): { slug: string; sessionId: string } | null {
  // Match pattern: rrs-{slug}-{sessionId}
  const match = roomName.match(/^rrs-(.+)-(.+)$/)
  if (!match) {
    console.warn('Room name does not match expected pattern:', roomName)
    return null
  }
  
  return {
    slug: match[1],
    sessionId: match[2]
  }
}

// Helper function to trigger agent joining
async function triggerAgentJoin(
  roomName: string,
  roomMetadata: Record<string, any>,
  supabaseClient: any
): Promise<void> {
  try {
    console.log(`🤖 Triggering agent join for room: ${roomName}`)
    
    // Extract agent configuration from room metadata
    const agentConfig = roomMetadata.agent_config
    const repRoomId = roomMetadata.rep_room_id
    const sessionId = roomMetadata.session_id
    const organizationId = roomMetadata.organization_id
    
    if (!agentConfig || !repRoomId) {
      console.warn('Missing agent configuration or rep room ID in room metadata')
      return
    }

    // Call agent-lifecycle function to dispatch agent
    const agentLifecycleUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/agent-lifecycle/dispatch/${roomName}`
    
    const agentDispatchPayload = {
      roomId: repRoomId,
      userId: roomMetadata.user_id,
      sessionId: sessionId,
      agentCloneId: roomMetadata.agent_clone_id,
      organizationId: organizationId,
      agentConfig: agentConfig,
      jobMetadata: {
        agentConfig: agentConfig,
        context: {
          repRoomId: repRoomId,
          userId: roomMetadata.user_id,
          sessionId: sessionId,
          agentCloneId: roomMetadata.agent_clone_id,
          organizationId: organizationId,
          tenantId: organizationId,
          repRoomSlug: extractSessionInfo(roomName)?.slug,
          userJwtToken: '',
          threadId: `room-${roomName}-${Date.now()}`,
          participantId: roomMetadata.participant_id,
          participantName: roomMetadata.participant_name,
          isPublicAccess: roomMetadata.is_public_access,
          isAnonymous: roomMetadata.is_anonymous,
          agent_config: agentConfig
        }
      },
      metadata: {
        participantId: roomMetadata.participant_id,
        participantName: roomMetadata.participant_name,
        isPublicAccess: roomMetadata.is_public_access,
        isAnonymous: roomMetadata.is_anonymous,
        triggeredBy: 'webhook_room_started'
      }
    }
    
    const response = await fetch(agentLifecycleUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': Deno.env.get('SUPABASE_ANON_KEY') || ''
      },
      body: JSON.stringify(agentDispatchPayload)
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Failed to dispatch agent via webhook:', response.status, errorText)
    } else {
      const result = await response.json()
      console.log('✅ Successfully dispatched agent via webhook:', result)
    }
    
  } catch (error) {
    console.error('Error triggering agent join:', error)
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Only POST method is allowed',
      }),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }

  try {
    // Initialize Supabase service client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get webhook signature for verification
    const signature = req.headers.get('livekit-signature')
    const webhookSecret = Deno.env.get('LIVEKIT_WEBHOOK_SECRET')
    
    // Read request body
    const body = await req.text()
    
    // Verify webhook signature if secret is configured
    if (webhookSecret && signature) {
      const isValidSignature = await verifyWebhookSignature(body, signature, webhookSecret)
      if (!isValidSignature) {
        console.error('Invalid webhook signature')
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid webhook signature',
          }),
          {
            status: 401,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }
    } else if (webhookSecret) {
      console.warn('Webhook secret configured but no signature provided')
    }

    // Parse webhook event
    let event: LiveKitWebhookEvent
    try {
      event = JSON.parse(body)
    } catch (error) {
      console.error('Failed to parse webhook body:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid JSON in request body',
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    console.log('Received LiveKit webhook event:', {
      event: event.event,
      room: event.room.name,
      participant: event.participant?.identity
    })

    // Extract session information from room name
    const sessionInfo = extractSessionInfo(event.room.name)
    if (!sessionInfo) {
      console.log('Ignoring non-rep-room event for room:', event.room.name)
      return new Response(
        JSON.stringify({ success: true, message: 'Ignored non-rep-room event' }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    const { slug, sessionId } = sessionInfo
    const roomMetadata = parseRoomMetadata(event.room.metadata)

    // Handle different event types
    switch (event.event) {
      case 'room_started':
        await handleRoomStarted(event, sessionId, slug, roomMetadata, supabaseClient)
        break
        
      case 'room_finished':
        await handleRoomFinished(event, sessionId, supabaseClient)
        break
        
      case 'participant_joined':
        await handleParticipantJoined(event, sessionId, supabaseClient)
        break
        
      case 'participant_left':
        await handleParticipantLeft(event, sessionId, supabaseClient)
        break
        
      default:
        console.log('Unhandled event type:', event.event)
    }

    return new Response(
      JSON.stringify({ success: true }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Webhook handler error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})

// Handle room_started event
async function handleRoomStarted(
  event: LiveKitWebhookEvent,
  sessionId: string,
  slug: string,
  roomMetadata: Record<string, any> | null,
  supabaseClient: any
): Promise<void> {
  try {
    console.log(`📍 Room started: ${event.room.name} (session: ${sessionId})`)
    
    // Create or update session record
    const sessionState: Partial<SessionState> = {
      session_id: sessionId,
      room_name: event.room.name,
      rep_room_id: roomMetadata?.rep_room_id || slug,
      status: 'active',
      participant_count: 0,
      agent_joined: false,
      created_at: new Date(event.room.creation_time * 1000).toISOString(),
      updated_at: new Date().toISOString(),
      metadata: roomMetadata
    }

    const { error: sessionError } = await supabaseClient
      .from('rep_room_sessions')
      .upsert(sessionState, { onConflict: 'session_id' })

    if (sessionError) {
      console.error('Failed to create session record:', sessionError)
    } else {
      console.log('✅ Session record created/updated:', sessionId)
    }

    // Trigger agent joining if room metadata indicates agent is required
    if (roomMetadata?.agent_required && !roomMetadata?.auto_dispatch_agent) {
      await triggerAgentJoin(event.room.name, roomMetadata, supabaseClient)
    }

  } catch (error) {
    console.error('Error handling room_started event:', error)
  }
}

// Handle room_finished event
async function handleRoomFinished(
  event: LiveKitWebhookEvent,
  sessionId: string,
  supabaseClient: any
): Promise<void> {
  try {
    console.log(`📍 Room finished: ${event.room.name} (session: ${sessionId})`)
    
    // Update session status to ended
    const { error: sessionError } = await supabaseClient
      .from('rep_room_sessions')
      .update({
        status: 'ended',
        updated_at: new Date().toISOString()
      })
      .eq('session_id', sessionId)

    if (sessionError) {
      console.error('Failed to update session status:', sessionError)
    } else {
      console.log('✅ Session marked as ended:', sessionId)
    }

    // Update any participants still marked as active
    const { error: participantError } = await supabaseClient
      .from('rep_room_participants')
      .update({
        left_at: new Date().toISOString()
      })
      .eq('session_id', sessionId)
      .is('left_at', null)

    if (participantError) {
      console.error('Failed to update participant left times:', participantError)
    }

  } catch (error) {
    console.error('Error handling room_finished event:', error)
  }
}

// Handle participant_joined event
async function handleParticipantJoined(
  event: LiveKitWebhookEvent,
  sessionId: string,
  supabaseClient: any
): Promise<void> {
  try {
    if (!event.participant) return
    
    console.log(`👤 Participant joined: ${event.participant.identity} (session: ${sessionId})`)
    
    const isAgent = event.participant.identity.startsWith('agent-') || 
                   event.participant.identity.includes('voice-agent')
    
    // Create participant record
    const participantState: Partial<ParticipantState> = {
      session_id: sessionId,
      identity: event.participant.identity,
      participant_sid: event.participant.sid,
      name: event.participant.name,
      joined_at: new Date(event.participant.joined_at * 1000).toISOString(),
      is_agent: isAgent,
      metadata: event.participant.metadata ? JSON.parse(event.participant.metadata) : null
    }

    const { error: participantError } = await supabaseClient
      .from('rep_room_participants')
      .insert(participantState)

    if (participantError) {
      console.error('Failed to create participant record:', participantError)
    } else {
      console.log('✅ Participant record created:', event.participant.identity)
    }

    // Update session participant count and agent status
    const { data: currentSession } = await supabaseClient
      .from('rep_room_sessions')
      .select('participant_count, agent_joined')
      .eq('session_id', sessionId)
      .single()

    if (currentSession) {
      const { error: updateError } = await supabaseClient
        .from('rep_room_sessions')
        .update({
          participant_count: (currentSession.participant_count || 0) + 1,
          agent_joined: currentSession.agent_joined || isAgent,
          updated_at: new Date().toISOString()
        })
        .eq('session_id', sessionId)

      if (updateError) {
        console.error('Failed to update session participant count:', updateError)
      }
    }

  } catch (error) {
    console.error('Error handling participant_joined event:', error)
  }
}

// Handle participant_left event
async function handleParticipantLeft(
  event: LiveKitWebhookEvent,
  sessionId: string,
  supabaseClient: any
): Promise<void> {
  try {
    if (!event.participant) return
    
    console.log(`👤 Participant left: ${event.participant.identity} (session: ${sessionId})`)
    
    // Update participant record with left time
    const { error: participantError } = await supabaseClient
      .from('rep_room_participants')
      .update({
        left_at: new Date().toISOString()
      })
      .eq('session_id', sessionId)
      .eq('participant_sid', event.participant.sid)

    if (participantError) {
      console.error('Failed to update participant left time:', participantError)
    } else {
      console.log('✅ Participant left time updated:', event.participant.identity)
    }

    // Update session participant count
    const { data: currentSession } = await supabaseClient
      .from('rep_room_sessions')
      .select('participant_count')
      .eq('session_id', sessionId)
      .single()

    if (currentSession && currentSession.participant_count > 0) {
      const { error: updateError } = await supabaseClient
        .from('rep_room_sessions')
        .update({
          participant_count: currentSession.participant_count - 1,
          updated_at: new Date().toISOString()
        })
        .eq('session_id', sessionId)

      if (updateError) {
        console.error('Failed to update session participant count:', updateError)
      }
    }

  } catch (error) {
    console.error('Error handling participant_left event:', error)
  }
}