import React, { useState, useEffect } from 'react';
import { Loader, FileText, Bar<PERSON>hart3, Search, Download, Share2, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Brain, MessageSquare, TrendingUp, AlertCircle, RefreshCw } from 'lucide-react';

interface GeneratedContent {
  id: string;
  type: 'analysis' | 'recommendation' | 'summary' | 'insight';
  title: string;
  content: string;
  metadata?: {
    generatedBy?: string;
    timestamp?: Date;
    confidence?: number;
    sources?: string[];
    tags?: string[];
  };
}

interface ContentGeneratorProps {
  isGenerating: boolean;
  generatedContent?: GeneratedContent;
  onGenerate?: (type: GeneratedContent['type'], prompt?: string) => void;
  onSave?: (content: GeneratedContent) => void;
  onExport?: (content: GeneratedContent, format: 'pdf' | 'docx' | 'txt') => void;
  onShare?: (content: GeneratedContent) => void;
  className?: string;
}

const CONTENT_TYPES = {
  analysis: {
    label: 'Analysis',
    icon: BarChart3,
    color: 'blue',
    description: 'Deep dive analysis with data insights'
  },
  recommendation: {
    label: 'Recommendation',
    icon: TrendingUp,
    color: 'green',
    description: 'Actionable recommendations and next steps'
  },
  summary: {
    label: 'Summary',
    icon: FileText,
    color: 'purple',
    description: 'Concise summary of key points'
  },
  insight: {
    label: 'Insight',
    icon: Brain,
    color: 'orange',
    description: 'Strategic insights and observations'
  }
};

const LOADING_MESSAGES = [
  'Analyzing conversation patterns...',
  'Processing contextual data...',
  'Generating insights...',
  'Synthesizing recommendations...',
  'Finalizing content...'
];

export const ContentGenerator: React.FC<ContentGeneratorProps> = ({
  isGenerating,
  generatedContent,
  onGenerate,
  onSave,
  onExport,
  onShare,
  className = ''
}) => {
  const [selectedType, setSelectedType] = useState<GeneratedContent['type']>('analysis');
  const [customPrompt, setCustomPrompt] = useState('');
  const [loadingMessage, setLoadingMessage] = useState(LOADING_MESSAGES[0]);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Simulate loading progress and messages
  useEffect(() => {
    if (isGenerating) {
      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          const newProgress = Math.min(prev + Math.random() * 15, 95);
          const messageIndex = Math.floor((newProgress / 100) * LOADING_MESSAGES.length);
          setLoadingMessage(LOADING_MESSAGES[Math.min(messageIndex, LOADING_MESSAGES.length - 1)]);
          return newProgress;
        });
      }, 800);

      return () => clearInterval(interval);
    } else {
      setLoadingProgress(0);
      setLoadingMessage(LOADING_MESSAGES[0]);
    }
  }, [isGenerating]);

  const handleGenerate = () => {
    if (onGenerate) {
      onGenerate(selectedType, customPrompt || undefined);
      setCustomPrompt('');
    }
  };

  const handleCopyToClipboard = async () => {
    if (generatedContent) {
      try {
        await navigator.clipboard.writeText(generatedContent.content);
        setCopiedToClipboard(true);
        setTimeout(() => setCopiedToClipboard(false), 2000);
      } catch (err) {
        console.error('Failed to copy to clipboard:', err);
      }
    }
  };

  const getTypeColor = (type: GeneratedContent['type']) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-700 border-blue-200',
      green: 'bg-green-100 text-green-700 border-green-200',
      purple: 'bg-purple-100 text-purple-700 border-purple-200',
      orange: 'bg-orange-100 text-orange-700 border-orange-200'
    };
    return colors[CONTENT_TYPES[type].color as keyof typeof colors];
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Loading State
  if (isGenerating) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
        <div className="p-6">
          <div className="text-center">
            {/* Animated Icon */}
            <div className="relative mb-6">
              <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                <Sparkles className="w-8 h-8 text-blue-600 animate-pulse" />
              </div>
              <div className="absolute inset-0 w-16 h-16 mx-auto border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>

            {/* Loading Message */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Generating {CONTENT_TYPES[selectedType].label}
            </h3>
            <p className="text-gray-600 mb-4">{loadingMessage}</p>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>

            <p className="text-sm text-gray-500">
              This may take a few moments while AI processes the conversation context...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI Content Generator</h3>
          </div>
          
          {generatedContent && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                Generated {generatedContent.metadata?.timestamp?.toLocaleTimeString()}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="p-4">
        {!generatedContent ? (
          /* Generation Controls */
          <div className="space-y-6">
            {/* Content Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Content Type
              </label>
              <div className="grid grid-cols-2 gap-3">
                {Object.entries(CONTENT_TYPES).map(([key, config]) => {
                  const Icon = config.icon;
                  const isSelected = selectedType === key;
                  
                  return (
                    <button
                      key={key}
                      onClick={() => setSelectedType(key as GeneratedContent['type'])}
                      className={`p-4 border-2 rounded-lg text-left transition-all ${
                        isSelected
                          ? `${getTypeColor(key as GeneratedContent['type'])} border-current`
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-2">
                        <Icon className="w-5 h-5" />
                        <span className="font-medium">{config.label}</span>
                      </div>
                      <p className="text-sm opacity-75">{config.description}</p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Custom Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Custom Instructions (Optional)
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="Add specific instructions or focus areas for the AI to consider..."
                className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
              />
            </div>

            {/* Advanced Options */}
            <div>
              <button
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900"
              >
                <span>Advanced Options</span>
                <RefreshCw className={`w-4 h-4 transition-transform ${showAdvancedOptions ? 'rotate-180' : ''}`} />
              </button>
              
              {showAdvancedOptions && (
                <div className="mt-3 p-4 bg-gray-50 rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">Include Sources</label>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">Add Confidence Score</label>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">Generate Tags</label>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </div>
                </div>
              )}
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerate}
              disabled={!onGenerate}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
            >
              <Sparkles className="w-5 h-5" />
              <span>Generate {CONTENT_TYPES[selectedType].label}</span>
            </button>
          </div>
        ) : (
          /* Generated Content Display */
          <div className="space-y-6">
            {/* Content Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getTypeColor(generatedContent.type)}`}>
                    {CONTENT_TYPES[generatedContent.type].label}
                  </span>
                  {generatedContent.metadata?.confidence && (
                    <span className={`text-sm font-medium ${getConfidenceColor(generatedContent.metadata.confidence)}`}>
                      {generatedContent.metadata.confidence}% confidence
                    </span>
                  )}
                </div>
                <h4 className="text-lg font-semibold text-gray-900">{generatedContent.title}</h4>
                {generatedContent.metadata?.generatedBy && (
                  <p className="text-sm text-gray-500 mt-1">
                    Generated by {generatedContent.metadata.generatedBy}
                  </p>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={handleCopyToClipboard}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Copy to clipboard"
                >
                  {copiedToClipboard ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
                
                {onShare && (
                  <button
                    onClick={() => onShare(generatedContent)}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Share content"
                  >
                    <Share2 className="w-4 h-4" />
                  </button>
                )}
                
                {onExport && (
                  <div className="relative group">
                    <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                    </button>
                    <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                      {['pdf', 'docx', 'txt'].map(format => (
                        <button
                          key={format}
                          onClick={() => onExport(generatedContent, format as 'pdf' | 'docx' | 'txt')}
                          className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
                        >
                          Export as {format.toUpperCase()}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Content Body */}
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                {generatedContent.content}
              </div>
            </div>

            {/* Metadata */}
            {generatedContent.metadata && (
              <div className="border-t border-gray-200 pt-4">
                {/* Tags */}
                {generatedContent.metadata.tags && generatedContent.metadata.tags.length > 0 && (
                  <div className="mb-3">
                    <span className="text-sm font-medium text-gray-700 mr-2">Tags:</span>
                    <div className="inline-flex flex-wrap gap-1">
                      {generatedContent.metadata.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Sources */}
                {generatedContent.metadata.sources && generatedContent.metadata.sources.length > 0 && (
                  <div>
                    <span className="text-sm font-medium text-gray-700">Sources:</span>
                    <ul className="mt-1 text-sm text-gray-600">
                      {generatedContent.metadata.sources.map((source, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-gray-400">•</span>
                          <span>{source}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <button
                onClick={() => window.location.reload()}
                className="text-sm text-gray-600 hover:text-gray-900 flex items-center space-x-1"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Generate New Content</span>
              </button>

              {onSave && (
                <button
                  onClick={() => onSave(generatedContent)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                >
                  Save Content
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};