import React, { useCallback, useEffect, useState } from 'react';
import { Users, Monitor, MessageCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { useTouchGestures } from '../../../hooks/useResponsiveLayout';

export type MobileTab = 'participants' | 'presentation' | 'conversation';

export interface TabBadge {
  count: number;
  type: 'notification' | 'warning' | 'success';
}

export interface MobileTabNavigationProps {
  activeTab: MobileTab;
  onTabChange: (tab: MobileTab) => void;
  badges?: Partial<Record<MobileTab, TabBadge>>;
  className?: string;
  enableSwipeGestures?: boolean;
  animationDuration?: number;
}

const TAB_CONFIG = {
  participants: {
    id: 'participants' as const,
    label: 'Participants',
    icon: Users,
    shortLabel: 'People'
  },
  presentation: {
    id: 'presentation' as const,
    label: 'Presentation',
    icon: Monitor,
    shortLabel: 'Present'
  },
  conversation: {
    id: 'conversation' as const,
    label: 'Conversation',
    icon: MessageCircle,
    shortLabel: 'Chat'
  }
};

const BADGE_STYLES = {
  notification: 'bg-blue-500 text-white',
  warning: 'bg-orange-500 text-white',
  success: 'bg-green-500 text-white'
};

/**
 * Mobile tab navigation component with swipe gesture support
 */
export const MobileTabNavigation: React.FC<MobileTabNavigationProps> = ({
  activeTab,
  onTabChange,
  badges = {},
  className = '',
  enableSwipeGestures = true,
  animationDuration = 300
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const touchGestures = useTouchGestures();

  const tabs = Object.values(TAB_CONFIG);
  const activeIndex = tabs.findIndex(tab => tab.id === activeTab);

  // Handle swipe gestures
  const handleSwipe = useCallback((direction: 'left' | 'right' | 'up' | 'down' | null) => {
    if (!enableSwipeGestures || !direction || isAnimating) return;

    let newIndex = activeIndex;
    
    if (direction === 'left' && activeIndex < tabs.length - 1) {
      newIndex = activeIndex + 1;
    } else if (direction === 'right' && activeIndex > 0) {
      newIndex = activeIndex - 1;
    }

    if (newIndex !== activeIndex) {
      setIsAnimating(true);
      onTabChange(tabs[newIndex].id);
      
      setTimeout(() => {
        setIsAnimating(false);
      }, animationDuration);
    }
  }, [activeIndex, tabs, onTabChange, enableSwipeGestures, isAnimating, animationDuration]);

  // Touch event handlers
  const onTouchEnd = useCallback(() => {
    const swipeDirection = touchGestures.onTouchEnd();
    handleSwipe(swipeDirection);
  }, [touchGestures, handleSwipe]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target !== document.body) return;

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          if (activeIndex > 0) {
            onTabChange(tabs[activeIndex - 1].id);
          }
          break;
        case 'ArrowRight':
          e.preventDefault();
          if (activeIndex < tabs.length - 1) {
            onTabChange(tabs[activeIndex + 1].id);
          }
          break;
        case '1':
        case '2':
        case '3': {
          e.preventDefault();
          const tabIndex = parseInt(e.key) - 1;
          if (tabIndex >= 0 && tabIndex < tabs.length) {
            onTabChange(tabs[tabIndex].id);
          }
          break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [activeIndex, tabs, onTabChange]);

  const renderBadge = (tabId: MobileTab) => {
    const badge = badges[tabId];
    if (!badge || badge.count === 0) return null;

    return (
      <span
        className={`
          absolute -top-1 -right-1 min-w-[18px] h-[18px] 
          rounded-full text-xs font-medium flex items-center justify-center
          ${BADGE_STYLES[badge.type]}
          animate-pulse
        `}
        aria-label={`${badge.count} ${badge.type === 'notification' ? 'notifications' : badge.type}`}
      >
        {badge.count > 99 ? '99+' : badge.count}
      </span>
    );
  };

  const renderNavigationArrows = () => (
    <div className="flex items-center justify-between px-2">
      <button
        onClick={() => activeIndex > 0 && onTabChange(tabs[activeIndex - 1].id)}
        disabled={activeIndex === 0 || isAnimating}
        className={`
          p-2 rounded-full transition-all duration-200
          ${activeIndex === 0 
            ? 'text-gray-300 cursor-not-allowed' 
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50 active:scale-95'
          }
        `}
        aria-label="Previous tab"
      >
        <ChevronLeft size={20} />
      </button>

      <div className="flex space-x-1">
        {tabs.map((_, index) => (
          <div
            key={index}
            className={`
              w-2 h-2 rounded-full transition-all duration-200
              ${index === activeIndex ? 'bg-blue-600' : 'bg-gray-300'}
            `}
          />
        ))}
      </div>

      <button
        onClick={() => activeIndex < tabs.length - 1 && onTabChange(tabs[activeIndex + 1].id)}
        disabled={activeIndex === tabs.length - 1 || isAnimating}
        className={`
          p-2 rounded-full transition-all duration-200
          ${activeIndex === tabs.length - 1 
            ? 'text-gray-300 cursor-not-allowed' 
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50 active:scale-95'
          }
        `}
        aria-label="Next tab"
      >
        <ChevronRight size={20} />
      </button>
    </div>
  );

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      {/* Tab Navigation */}
      <div
        className="flex"
        onTouchStart={enableSwipeGestures ? touchGestures.onTouchStart : undefined}
        onTouchMove={enableSwipeGestures ? touchGestures.onTouchMove : undefined}
        onTouchEnd={enableSwipeGestures ? onTouchEnd : undefined}
      >
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = tab.id === activeTab;
          
          return (
            <button
              key={tab.id}
              onClick={() => !isAnimating && onTabChange(tab.id)}
              disabled={isAnimating}
              className={`
                flex-1 relative px-4 py-3 text-center transition-all duration-200
                ${isActive
                  ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50 active:bg-gray-100'
                }
                ${isAnimating ? 'pointer-events-none' : ''}
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset
              `}
              aria-pressed={isActive}
              aria-label={`Switch to ${tab.label} tab`}
            >
              <div className="flex flex-col items-center space-y-1">
                <div className="relative">
                  <Icon 
                    size={20} 
                    className={`
                      transition-transform duration-200
                      ${isActive ? 'scale-110' : 'scale-100'}
                    `}
                  />
                  {renderBadge(tab.id)}
                </div>
                <span className="text-xs font-medium">
                  {tab.shortLabel}
                </span>
              </div>
            </button>
          );
        })}
      </div>

      {/* Navigation Arrows and Indicators */}
      {enableSwipeGestures && (
        <div className="py-2 border-t border-gray-100">
          {renderNavigationArrows()}
        </div>
      )}

      {/* Active Tab Indicator Animation */}
      <div
        className="absolute bottom-0 left-0 h-0.5 bg-blue-600 transition-all duration-300 ease-out"
        style={{
          width: `${100 / tabs.length}%`,
          transform: `translateX(${activeIndex * 100}%)`
        }}
      />
    </div>
  );
};

/**
 * Hook for managing mobile tab state with persistence
 */
export const useMobileTabNavigation = (
  initialTab: MobileTab = 'participants',
  persistKey?: string
) => {
  const [activeTab, setActiveTab] = useState<MobileTab>(() => {
    if (persistKey && typeof window !== 'undefined') {
      const saved = localStorage.getItem(`mobile-tab-${persistKey}`);
      if (saved && ['participants', 'presentation', 'conversation'].includes(saved)) {
        return saved as MobileTab;
      }
    }
    return initialTab;
  });

  const [badges, setBadges] = useState<Partial<Record<MobileTab, TabBadge>>>({});

  const changeTab = useCallback((tab: MobileTab) => {
    setActiveTab(tab);
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`mobile-tab-${persistKey}`, tab);
    }
  }, [persistKey]);

  const updateBadge = useCallback((tab: MobileTab, badge: TabBadge | null) => {
    setBadges(prev => {
      if (badge === null) {
        const { [tab]: removed, ...rest } = prev;
        return rest;
      }
      return { ...prev, [tab]: badge };
    });
  }, []);

  const clearBadge = useCallback((tab: MobileTab) => {
    updateBadge(tab, null);
  }, [updateBadge]);

  const incrementBadge = useCallback((tab: MobileTab, type: TabBadge['type'] = 'notification') => {
    setBadges(prev => {
      const current = prev[tab];
      return {
        ...prev,
        [tab]: {
          count: (current?.count || 0) + 1,
          type
        }
      };
    });
  }, []);

  return {
    activeTab,
    badges,
    changeTab,
    updateBadge,
    clearBadge,
    incrementBadge
  };
};