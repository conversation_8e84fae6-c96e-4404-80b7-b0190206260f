import React, { useState, useEffect } from 'react';
import { Monitor, Loader, FileText, BarChart3, Search, AlertCircle, Sparkles, Target, ArrowRight } from 'lucide-react';
import { PresentationContent } from '../../types/rep-room';
import { KeywordAnalysisChart } from './presentation/KeywordAnalysisChart';
import { CompetitorAnalysisView } from './presentation/CompetitorAnalysisView';
import { ContentGenerator } from './presentation/ContentGenerator';

// Type definitions for enhanced data structures
interface KeywordData {
  term: string;
  volume: number;
  difficulty: number;
  category: 'product' | 'competitor' | 'sentiment';
  trend?: 'up' | 'down' | 'stable';
}

interface CompetitorData {
  id: string;
  name: string;
  domain: string;
  description: string;
  logo?: string;
  metrics: {
    traffic: number;
    domainAuthority: number;
    marketShare: number;
    socialFollowing: number;
    contentScore: number;
    brandStrength: number;
  };
  strengths: string[];
  weaknesses: string[];
  threatLevel: 'low' | 'medium' | 'high';
  trend: 'up' | 'down' | 'stable';
  positioning: string;
  keyFeatures: string[];
  pricing?: {
    model: string;
    range: string;
  };
}

interface GeneratedContent {
  id: string;
  type: 'analysis' | 'recommendation' | 'summary' | 'insight';
  title: string;
  content: string;
  metadata?: {
    generatedBy?: string;
    timestamp?: Date;
    confidence?: number;
    sources?: string[];
    tags?: string[];
  };
}

interface PresentationAreaProps {
  content: PresentationContent;
  className?: string;
  onContentGenerate?: (type: string, prompt?: string) => void;
  onContentSave?: (content: GeneratedContent) => void;
  onContentExport?: (content: GeneratedContent, format: string) => void;
  onContentShare?: (content: GeneratedContent) => void;
}

export const PresentationArea: React.FC<PresentationAreaProps> = ({ 
  content, 
  className = '',
  onContentGenerate,
  onContentSave,
  onContentExport,
  onContentShare
}) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousContent, setPreviousContent] = useState<PresentationContent | null>(null);
  const [enhancedMode, setEnhancedMode] = useState(true);

  // Handle content transitions
  useEffect(() => {
    if (previousContent && previousContent.type !== content.type) {
      setIsTransitioning(true);
      const timer = setTimeout(() => {
        setIsTransitioning(false);
      }, 300);
      return () => clearTimeout(timer);
    }
    setPreviousContent(content);
  }, [content.type, previousContent]);

  // Get icon based on content type
  const getContentIcon = (type: string = content.type) => {
    switch (type) {
      case 'keyword-analysis':
        return <Search className="w-5 h-5" />;
      case 'competitor-analysis':
        return <BarChart3 className="w-5 h-5" />;
      case 'content-generator':
        return <Sparkles className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  // Get content type display name
  const getContentTypeName = (type: string = content.type) => {
    switch (type) {
      case 'keyword-analysis':
        return 'Keyword Analysis';
      case 'competitor-analysis':
        return 'Competitor Analysis';
      case 'content-generator':
        return 'AI Content Generator';
      default:
        return 'Content';
    }
  };

  // Transform legacy data to enhanced format
  const transformKeywordData = (data: unknown): KeywordData[] => {
    if (!data || typeof data !== 'object') return [];
    
    const legacyData = data as Record<string, unknown>;
    const keywords = legacyData.keywords as Array<Record<string, unknown>> | undefined;
    
    if (!keywords || !Array.isArray(keywords)) return [];
    
    return keywords.map((keyword, index: number) => {
      const categories: Array<'product' | 'competitor' | 'sentiment'> = ['product', 'competitor', 'sentiment'];
      const trends: Array<'up' | 'down' | 'stable'> = ['up', 'down', 'stable'];
      
      return {
        term: (keyword.term as string) || (keyword.keyword as string) || `Keyword ${index + 1}`,
        volume: (keyword.volume as number) || Math.floor(Math.random() * 100000),
        difficulty: (keyword.difficulty as number) || Math.floor(Math.random() * 100),
        category: (keyword.category && categories.includes(keyword.category as 'product' | 'competitor' | 'sentiment'))
          ? keyword.category as 'product' | 'competitor' | 'sentiment'
          : categories[Math.floor(Math.random() * 3)],
        trend: (keyword.trend && trends.includes(keyword.trend as 'up' | 'down' | 'stable'))
          ? keyword.trend as 'up' | 'down' | 'stable'
          : trends[Math.floor(Math.random() * 3)]
      };
    });
  };

  const transformCompetitorData = (data: unknown): CompetitorData[] => {
    if (!data || typeof data !== 'object') return [];
    
    const legacyData = data as Record<string, unknown>;
    const competitors = legacyData.competitors as Array<Record<string, unknown>> | undefined;
    
    if (!competitors || !Array.isArray(competitors)) return [];
    
    return competitors.map((competitor, index: number) => {
      const threatLevels: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high'];
      const trends: Array<'up' | 'down' | 'stable'> = ['up', 'down', 'stable'];
      
      return {
        id: (competitor.id as string) || `competitor-${index}`,
        name: (competitor.name as string) || `Competitor ${index + 1}`,
        domain: (competitor.domain as string) || (competitor.website as string) || `competitor${index}.com`,
        description: (competitor.description as string) || 'Competitor description not available',
        logo: competitor.logo as string | undefined,
        metrics: {
          traffic: (competitor.traffic as number) || Math.floor(Math.random() * 10000000),
          domainAuthority: (competitor.domain_authority as number) || Math.floor(Math.random() * 100),
          marketShare: (competitor.market_share as number) || Math.floor(Math.random() * 50),
          socialFollowing: (competitor.social_following as number) || Math.floor(Math.random() * 1000000),
          contentScore: (competitor.content_score as number) || Math.floor(Math.random() * 100),
          brandStrength: (competitor.brand_strength as number) || Math.floor(Math.random() * 100)
        },
        strengths: (competitor.strengths as string[]) || ['Strong market presence', 'Good user experience'],
        weaknesses: (competitor.weaknesses as string[]) || ['Limited features', 'Higher pricing'],
        threatLevel: competitor.strength === 'high' ? 'high' : competitor.strength === 'medium' ? 'medium' : 'low',
        trend: (competitor.trend && trends.includes(competitor.trend as 'up' | 'down' | 'stable'))
          ? competitor.trend as 'up' | 'down' | 'stable'
          : trends[Math.floor(Math.random() * 3)],
        positioning: (competitor.positioning as string) || 'Market positioning analysis pending',
        keyFeatures: (competitor.key_features as string[]) || (competitor.features as string[]) || ['Feature 1', 'Feature 2'],
        pricing: competitor.pricing ? {
          model: ((competitor.pricing as Record<string, unknown>).model as string) || 'Subscription',
          range: ((competitor.pricing as Record<string, unknown>).range as string) || '$10-50/month'
        } : undefined
      };
    });
  };

  // Render loading state with enhanced animation
  if (content.loading) {
    return (
      <div className={`bg-white flex flex-col h-full transition-all duration-300 ${className}`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
          <div className="flex items-center space-x-2">
            <Monitor className="w-5 h-5 text-gray-600" />
            <h2 className="font-semibold text-gray-900">Presentation</h2>
          </div>
        </div>

        {/* Enhanced Loading Content */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            {/* Animated Loading Icon */}
            <div className="relative mb-6">
              <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                {getContentIcon()}
              </div>
              <div className="absolute inset-0 w-16 h-16 mx-auto border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>

            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Generating {getContentTypeName()}
            </h3>
            
            {content.generatedBy && (
              <p className="text-gray-600 mb-4">
                Agent {content.generatedBy} is analyzing conversation context...
              </p>
            )}

            {/* Loading Progress Indicator */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>

            <p className="text-sm text-gray-500">
              This may take a few moments while we process the data
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Enhanced keyword analysis renderer
  const renderEnhancedKeywordAnalysis = () => {
    const keywordData = transformKeywordData(content.data);
    
    if (keywordData.length === 0) {
      return (
        <div className="text-center py-12">
          <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Keyword Data Available</h3>
          <p className="text-gray-600">Start a conversation to generate keyword analysis</p>
        </div>
      );
    }

    return (
      <KeywordAnalysisChart
        keywords={keywordData}
        onKeywordClick={(keyword) => {
          console.log('Keyword clicked:', keyword);
          // Handle keyword click - could trigger detailed analysis
        }}
        onFilterChange={(filters) => {
          console.log('Filters changed:', filters);
          // Handle filter changes
        }}
        className="h-full"
      />
    );
  };

  // Enhanced competitor analysis renderer
  const renderEnhancedCompetitorAnalysis = () => {
    const competitorData = transformCompetitorData(content.data);
    
    if (competitorData.length === 0) {
      return (
        <div className="text-center py-12">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Competitor Data Available</h3>
          <p className="text-gray-600">Start analyzing competitors to see detailed insights</p>
        </div>
      );
    }

    return (
      <CompetitorAnalysisView
        competitors={competitorData}
        onCompetitorSelect={(competitor) => {
          console.log('Competitor selected:', competitor);
          // Handle competitor selection
        }}
        className="h-full"
      />
    );
  };

  // Content generator renderer
  const renderContentGenerator = () => {
    let generatedContent: GeneratedContent | undefined;
    
    if (content.data && typeof content.data === 'object' && 'id' in content.data) {
      generatedContent = content.data as GeneratedContent;
    }
    
    return (
      <ContentGenerator
        isGenerating={false}
        generatedContent={generatedContent}
        onGenerate={onContentGenerate}
        onSave={onContentSave}
        onExport={onContentExport}
        onShare={onContentShare}
        className="h-full"
      />
    );
  };

  // Legacy content renderers (fallback)
  const renderLegacyKeywordAnalysis = () => {
    if (!content.data || typeof content.data !== 'object') return null;

    const data = content.data as Record<string, unknown>;
    const keywords = data.keywords as Array<Record<string, unknown>> | undefined;
    const insights = data.insights as string[] | undefined;
    
    if (!keywords || !Array.isArray(keywords)) return null;

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">Top Keywords</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {keywords.slice(0, 12).map((keyword, index: number) => (
              <div key={index} className="bg-blue-50 p-3 rounded-lg">
                <div className="font-medium text-blue-900">
                  {(keyword.term as string) || (keyword.keyword as string) || 'Unknown'}
                </div>
                <div className="text-sm text-blue-600">
                  Volume: {(keyword.volume as number)?.toLocaleString() || 'N/A'}
                </div>
                <div className="text-sm text-blue-600">
                  Difficulty: {(keyword.difficulty as number) || 'N/A'}
                </div>
              </div>
            ))}
          </div>
        </div>

        {insights && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Key Insights</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <ul className="space-y-2">
                {insights.map((insight: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-gray-700">{insight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderLegacyCompetitorAnalysis = () => {
    if (!content.data || typeof content.data !== 'object') return null;

    const data = content.data as Record<string, unknown>;
    const competitors = data.competitors as Array<Record<string, unknown>> | undefined;
    const summary = data.summary as string | undefined;
    
    if (!competitors || !Array.isArray(competitors)) return null;

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">Competitor Overview</h3>
          <div className="space-y-3">
            {competitors.slice(0, 5).map((competitor, index: number) => (
              <div key={index} className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">{competitor.name as string}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    competitor.strength === 'high' ? 'bg-red-100 text-red-800' :
                    competitor.strength === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {competitor.strength as string} threat
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{competitor.description as string}</p>
                <div className="flex space-x-4 text-sm">
                  {competitor.traffic && (
                    <span className="text-gray-500">
                      Traffic: {(competitor.traffic as number).toLocaleString()}
                    </span>
                  )}
                  {competitor.domain_authority && (
                    <span className="text-gray-500">
                      DA: {competitor.domain_authority as number}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {summary && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Analysis Summary</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-700">{summary}</p>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderDefaultContent = () => {
    if (typeof content.data === 'string') {
      return (
        <div className="prose max-w-none">
          <div className="whitespace-pre-wrap text-gray-700">
            {content.data}
          </div>
        </div>
      );
    }

    if (content.data && typeof content.data === 'object') {
      return (
        <div className="bg-gray-50 p-4 rounded-lg">
          <pre className="text-sm text-gray-700 overflow-auto">
            {JSON.stringify(content.data, null, 2)}
          </pre>
        </div>
      );
    }

    return (
      <div className="text-center text-gray-500 py-8">
        <AlertCircle className="w-8 h-8 mx-auto mb-2" />
        <p>No content available</p>
      </div>
    );
  };

  // Main content renderer with enhanced mode toggle
  const renderContent = () => {
    const contentWithTransition = (
      <div className={`transition-all duration-300 ${isTransitioning ? 'opacity-0 transform scale-95' : 'opacity-100 transform scale-100'}`}>
        {enhancedMode ? (
          // Enhanced mode
          (() => {
            switch (content.type) {
              case 'keyword-analysis':
                return renderEnhancedKeywordAnalysis();
              case 'competitor-analysis':
                return renderEnhancedCompetitorAnalysis();
              case 'content-generator':
                return renderContentGenerator();
              default:
                return renderDefaultContent();
            }
          })()
        ) : (
          // Legacy mode
          (() => {
            switch (content.type) {
              case 'keyword-analysis':
                return renderLegacyKeywordAnalysis();
              case 'competitor-analysis':
                return renderLegacyCompetitorAnalysis();
              default:
                return renderDefaultContent();
            }
          })()
        )}
      </div>
    );

    return contentWithTransition;
  };

  return (
    <div className={`bg-white flex flex-col h-full transition-all duration-300 ${className}`}>
      {/* Enhanced Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Monitor className="w-5 h-5 text-gray-600" />
            <h2 className="font-semibold text-gray-900">Presentation</h2>
          </div>
          
          {/* Enhanced Mode Toggle */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setEnhancedMode(!enhancedMode)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                enhancedMode
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {enhancedMode ? 'Enhanced' : 'Legacy'} Mode
            </button>
            
            {/* Content Type Badge */}
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {getContentIcon()}
              <span>{getContentTypeName()}</span>
            </div>
          </div>
        </div>

        {/* Content Title */}
        {content.title && (
          <h3 className="text-lg font-medium text-gray-900 mt-2">
            {content.title}
          </h3>
        )}

        {/* Generated By */}
        {content.generatedBy && (
          <div className="flex items-center space-x-2 mt-2">
            <p className="text-sm text-gray-500">
              Generated by Agent {content.generatedBy}
            </p>
            {enhancedMode && (
              <div className="flex items-center space-x-1 text-xs text-blue-600">
                <Sparkles className="w-3 h-3" />
                <span>Enhanced Analysis</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};