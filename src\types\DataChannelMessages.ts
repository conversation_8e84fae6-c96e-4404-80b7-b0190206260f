/**
 * TypeScript interfaces for LiveKit data channel messages
 * Phase 4: Real-time Features & Data Channels
 * 
 * Provides structured message format for:
 * - Session-aware communication
 * - Chat messages
 * - System events
 * - Custom actions
 * - Agent responses
 */

// Base message interface with session context
export interface BaseDataChannelMessage {
  type: string;
  timestamp: number;
  sessionId: string;
  participantId?: string;
  messageId?: string;
}

// Participant information
export interface ParticipantInfo {
  id: string;
  name: string;
  email?: string;
  isAgent?: boolean;
  joinedAt: number;
  lastSeen: number;
}

// Participant state information
export interface ParticipantState {
  id: string;
  isMuted: boolean;
  isSpeaking: boolean;
  isConnected: boolean;
  audioLevel?: number;
  lastActivity: number;
}

// Chat message types
export interface ChatMessage extends BaseDataChannelMessage {
  type: 'chat_message';
  content: {
    text: string;
    sender: ParticipantInfo;
    replyTo?: string;
    metadata?: Record<string, unknown>;
  };
}

// System event messages
export interface SystemEventMessage extends BaseDataChannelMessage {
  type: 'system_event';
  event: 'participant_joined' | 'participant_left' | 'session_started' | 'session_ended' | 'agent_joined' | 'agent_left';
  content: {
    participant?: ParticipantInfo;
    message?: string;
    metadata?: Record<string, unknown>;
  };
}

// Participant list update
export interface ParticipantListUpdate extends BaseDataChannelMessage {
  type: 'participant_list_update';
  content: {
    participants: ParticipantInfo[];
    participantStates: Record<string, ParticipantState>;
    totalCount: number;
  };
}

// Voice transcription messages
export interface TranscriptionMessage extends BaseDataChannelMessage {
  type: 'transcription';
  content: {
    text: string;
    speaker: ParticipantInfo;
    isInterim: boolean;
    confidence?: number;
    language?: string;
  };
}

// Agent response messages (AG-UI compatible)
export interface AgentResponseStart extends BaseDataChannelMessage {
  type: 'agent_response_start';
  content: {
    responseId: string;
    agentId: string;
    context?: Record<string, unknown>;
  };
}

export interface AgentResponseContent extends BaseDataChannelMessage {
  type: 'agent_response_content';
  content: {
    responseId: string;
    delta: string;
    isComplete: boolean;
  };
}

export interface AgentResponseEnd extends BaseDataChannelMessage {
  type: 'agent_response_end';
  content: {
    responseId: string;
    finalText: string;
    metadata?: Record<string, unknown>;
  };
}

// Voice activity messages
export interface VoiceActivityMessage extends BaseDataChannelMessage {
  type: 'voice_activity';
  content: {
    participantId: string;
    activity: 'speaking_start' | 'speaking_end' | 'muted' | 'unmuted';
    audioLevel?: number;
  };
}

// Interruption messages
export interface InterruptionMessage extends BaseDataChannelMessage {
  type: 'interruption';
  content: {
    interruptedBy: string;
    reason?: string;
  };
}

// Session state messages
export interface SessionStateMessage extends BaseDataChannelMessage {
  type: 'session_state';
  content: {
    state: 'active' | 'paused' | 'ended';
    participants: ParticipantInfo[];
    metadata?: Record<string, unknown>;
  };
}

// Custom action messages
export interface CustomActionMessage extends BaseDataChannelMessage {
  type: 'custom_action';
  action: string;
  content: {
    data: Record<string, unknown>;
    targetParticipant?: string;
    requiresResponse?: boolean;
  };
}

// Presentation/UI update messages
export interface PresentationUpdateMessage extends BaseDataChannelMessage {
  type: 'presentation_update';
  content: {
    updateType: 'slide_change' | 'content_update' | 'annotation' | 'highlight';
    data: Record<string, unknown>;
    targetParticipants?: string[];
  };
}

// Error messages
export interface ErrorMessage extends BaseDataChannelMessage {
  type: 'error';
  content: {
    errorCode: string;
    message: string;
    details?: Record<string, unknown>;
    recoverable: boolean;
  };
}

// Heartbeat/ping messages
export interface HeartbeatMessage extends BaseDataChannelMessage {
  type: 'heartbeat';
  content: {
    status: 'ping' | 'pong';
    serverTime?: number;
  };
}

// Union type for all possible data channel messages
export type DataChannelMessage = 
  | ChatMessage
  | SystemEventMessage
  | ParticipantListUpdate
  | TranscriptionMessage
  | AgentResponseStart
  | AgentResponseContent
  | AgentResponseEnd
  | VoiceActivityMessage
  | InterruptionMessage
  | SessionStateMessage
  | CustomActionMessage
  | PresentationUpdateMessage
  | ErrorMessage
  | HeartbeatMessage;

// Legacy AG-UI message types for backward compatibility
export interface LegacyTextMessageStart extends BaseDataChannelMessage {
  type: 'TEXT_MESSAGE_START' | 'TextMessageStart';
  content?: {
    id: string;
    role: 'user' | 'assistant';
  };
  id?: string;
  role?: 'user' | 'assistant';
}

export interface LegacyTextMessageContent extends BaseDataChannelMessage {
  type: 'TEXT_MESSAGE_CONTENT' | 'TextMessageContent';
  content?: {
    id: string;
    delta: string;
  };
  id?: string;
  delta?: string;
  text?: string;
}

export interface LegacyTextMessageEnd extends BaseDataChannelMessage {
  type: 'TEXT_MESSAGE_END' | 'TextMessageEnd';
  content?: {
    id: string;
  };
  id?: string;
}

export interface LegacyUserTranscriptFinal extends BaseDataChannelMessage {
  type: 'USER_TRANSCRIPT_FINAL';
  content?: {
    text: string;
  };
  text?: string;
}

// Legacy message union type
export type LegacyDataChannelMessage = 
  | LegacyTextMessageStart
  | LegacyTextMessageContent
  | LegacyTextMessageEnd
  | LegacyUserTranscriptFinal;

// Combined message type for backward compatibility
export type AllDataChannelMessages = DataChannelMessage | LegacyDataChannelMessage;

// Message validation utilities
export function isValidDataChannelMessage(data: unknown): data is AllDataChannelMessages {
  if (!data || typeof data !== 'object') {
    return false;
  }
  
  const message = data as Record<string, unknown>;
  return typeof message.type === 'string' && typeof message.timestamp === 'number';
}

export function createBaseMessage(
  type: string, 
  sessionId: string, 
  participantId?: string
): BaseDataChannelMessage {
  return {
    type,
    timestamp: Date.now(),
    sessionId,
    participantId,
    messageId: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  };
}

// Message factory functions
export function createChatMessage(
  sessionId: string,
  text: string,
  sender: ParticipantInfo,
  replyTo?: string
): ChatMessage {
  return {
    type: 'chat_message',
    timestamp: Date.now(),
    sessionId,
    participantId: sender.id,
    messageId: `chat_message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content: {
      text,
      sender,
      replyTo
    }
  };
}

export function createSystemEventMessage(
  sessionId: string,
  event: SystemEventMessage['event'],
  participant?: ParticipantInfo,
  message?: string
): SystemEventMessage {
  return {
    type: 'system_event',
    timestamp: Date.now(),
    sessionId,
    messageId: `system_event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    event,
    content: {
      participant,
      message
    }
  };
}

export function createParticipantListUpdate(
  sessionId: string,
  participants: ParticipantInfo[],
  participantStates: Record<string, ParticipantState>
): ParticipantListUpdate {
  return {
    type: 'participant_list_update',
    timestamp: Date.now(),
    sessionId,
    messageId: `participant_list_update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content: {
      participants,
      participantStates,
      totalCount: participants.length
    }
  };
}

export function createTranscriptionMessage(
  sessionId: string,
  text: string,
  speaker: ParticipantInfo,
  isInterim: boolean = false,
  confidence?: number
): TranscriptionMessage {
  return {
    type: 'transcription',
    timestamp: Date.now(),
    sessionId,
    participantId: speaker.id,
    messageId: `transcription_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content: {
      text,
      speaker,
      isInterim,
      confidence
    }
  };
}

export function createVoiceActivityMessage(
  sessionId: string,
  participantId: string,
  activity: VoiceActivityMessage['content']['activity'],
  audioLevel?: number
): VoiceActivityMessage {
  return {
    type: 'voice_activity',
    timestamp: Date.now(),
    sessionId,
    participantId,
    messageId: `voice_activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content: {
      participantId,
      activity,
      audioLevel
    }
  };
}

export function createInterruptionMessage(
  sessionId: string,
  interruptedBy: string,
  reason?: string
): InterruptionMessage {
  return {
    type: 'interruption',
    timestamp: Date.now(),
    sessionId,
    participantId: interruptedBy,
    messageId: `interruption_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content: {
      interruptedBy,
      reason
    }
  };
}