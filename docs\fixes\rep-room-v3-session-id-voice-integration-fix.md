# Rep Room V3 Session ID Voice Integration Fix

## Problem Summary

**Critical Issue**: Session IDs from Rep Room V3 URLs were not being properly connected to LiveKit voice rooms, causing multiple users accessing the same session URL to join different voice rooms instead of the same collaborative session.

### Root Cause Analysis

1. **Frontend Issue**: `UnifiedVoiceContext.tsx` was only extracting `rep_room_id` from URLs but ignoring the session ID portion
2. **Backend Issue**: `rep-room-voice-token` function was generating its own session ID instead of using the one from the frontend
3. **Room Naming Issue**: LiveKit rooms were named using `participantId` instead of `sessionId`, preventing proper session correlation

### URL Pattern Analysis
- **Expected URL**: `/rroom-v3/t1/session-1234567890-abc123`
- **Session ID**: `session-1234567890-abc123`
- **Rep Room ID**: `t1`

## Solution Implemented

### 1. Frontend Fix (UnifiedVoiceContext.tsx)

**Before:**
```typescript
// Only extracted rep_room_id, ignored session ID
const repRoomId = window.location.pathname.includes('/rroom/')
  ? window.location.pathname.split('/rroom/')[1]?.split('/')[0] || 't1'
  : 't1';

const tokenRequest = {
  rep_room_id: repRoomId,
  participant_name: user?.email || 'Anonymous User'
};
```

**After:**
```typescript
// Extract both rep room ID and session ID from URL
const pathParts = window.location.pathname.split('/');

let repRoomId = 't1';
let sessionId = null;

if (window.location.pathname.includes('/rroom-v3/')) {
  const rRoomIndex = pathParts.findIndex(part => part === 'rroom-v3');
  if (rRoomIndex !== -1 && pathParts[rRoomIndex + 1]) {
    repRoomId = pathParts[rRoomIndex + 1]; // e.g., 't1'
    if (pathParts[rRoomIndex + 2] && pathParts[rRoomIndex + 2].startsWith('session-')) {
      sessionId = pathParts[rRoomIndex + 2]; // e.g., 'session-123'
    }
  }
}

const tokenRequest = {
  rep_room_id: repRoomId,
  session_id: sessionId, // CRITICAL: Pass session ID to voice token function
  participant_name: user?.email || 'Anonymous User'
};
```

### 2. Backend Fix (rep-room-voice-token/index.ts)

**Interface Update:**
```typescript
interface LiveKitTokenRequest {
  rep_room_id: string
  session_id?: string // CRITICAL: Accept session ID from frontend
  participant_name?: string
  participant_metadata?: Record<string, any>
}
```

**Session ID and Room Naming Fix:**
```typescript
// Before: Generated own session ID
const sessionId = `session-${user?.id || 'anonymous'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
const roomName = `rep-room-${requestData.rep_room_id}-${participantId}`

// After: Use frontend session ID
const sessionId = requestData.session_id || `session-${user?.id || 'anonymous'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
const roomName = `rep-room-${requestData.rep_room_id}-${sessionId}`
```

## Technical Impact

### Voice Session Correlation
- **Before**: Multiple users accessing `/rroom-v3/t1/session-123` would join different LiveKit rooms
- **After**: All users accessing the same session URL join the same LiveKit room for proper collaboration

### Room Naming Convention
- **Before**: `rep-room-t1-user-abc123-1234567890` (unique per participant)
- **After**: `rep-room-t1-session-1234567890-abc123` (shared per session)

### Session Tracking
- Session IDs are now properly tracked in the `voice_sessions` table
- LiveKit room metadata includes the correct session ID for agent correlation
- Agent dispatch includes session context for proper conversation continuity

## Verification Steps

1. **Access Rep Room V3**: Navigate to `/rroom-v3/t1/session-test123`
2. **Check Session Extraction**: Verify frontend extracts `session-test123`
3. **Voice Token Request**: Confirm session ID is passed to voice token function
4. **LiveKit Room Creation**: Verify room name includes session ID: `rep-room-t1-session-test123`
5. **Multi-User Test**: Multiple users accessing same URL should join same voice room

## Files Modified

1. **src/contexts/rroom/UnifiedVoiceContext.tsx**
   - Added session ID extraction from URL path
   - Updated token request to include session ID

2. **supabase/functions/rep-room-voice-token/index.ts**
   - Added session_id to LiveKitTokenRequest interface
   - Modified session ID generation to use frontend value
   - Updated room naming to use session ID instead of participant ID

## Testing Scenarios

### Scenario 1: Single User Session
- URL: `/rroom-v3/t1/session-abc123`
- Expected Room: `rep-room-t1-session-abc123`
- Result: ✅ User joins correct session-specific room

### Scenario 2: Multi-User Session
- User A: `/rroom-v3/t1/session-abc123`
- User B: `/rroom-v3/t1/session-abc123`
- Expected: Both users join `rep-room-t1-session-abc123`
- Result: ✅ Collaborative voice session established

### Scenario 3: Different Sessions
- User A: `/rroom-v3/t1/session-abc123`
- User B: `/rroom-v3/t1/session-def456`
- Expected: Separate rooms (`rep-room-t1-session-abc123` vs `rep-room-t1-session-def456`)
- Result: ✅ Proper session isolation

## Backward Compatibility

- Legacy `/rroom/` URLs continue to work with existing logic
- New `/rroom-v3/` URLs use enhanced session ID extraction
- Fallback session ID generation maintains functionality for edge cases

## Next Steps

1. **Deploy Edge Function**: Update the `rep-room-voice-token` function in Supabase
2. **Test Voice Integration**: Verify multi-user voice sessions work correctly
3. **Monitor Session Tracking**: Check `voice_sessions` table for proper session correlation
4. **Agent Integration**: Ensure agents receive correct session context

## Success Criteria

- ✅ Session IDs properly extracted from Rep Room V3 URLs
- ✅ Voice token function accepts and uses frontend session IDs
- ✅ LiveKit rooms named with session IDs for proper correlation
- ✅ Multiple users in same session join same voice room
- ✅ Voice sessions tracked with correct session IDs in database
- ✅ Agent dispatch includes proper session context

This fix resolves the critical gap in Rep Room V3 voice integration and enables proper multi-participant voice collaboration within shared sessions.