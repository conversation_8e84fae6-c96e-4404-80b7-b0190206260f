import React from 'react';
import { Clock, Users, ArrowRight, Activity } from 'lucide-react';
import { 
  Agent, 
  AgentOrchestrationState, 
  AgentHandoff, 
  OrchestrationEvent 
} from '../../../types/rep-room';
import { StatusIndicator } from '../StatusIndicator';
import { AgentHandoffIndicator } from './AgentHandoffIndicator';

interface AgentOrchestrationPanelProps {
  orchestrationState: AgentOrchestrationState;
  agents: Agent[];
  className?: string;
  onAgentSelect?: (agentId: string) => void;
}

export const AgentOrchestrationPanel: React.FC<AgentOrchestrationPanelProps> = ({
  orchestrationState,
  agents,
  className = '',
  onAgentSelect
}) => {
  const { 
    activeHandoffs, 
    eventTimeline, 
    coordinationStatus,
    availableSpecialists 
  } = orchestrationState;

  // Get agent by ID helper
  const getAgentById = (id: string) => agents.find(agent => agent.id === id);

  // Format duration helper
  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date();
    const duration = Math.floor((end.getTime() - startTime.getTime()) / 1000);
    if (duration < 60) return `${duration}s`;
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}m ${seconds}s`;
  };

  // Get recent events (last 10)
  const recentEvents = eventTimeline
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10);

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Agent Orchestration</h3>
          </div>
          <div className="flex items-center space-x-2">
            <StatusIndicator 
              status={coordinationStatus === 'idle' ? 'ready' : 'working'} 
              size="sm" 
            />
            <span className="text-sm text-gray-600 capitalize">
              {coordinationStatus.replace('-', ' ')}
            </span>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Active Agents Overview */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <Users className="w-4 h-4 mr-2" />
            Active Agents ({agents.filter(a => a.status !== 'idle').length})
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {agents.map(agent => (
              <div
                key={agent.id}
                className={`p-3 rounded-lg border transition-all cursor-pointer ${
                  agent.status === 'idle' 
                    ? 'border-gray-200 bg-gray-50' 
                    : 'border-blue-200 bg-blue-50 hover:bg-blue-100'
                }`}
                onClick={() => onAgentSelect?.(agent.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {agent.avatar ? (
                      <img 
                        src={agent.avatar} 
                        alt={agent.name}
                        className="w-8 h-8 rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <Users className="w-4 h-4 text-blue-600" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {agent.name}
                      </p>
                      <StatusIndicator status={agent.status} size="sm" />
                    </div>
                    <p className="text-xs text-gray-500 capitalize">
                      {agent.status === 'delegating' ? 'Delegating task...' :
                       agent.status === 'speaking' ? 'Speaking...' :
                       agent.status === 'thinking' ? 'Processing...' :
                       agent.status === 'working' ? 'Working...' :
                       agent.status === 'ready' ? 'Ready' : 'Idle'}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Active Handoffs */}
        {activeHandoffs.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <ArrowRight className="w-4 h-4 mr-2" />
              Active Handoffs ({activeHandoffs.length})
            </h4>
            <div className="space-y-3">
              {activeHandoffs.map(handoff => {
                const fromAgent = getAgentById(handoff.fromAgentId);
                const toAgent = getAgentById(handoff.toAgentId);
                
                return (
                  <div key={handoff.id} className="relative">
                    <AgentHandoffIndicator
                      handoff={handoff}
                      fromAgent={fromAgent}
                      toAgent={toAgent}
                      showDetails={true}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Response Coordination Timeline */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Recent Activity
          </h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {recentEvents.length > 0 ? (
              recentEvents.map(event => {
                const agent = getAgentById(event.agentId);
                const timeAgo = formatDuration(event.timestamp);
                
                return (
                  <div key={event.id} className="flex items-start space-x-3 p-2 rounded-lg bg-gray-50">
                    <div className="flex-shrink-0 mt-1">
                      <div className={`w-2 h-2 rounded-full ${
                        event.type === 'handoff' ? 'bg-purple-500' :
                        event.type === 'delegation' ? 'bg-blue-500' :
                        event.type === 'activation' ? 'bg-green-500' :
                        'bg-gray-500'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">{agent?.name || 'Unknown Agent'}</span>
                        {' '}
                        {event.type === 'handoff' && 'initiated handoff'}
                        {event.type === 'delegation' && 'delegated task'}
                        {event.type === 'activation' && 'was activated'}
                        {event.type === 'completion' && 'completed task'}
                      </p>
                      <p className="text-xs text-gray-500">{timeAgo} ago</p>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                No recent activity
              </p>
            )}
          </div>
        </div>

        {/* Available Specialists Summary */}
        {availableSpecialists.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Available Specialists ({availableSpecialists.length})
            </h4>
            <div className="flex flex-wrap gap-2">
              {availableSpecialists.map(specialist => (
                <span
                  key={specialist.id}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  {specialist.specialization || specialist.name}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};