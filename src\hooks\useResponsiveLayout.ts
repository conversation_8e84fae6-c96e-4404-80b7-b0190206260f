import { useState, useEffect, useCallback } from 'react';

export type DeviceType = 'mobile' | 'tablet' | 'desktop';
export type Orientation = 'portrait' | 'landscape';

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
}

export interface ResponsiveLayoutState {
  deviceType: DeviceType;
  orientation: Orientation;
  screenWidth: number;
  screenHeight: number;
  isTouchDevice: boolean;
  breakpoints: ResponsiveBreakpoints;
}

export interface ResponsiveLayoutActions {
  isDevice: (type: DeviceType) => boolean;
  isMobileOrTablet: () => boolean;
  isPortrait: () => boolean;
  isLandscape: () => boolean;
  getBreakpoint: () => DeviceType;
}

const DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 767,
  tablet: 1199,
  desktop: 1200
};

/**
 * Hook to manage responsive behavior and device detection
 */
export const useResponsiveLayout = (
  customBreakpoints?: Partial<ResponsiveBreakpoints>
): ResponsiveLayoutState & ResponsiveLayoutActions => {
  const breakpoints = { ...DEFAULT_BREAKPOINTS, ...customBreakpoints };

  const getDeviceType = useCallback((width: number): DeviceType => {
    if (width <= breakpoints.mobile) return 'mobile';
    if (width <= breakpoints.tablet) return 'tablet';
    return 'desktop';
  }, [breakpoints]);

  const getOrientation = useCallback((width: number, height: number): Orientation => {
    return width > height ? 'landscape' : 'portrait';
  }, []);

  const detectTouchDevice = useCallback((): boolean => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  const [state, setState] = useState<ResponsiveLayoutState>(() => {
    // Initialize with safe defaults for SSR
    const initialWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const initialHeight = typeof window !== 'undefined' ? window.innerHeight : 800;
    
    return {
      deviceType: getDeviceType(initialWidth),
      orientation: getOrientation(initialWidth, initialHeight),
      screenWidth: initialWidth,
      screenHeight: initialHeight,
      isTouchDevice: typeof window !== 'undefined' ? detectTouchDevice() : false,
      breakpoints
    };
  });

  const updateLayout = useCallback(() => {
    if (typeof window === 'undefined') return;

    const width = window.innerWidth;
    const height = window.innerHeight;
    const deviceType = getDeviceType(width);
    const orientation = getOrientation(width, height);

    setState(prev => ({
      ...prev,
      deviceType,
      orientation,
      screenWidth: width,
      screenHeight: height
    }));
  }, [getDeviceType, getOrientation]);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Update layout on mount
    updateLayout();

    // Debounced resize handler
    let timeoutId: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateLayout, 150);
    };

    // Orientation change handler
    const handleOrientationChange = () => {
      // Small delay to ensure dimensions are updated
      setTimeout(updateLayout, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [updateLayout]);

  // Actions
  const isDevice = useCallback((type: DeviceType): boolean => {
    return state.deviceType === type;
  }, [state.deviceType]);

  const isMobileOrTablet = useCallback((): boolean => {
    return state.deviceType === 'mobile' || state.deviceType === 'tablet';
  }, [state.deviceType]);

  const isPortrait = useCallback((): boolean => {
    return state.orientation === 'portrait';
  }, [state.orientation]);

  const isLandscape = useCallback((): boolean => {
    return state.orientation === 'landscape';
  }, [state.orientation]);

  const getBreakpoint = useCallback((): DeviceType => {
    return state.deviceType;
  }, [state.deviceType]);

  return {
    ...state,
    isDevice,
    isMobileOrTablet,
    isPortrait,
    isLandscape,
    getBreakpoint
  };
};

/**
 * Hook for touch gesture detection
 */
export const useTouchGestures = () => {
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null);

  const minSwipeDistance = 50;

  const onTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    });
  }, []);

  const onTouchMove = useCallback((e: React.TouchEvent) => {
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    });
  }, []);

  const onTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd) return null;

    const distanceX = touchStart.x - touchEnd.x;
    const distanceY = touchStart.y - touchEnd.y;
    const isLeftSwipe = distanceX > minSwipeDistance;
    const isRightSwipe = distanceX < -minSwipeDistance;
    const isUpSwipe = distanceY > minSwipeDistance;
    const isDownSwipe = distanceY < -minSwipeDistance;

    let direction: 'left' | 'right' | 'up' | 'down' | null = null;

    if (Math.abs(distanceX) > Math.abs(distanceY)) {
      // Horizontal swipe
      if (isLeftSwipe) direction = 'left';
      if (isRightSwipe) direction = 'right';
    } else {
      // Vertical swipe
      if (isUpSwipe) direction = 'up';
      if (isDownSwipe) direction = 'down';
    }

    return direction;
  }, [touchStart, touchEnd, minSwipeDistance]);

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    touchStart,
    touchEnd
  };
};

/**
 * Hook for managing responsive panel states
 */
export const useResponsivePanels = () => {
  const [panelStates, setPanelStates] = useState({
    leftPanelCollapsed: false,
    rightPanelCollapsed: false,
    activeTab: 'participants' as 'participants' | 'presentation' | 'conversation'
  });

  const toggleLeftPanel = useCallback(() => {
    setPanelStates(prev => ({
      ...prev,
      leftPanelCollapsed: !prev.leftPanelCollapsed
    }));
  }, []);

  const toggleRightPanel = useCallback(() => {
    setPanelStates(prev => ({
      ...prev,
      rightPanelCollapsed: !prev.rightPanelCollapsed
    }));
  }, []);

  const setActiveTab = useCallback((tab: 'participants' | 'presentation' | 'conversation') => {
    setPanelStates(prev => ({
      ...prev,
      activeTab: tab
    }));
  }, []);

  const collapsePanel = useCallback((panel: 'left' | 'right', collapsed: boolean) => {
    setPanelStates(prev => ({
      ...prev,
      [`${panel}PanelCollapsed`]: collapsed
    }));
  }, []);

  return {
    ...panelStates,
    toggleLeftPanel,
    toggleRightPanel,
    setActiveTab,
    collapsePanel
  };
};