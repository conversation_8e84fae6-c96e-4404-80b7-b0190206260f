import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller, FormProvider } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Save, HelpCircle, AlertCircle, Check } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import SchemaForm from '@/components/schema-form/SchemaForm';
import PresentationConfigForm from '@/components/agent/PresentationConfigForm';
// Update import path to match actual file case
import JSONEditor from '../ui/json-editor';

import { ExtendedAgentType } from '@/hooks/useAgentTypeDetails';
import { Json } from '@/integrations/supabase/types';
import { correctSchemaFieldPlacement } from '@/lib/agent-schema-correction';

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'pending', label: 'Pending Review' },
  { value: 'published', label: 'Published' },
  { value: 'deprecated', label: 'Deprecated' },
  { value: 'archived', label: 'Archived' },
];

const channelOptions = [
  { value: 'Website Widget', label: 'Website Widget' },
  { value: 'RepRoom', label: 'RepRoom' },
  { value: 'Phone', label: 'Phone' },
];

const operationalModeOptions = [
  { value: 'interactive_user_facing', label: 'Interactive User-Facing' },
  { value: 'autonomous_background', label: 'Autonomous Background' },
];

// Comprehensive validation schema for all form fields
const formSchema = z.object({
  // Basic Info Tab
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  version: z.string().min(1, 'Version is required').regex(/^\d+\.\d+\.\d+$/, 'Must be in semantic version format (e.g., 1.0.0)'),
  status: z.enum(['draft', 'pending', 'published', 'deprecated', 'archived']),
  category: z.string().optional(),
  is_public: z.boolean().default(false),
  available_channels: z.array(z.string()).min(1, 'At least one deployment channel must be selected'),
  pricing: z.string().min(1, 'Pricing structure is required').refine(
    (val) => {
      try {
        const parsed = JSON.parse(val);
        return parsed && typeof parsed === 'object' &&
          (typeof parsed.llm_completion_token === 'number' || typeof parsed.llm_prompt_token === 'number');
      } catch {
        return false;
      }
    },
    {
      message: 'Must be valid JSON with at least llm_completion_token or llm_prompt_token defined as numbers',
    }
  ),
  
  // Configuration Tab
  configuration_schema: z.string().min(1, 'Configuration schema is required'),
  runtime_context_schema: z.string().optional(),
  knowledge_source_config_schema: z.string().optional(),
  human_in_the_loop_schema: z.string().optional(),
  
  // Default Configuration Values Tab (formerly Static Overrides)
  default_config_values: z.any().optional(),
  
  // Presentation Tab
  presentation_config: z.any().optional(),
  
  // Advanced Tab
  id: z.string().optional(), // Optional during creation, required and readonly during editing
  mastra_agent_id: z.string().optional(), // External Mastra agent identifier
  mastra_api_base_url: z.string().optional().refine(
    (val) => !val || val === '' || z.string().url().safeParse(val).success,
    { message: 'Must be a valid URL or empty' }
  ),
  capabilities: z.array(z.string()).optional(),
  agent_operational_mode: z.enum(['interactive_user_facing', 'autonomous_background']),
  trigger_events: z.array(z.string()).optional(),
  
  // Legacy fields for backward compatibility
  customizable_parameters: z.any().optional(),
  voice_config: z.any().optional(),
  applicable_metrics: z.any().optional(),
  chat_ui_settings: z.any().optional(),
  phone_settings: z.any().optional(),
  static_overrides: z.any().optional(),
  presentation_overrides: z.any().optional(),
});

export type AgentTypeFormValues = z.infer<typeof formSchema>;

interface AgentTypeFormProps {
  initialData?: ExtendedAgentType;
  onSubmit: (data: AgentTypeFormValues) => Promise<void>;
  isSubmitting?: boolean;
  isCreating?: boolean;
}

// Define types at the module level for reuse
type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
type JsonObject = { [key: string]: JsonValue };
type JsonArray = JsonValue[];

// Type guard function to check if a value is a valid JsonValue
const isJsonValue = (value: unknown): value is JsonValue => {
  if (value === null ||
      typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean') {
    return true;
  }
  
  if (Array.isArray(value)) {
    return value.every(item => isJsonValue(item));
  }
  
  if (typeof value === 'object' && value !== null) {
    return Object.values(value).every(item => isJsonValue(item));
  }
  
  return false;
};

export const AgentTypeForm: React.FC<AgentTypeFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting = false,
  isCreating = false,
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('basic');
  const [formErrors, setFormErrors] = useState<string[]>([]);
  const [validationStatus, setValidationStatus] = useState<Record<string, boolean>>({
    basic: false,
    config: false,
    static: false,
    presentation: false,
    advanced: false
  });

  // Helper to extract default values from schema
  const extractDefaultsFromSchema = (schema: string) => {
    if (!schema) return {};

    try {
      const parsedSchema = JSON.parse(schema);
      
      // Use types defined at module level
      const defaults: Record<string, JsonValue> = {};
      
      // Process object properties to extract defaults
      const processProperties = (properties: Record<string, JsonObject>, parentPath = '') => {
        if (!properties) return;
        
        Object.entries(properties).forEach(([key, prop]) => {
          const path = parentPath ? `${parentPath}.${key}` : key;
          
          if (prop.default !== undefined) {
            defaults[path] = prop.default;
          }
          
          if (prop.properties && typeof prop.properties === 'object' && !Array.isArray(prop.properties)) {
            // Recurse into nested objects, but only when properties is actually an object
            processProperties(prop.properties as Record<string, JsonObject>, path);
          }
        });
      };
      
      if (parsedSchema.properties) {
        processProperties(parsedSchema.properties);
      }
      
      return defaults;
    } catch (e) {
      console.error('Error extracting defaults from schema:', e);
      return {};
    }
  };

  // Debug initial data values for troubleshooting
  React.useEffect(() => {
    if (initialData) {
      console.log('AgentTypeForm initialData:', initialData);
      console.log('Schema fields:', {
        'configuration_schema': initialData.configuration_schema,
        'runtime_context_schema': initialData.runtime_context_schema,
        'knowledge_source_config_schema': initialData.knowledge_source_config_schema,
        'human_in_the_loop_schema': initialData.human_in_the_loop_schema
      });
      console.log('Presentation config:', initialData.presentation_config);
    }
  }, [initialData]);

  // Add effect to reinitialize form if initialData changes after initial render
  // This helps when data loads asynchronously and becomes available later
  React.useEffect(() => {
    if (initialData) {
      console.log('AgentTypeForm initialData updated:', initialData);
      console.log('Reinitializing form with updated data');
      
      // Extract available channels from either top-level or configuration
      const normalizedChannels = (() => {
        // Check top-level available_channels first
        if (Array.isArray(initialData.available_channels)) {
          return initialData.available_channels;
        }
        
        // Then check in configuration (with proper type guard)
        const config = initialData.configuration;
        if (config && typeof config === 'object' && !Array.isArray(config) &&
            'available_channels' in config && Array.isArray(config.available_channels)) {
          return config.available_channels as string[];
        }
        
        // Handle string serialized arrays
        if (typeof initialData.available_channels === 'string') {
          try {
            const parsed = JSON.parse(initialData.available_channels);
            if (Array.isArray(parsed)) {
              return parsed;
            }
          } catch (e) {
            console.error('Error parsing available_channels string:', e);
          }
        }
        
        // Default to empty array
        return [];
      })();
      
      // Reset form with properly typed values, ensuring correct types for all fields
      
      // Apply schema field correction to ensure each schema is in its proper field
      const correctedData = correctSchemaFieldPlacement(initialData);
      
      // Continue with form reset using the corrected data
      form.reset({...correctedData,
        // Basic fields
        id: initialData.id || '',
        name: initialData.name || '',
        description: initialData.description || '',
        version: initialData.version || '',
        status: (initialData.status as 'draft' | 'pending' | 'published' | 'deprecated' | 'archived') || 'draft',
        category: initialData.category || '',
        is_public: !!initialData.is_public,
        
        // Arrays
        available_channels: normalizedChannels,
        capabilities: Array.isArray(initialData.capabilities) ? initialData.capabilities : [],
        trigger_events: Array.isArray(initialData.trigger_events) ? initialData.trigger_events : [],
        
        // Ensure pricing is formatted as a string for the form
        pricing: typeof initialData.pricing === 'string' ?
          initialData.pricing :
          (typeof initialData.pricing === 'object' && initialData.pricing !== null) ?
            JSON.stringify(initialData.pricing, null, 2) :
            JSON.stringify({ llm_completion_token: 0.01, llm_prompt_token: 0.005 }, null, 2),
        
        // Objects - Note: We've removed duplicated entries
        default_config_values: initialData.default_config_values || {},
        customizable_parameters: initialData.customizable_parameters || {},
        voice_config: initialData.voice_config || {},
        applicable_metrics: initialData.applicable_metrics || {},
        chat_ui_settings: initialData.chat_ui_settings || {},
        phone_settings: initialData.phone_settings || {},
        static_overrides: initialData.static_overrides || {},
        presentation_overrides: initialData.presentation_overrides || {},
        
        // URLs
        mastra_api_base_url: initialData.mastra_api_base_url || '',
        
        // Set operational mode or default to interactive
        agent_operational_mode: (initialData.agent_operational_mode as 'interactive_user_facing' | 'autonomous_background') || 'interactive_user_facing',
        // Ensure other key fields are properly initialized too
        configuration_schema: typeof initialData.configuration_schema === 'string' ?
          initialData.configuration_schema :
          JSON.stringify(initialData.configuration_schema || {}, null, 2),
        presentation_config: initialData.presentation_config || {}
      });
    }
  }, [initialData?.id, initialData?.updated_at]); // Only trigger on ID or update timestamp change

  // Set up form with default values or initial data
  const form = useForm<AgentTypeFormValues>({
    resolver: zodResolver(formSchema),
    mode: "onChange", // Validate fields on change
    defaultValues: initialData ? {
      ...initialData,
      // Convert Json types to plain objects where needed
      configuration_schema: (() => {
        console.log('Processing configuration_schema for form:', {
          hasSchema: !!initialData.configuration_schema,
          schemaType: typeof initialData.configuration_schema,
          schemaValue: initialData.configuration_schema
        });
        
        if (!initialData.configuration_schema) {
          console.log('No configuration_schema found, returning empty object');
          return '{}';
        }
        
        if (typeof initialData.configuration_schema === 'string') {
          // Ensure empty strings default to a valid JSON object
          const trimmed = initialData.configuration_schema.trim();
          if (!trimmed) {
            console.log('Empty string configuration_schema, returning empty object');
            return '{}';
          }
          
          // Validate it's actually valid JSON
          try {
            // Parse and re-stringify to ensure consistent formatting
            const parsed = JSON.parse(trimmed);
            console.log('Valid JSON string configuration_schema, pretty-printing');
            return JSON.stringify(parsed, null, 2);
          } catch (e) {
            console.warn('Invalid JSON string in configuration_schema, returning empty object:', e);
            return '{}';
          }
        }
        
        // If it's an object, stringify it properly
        if (typeof initialData.configuration_schema === 'object' && initialData.configuration_schema !== null) {
          console.log('Object configuration_schema, converting to string');
          return JSON.stringify(initialData.configuration_schema, null, 2);
        }
        
        console.warn('Unexpected configuration_schema type, returning empty object');
        return '{}';
      })(),
      runtime_context_schema: (() => {
        if (!initialData.runtime_context_schema) return '';
        if (typeof initialData.runtime_context_schema === 'string') {
          return initialData.runtime_context_schema.trim() || '{}';
        }
        return JSON.stringify(initialData.runtime_context_schema, null, 2);
      })(),
      knowledge_source_config_schema: (() => {
        if (!initialData.knowledge_source_config_schema) return '';
        if (typeof initialData.knowledge_source_config_schema === 'string') {
          return initialData.knowledge_source_config_schema.trim() || '{}';
        }
        return JSON.stringify(initialData.knowledge_source_config_schema, null, 2);
      })(),
      human_in_the_loop_schema: (() => {
        if (!initialData.human_in_the_loop_schema) return '';
        if (typeof initialData.human_in_the_loop_schema === 'string') {
          return initialData.human_in_the_loop_schema.trim() || '{}';
        }
        return JSON.stringify(initialData.human_in_the_loop_schema, null, 2);
      })(),
      pricing: initialData.pricing ?
        (typeof initialData.pricing === 'string' ?
          initialData.pricing :
          JSON.stringify(initialData.pricing, null, 2)) :
        JSON.stringify({
          llm_completion_token: 0.01,
          llm_prompt_token: 0.005
        }, null, 2),
      // Make sure available_channels is always an array and contains the saved values
      // Safely access available_channels, ensuring it's always an array
      available_channels: (() => {
        // Debug logging for available_channels during initialization
        console.log('Initializing available_channels from initialData:', {
          topLevel: initialData?.available_channels,
          topLevelType: typeof initialData?.available_channels,
          configNested: initialData?.configuration && typeof initialData.configuration === 'object' &&
            !Array.isArray(initialData.configuration) ? initialData.configuration.available_channels : undefined
        });
          
        // Check top-level available_channels first
        if (Array.isArray(initialData?.available_channels)) {
          console.log('Using available_channels from top level');
          return initialData.available_channels;
        }
        
        // Then check in configuration with proper type guard
        const config = initialData?.configuration;
        if (config && typeof config === 'object' && !Array.isArray(config) &&
            'available_channels' in config && Array.isArray(config.available_channels)) {
          console.log('Using available_channels from configuration object');
          return config.available_channels as string[];
        }
        
        // Handle if it somehow came in as a string
        if (typeof initialData?.available_channels === 'string') {
          try {
            const parsed = JSON.parse(initialData.available_channels);
            if (Array.isArray(parsed)) {
              console.log('Parsed available_channels from string:', parsed);
              return parsed;
            }
          } catch (e) {
            console.error('Failed to parse available_channels string:', e);
          }
        }
        
        // Log the final result and return empty array as fallback
        console.log('Defaulting to empty array for available_channels');
        return [];
      })(),
      // Make sure default_config_values is properly initialized and never undefined
      default_config_values: initialData.default_config_values !== undefined ? initialData.default_config_values : {},
      // Ensure presentation_config is correctly initialized with priority to presentation_config, then presentation_overrides
      presentation_config: (() => {
        // First try presentation_config
        if (initialData.presentation_config) {
          return initialData.presentation_config === null ? {} : initialData.presentation_config;
        }
        
        // Fall back to presentation_overrides if available
        if (initialData.presentation_overrides) {
          return initialData.presentation_overrides === null ? {} : initialData.presentation_overrides;
        }
        
        // Default to empty object
        return {};
      })(),
      id: initialData.id || '',
      mastra_agent_id: initialData.mastra_agent_id || '',
      mastra_api_base_url: (() => {
        // Extract Mastra API Base URL with more robust fallback handling
        if (initialData.mastra_api_base_url) {
          return initialData.mastra_api_base_url;
        }
        
        // Try to get from configuration if top-level property is not available
        if (initialData.configuration && typeof initialData.configuration === 'object' &&
            !Array.isArray(initialData.configuration)) {
          const config = initialData.configuration as Record<string, unknown>;
          if (typeof config.mastra_api_base_url === 'string') {
            return config.mastra_api_base_url;
          }
        }
        
        return '';
      })(),
      capabilities: initialData.capabilities || [],
      agent_operational_mode: initialData.agent_operational_mode || 'interactive_user_facing',
      trigger_events: initialData.trigger_events || [],
      
      // Legacy fields
      customizable_parameters: initialData.customizable_parameters || {},
      voice_config: initialData.voice_config || {},
      applicable_metrics: initialData.applicable_metrics || {},
      chat_ui_settings: initialData.chat_ui_settings || {},
      phone_settings: initialData.phone_settings || {},
      static_overrides: initialData.static_overrides || {},
      presentation_overrides: initialData.presentation_overrides || {},
    } : {
      name: '',
      description: '',
      version: '1.0.0',
      status: 'draft',
      category: '',
      is_public: false,
      available_channels: [],
      pricing: JSON.stringify({
        llm_completion_token: 0.01,
        llm_prompt_token: 0.005
      }, null, 2),
      configuration_schema: '',
      runtime_context_schema: '',
      knowledge_source_config_schema: '',
      human_in_the_loop_schema: '',
      default_config_values: {},
      presentation_config: {},
      mastra_agent_id: '',
      mastra_api_base_url: '',
      capabilities: [],
      agent_operational_mode: 'interactive_user_facing',
      trigger_events: [],
      
      // Legacy fields
      customizable_parameters: {},
      voice_config: {},
      applicable_metrics: {},
      chat_ui_settings: {},
      phone_settings: {},
      static_overrides: {},
      presentation_overrides: {},
    },
  });
  
  // Watch for form values to validate each tab
  useEffect(() => {
    const subscription = form.watch((formData) => {
      validateTab(formData as AgentTypeFormValues);
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);
  
  // Removed the useEffect that was syncing presentation_config to presentation_overrides
  // Now handled directly in the FormField render function
  
  // Watch for configuration schema changes and apply defaults when schema changes
  useEffect(() => {
    const configSchema = form.watch('configuration_schema');
    
    // Debug logging for schema changes
    console.debug('Configuration schema changed:', configSchema);
    
    if (configSchema && isValidJson(configSchema)) {
      try {
        // Extract defaults from the schema
        const schemaDefaults = extractDefaultsFromSchema(configSchema);
        console.debug('Extracted schema defaults:', schemaDefaults);
        
        // Get current default values and ensure it's an object
        const currentDefaults = form.watch('default_config_values') || {};
        console.debug('Current default values:', currentDefaults);
        
        // If default values are empty or undefined, initialize with schema defaults
        if (!currentDefaults || Object.keys(currentDefaults).length === 0) {
          console.debug('Initializing default_config_values with schema defaults');
          form.setValue('default_config_values', schemaDefaults);
        } else {
          // Otherwise, merge schema defaults with current values (existing values take precedence)
          console.debug('Merging schema defaults with existing values');
          const mergedDefaults = { ...schemaDefaults };
          
          // Apply existing values on top of schema defaults (preserving user configurations)
          Object.entries(currentDefaults).forEach(([key, value]) => {
            if (value !== undefined) {
              // Only assign if value is a valid JsonValue
              if (isJsonValue(value)) {
                mergedDefaults[key] = value; // Now TypeScript knows value is JsonValue
              } else {
                console.warn(`Skipping non-JsonValue value for key ${key}:`, value);
              }
            }
          });
          
          // Update the form with merged values
          form.setValue('default_config_values', mergedDefaults, { shouldDirty: false });
        }
      } catch (error) {
        console.error('Error processing configuration schema defaults:', error);
      }
    }
  }, [form.watch('configuration_schema')]);
  
  // Validate the current tab
  const validateTab = (formData: AgentTypeFormValues) => {
    const newValidationStatus = { ...validationStatus };
    
    // Only validate if we have initial data (for editing) or we're creating
    const shouldValidate = isCreating || !!initialData;
    
    if (!shouldValidate) {
      // If we're editing but don't have data yet, mark all as invalid to keep button disabled
      newValidationStatus.basic = false;
      newValidationStatus.config = false;
      newValidationStatus.advanced = false;
      setValidationStatus(newValidationStatus);
      return;
    }
    
    // Debug logging for validation
    console.log('Validating form data:', {
      name: formData.name,
      version: formData.version,
      status: formData.status,
      available_channels: formData.available_channels,
      pricing: formData.pricing,
      configuration_schema: formData.configuration_schema,
      agent_operational_mode: formData.agent_operational_mode
    });
    
    // Basic Info tab validation
    const hasName = !!(formData.name && formData.name.trim());
    const hasVersion = !!(formData.version && formData.version.trim());
    const hasStatus = !!formData.status;
    const hasChannels = !!(formData.available_channels && Array.isArray(formData.available_channels) && formData.available_channels.length > 0);
    const hasPricing = !!(formData.pricing && formData.pricing.trim());
    const isPricingValid = hasPricing && isValidJson(formData.pricing as string);
    
    console.log('Basic validation checks:', {
      hasName,
      hasVersion,
      hasStatus,
      hasChannels,
      hasPricing,
      isPricingValid
    });
    
    newValidationStatus.basic = hasName && hasVersion && hasStatus && hasChannels && isPricingValid;
    
    // Additional pricing validation if it's valid JSON
    if (isPricingValid) {
      try {
        const pricingObj = JSON.parse(formData.pricing as string);
        // Check for both old and new pricing field formats
        const hasRequiredPricingFields = !!(
          pricingObj.llm_completion_token ||
          pricingObj.llm_prompt_token ||
          pricingObj.credits_per_completion_token ||
          pricingObj.credits_per_prompt_token
        );
        console.log('Pricing object validation:', { pricingObj, hasRequiredPricingFields });
        if (!hasRequiredPricingFields) {
          newValidationStatus.basic = false;
        }
      } catch (e) {
        console.error('Error parsing pricing JSON:', e);
        newValidationStatus.basic = false;
      }
    }
    
    // Configuration tab validation - only require configuration_schema, other fields can be empty
    const hasConfigSchema = !!(formData.configuration_schema && formData.configuration_schema.trim());
    const isConfigSchemaValid = hasConfigSchema && isValidJson(formData.configuration_schema);
    
    console.log('Config validation checks:', {
      hasConfigSchema,
      isConfigSchemaValid
    });
    
    newValidationStatus.config = isConfigSchemaValid;
    
    // Default Configuration Values tab validation - this is always valid, depends on Schema
    newValidationStatus.static = true;
    
    // Presentation tab validation - this is always valid, has its own validation
    newValidationStatus.presentation = true;
    
    // Advanced tab validation - operational_mode is required, but mastra_api_base_url can be empty
    const hasOperationalMode = !!formData.agent_operational_mode;
    
    console.log('Advanced validation checks:', {
      hasOperationalMode
    });
    
    newValidationStatus.advanced = hasOperationalMode;
    
    console.log('Final validation status:', newValidationStatus);
    setValidationStatus(newValidationStatus);
  };
  
  // Helper to validate JSON strings
  const isValidJson = (jsonString: string): boolean => {
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  };
  
  // Helper function to parse form errors and create a structured error object
  const parseFormError = (error: unknown): {
    message: string;
    title?: string;
    fieldErrors?: Record<string, string>;
    tabWithError?: string;
  } => {
    let message = "An unknown error occurred";
    let title: string | undefined = undefined;
    let fieldErrors: Record<string, string> = {};
    
    if (error instanceof Error) {
      message = error.message;
      
      // Check for validation error patterns
      if (error.message.includes('Validation failed')) {
        title = 'Validation Error';
        
        // Try to extract field-specific errors
        const fieldErrorMatches = error.message.match(/(\w+)(?: is required| must be \w+| should \w+|\s+invalid)/g);
        if (fieldErrorMatches) {
          fieldErrorMatches.forEach(match => {
            const fieldName = match.split(' ')[0].toLowerCase();
            fieldErrors[fieldName] = match;
          });
        }
      } else if (error.message.includes('schema validation')) {
        title = 'Schema Validation Error';
      } else if (error.message.includes('network') || error.message.includes('connection')) {
        title = 'Network Error';
        message = 'Failed to connect to the server. Please check your internet connection.';
      } else if (error.message.includes('permission') || error.message.includes('access denied')) {
        title = 'Permission Error';
        message = 'You do not have permission to perform this action.';
      }
    } else if (typeof error === 'object' && error !== null) {
      // Handle object errors
      const errorObj = error as Record<string, unknown>;
      
      if (errorObj.message) {
        message = String(errorObj.message);
      }
      
      if (errorObj.fieldErrors) {
        fieldErrors = errorObj.fieldErrors as Record<string, string>;
      }
      
      if (errorObj.details && Array.isArray(errorObj.details)) {
        message = errorObj.details.join('. ');
      }
    }
    
    return { message, title, fieldErrors };
  };
  
  // Helper to determine which tab contains errors
  const determineTabWithError = (error: { message: string, fieldErrors?: Record<string, string> }): string | null => {
    // Check field-specific errors first
    if (error.fieldErrors) {
      const fieldToTabMap: Record<string, string> = {
        // Basic tab fields
        name: 'basic',
        description: 'basic',
        version: 'basic',
        status: 'basic',
        category: 'basic',
        is_public: 'basic',
        available_channels: 'basic',
        pricing: 'basic',
        
        // Configuration tab fields
        configuration_schema: 'config',
        runtime_context_schema: 'config',
        knowledge_source_config_schema: 'config',
        human_in_the_loop_schema: 'config',
        
        // Default values tab
        default_config_values: 'static',
        
        // Presentation tab
        presentation_config: 'presentation',
        
        // Advanced tab
        id: 'advanced',
        mastra_agent_id: 'advanced',
        mastra_api_base_url: 'advanced',
        capabilities: 'advanced',
        agent_operational_mode: 'advanced',
        trigger_events: 'advanced'
      };
      
      // Check if any of the field errors map to a specific tab
      for (const field of Object.keys(error.fieldErrors)) {
        if (fieldToTabMap[field]) {
          return fieldToTabMap[field];
        }
      }
    }
    
    // Check error message for tab-specific keywords
    const errorMsg = error.message.toLowerCase();
    
    if (errorMsg.includes('mastra api') || errorMsg.includes('operational mode') ||
        errorMsg.includes('capabilities') || errorMsg.includes('trigger events')) {
      return 'advanced';
    } else if (errorMsg.includes('configuration schema') || errorMsg.includes('runtime context') ||
               errorMsg.includes('knowledge source') || errorMsg.includes('human in the loop')) {
      return 'config';
    } else if (errorMsg.includes('presentation') || errorMsg.includes('avatar') ||
               errorMsg.includes('voice') || errorMsg.includes('personality')) {
      return 'presentation';
    } else if (errorMsg.includes('default value') || errorMsg.includes('static override')) {
      return 'static';
    } else if (errorMsg.includes('name') || errorMsg.includes('version') ||
               errorMsg.includes('status') || errorMsg.includes('channel') ||
               errorMsg.includes('pricing') || errorMsg.includes('category')) {
      return 'basic';
    }
    
    // Default to the current tab if we can't determine which tab contains the error
    return null;
  };

  const handleFormSubmit = async (data: AgentTypeFormValues) => {
    console.log('🚀 Form submission started!');
    console.log('Form data received:', data);
    
    try {
      setFormErrors([]);
      
      // Debug log to check all form data before submission
      console.log('Data from AgentTypeForm:', JSON.stringify(data, null, 2));
      console.log('Default config values:', JSON.stringify(data.default_config_values, null, 2));
      
      // Add specific tracing for presentation_config
      console.log('[TRACE-AGENTFORM] Submitting with presentation_config:',
        data.presentation_config ?
          JSON.stringify({
            avatar_type: data.presentation_config.avatar?.type,
            full_avatar: data.presentation_config.avatar
          }, null, 2) :
          'undefined/null'
      );
      
      // Transform JSON strings to objects where needed, but preserve empty values
      // Add extensive logging to trace schema processing
      console.log('Processing form data fields for submission:', {
        config_schema_type: typeof data.configuration_schema,
        config_schema_length: data.configuration_schema?.length,
        has_runtime_context: !!data.runtime_context_schema,
        has_knowledge_source_config: !!data.knowledge_source_config_schema,
        has_human_in_the_loop: !!data.human_in_the_loop_schema
      });
      
      const transformedData = {
        ...data,
        // Process configuration_schema with enhanced validation and logging
        configuration_schema: (() => {
          console.log('Processing configuration_schema for submission...');
          
          if (!data.configuration_schema?.trim()) {
            console.log('Empty configuration_schema, using empty object');
            return {};
          }
          
          if (isValidJson(data.configuration_schema)) {
            try {
              const parsed = JSON.parse(data.configuration_schema);
              console.log('Successfully parsed configuration_schema as JSON object:',
                Object.keys(parsed));
              return parsed;
            } catch (e) {
              console.error('Failed to parse configuration_schema despite validation check:', e);
              return {};
            }
          } else {
            console.warn('Invalid JSON in configuration_schema, preserving as string:',
              data.configuration_schema.substring(0, 50) + '...');
            return data.configuration_schema;
          }
        })(),
        
        // Process other schema fields with similar enhanced handling
        runtime_context_schema: (() => {
          if (!data.runtime_context_schema?.trim()) return null;
          
          if (isValidJson(data.runtime_context_schema)) {
            try {
              return JSON.parse(data.runtime_context_schema);
            } catch (e) {
              console.error('Failed to parse runtime_context_schema:', e);
              return null;
            }
          }
          return data.runtime_context_schema;
        })(),
        
        knowledge_source_config_schema: (() => {
          if (!data.knowledge_source_config_schema?.trim()) return null;
          
          if (isValidJson(data.knowledge_source_config_schema)) {
            try {
              return JSON.parse(data.knowledge_source_config_schema);
            } catch (e) {
              console.error('Failed to parse knowledge_source_config_schema:', e);
              return null;
            }
          }
          return data.knowledge_source_config_schema;
        })(),
        
        human_in_the_loop_schema: (() => {
          if (!data.human_in_the_loop_schema?.trim()) return null;
          
          if (isValidJson(data.human_in_the_loop_schema)) {
            try {
              return JSON.parse(data.human_in_the_loop_schema);
            } catch (e) {
              console.error('Failed to parse human_in_the_loop_schema:', e);
              return null;
            }
          }
          return data.human_in_the_loop_schema;
        })(),
        
        // Process pricing data
        pricing: (() => {
          if (!data.pricing?.trim()) {
            return {
              llm_completion_token: 0.01,
              llm_prompt_token: 0.005
            };
          }
          
          if (isValidJson(data.pricing)) {
            try {
              return JSON.parse(data.pricing);
            } catch (e) {
              console.error('Failed to parse pricing:', e);
              return data.pricing;
            }
          }
          return data.pricing;
        })(),
        
        // Ensure other objects are properly handled
        default_config_values: data.default_config_values !== undefined ? data.default_config_values : {},
        presentation_config: data.presentation_config !== undefined ? data.presentation_config : {},
        capabilities: data.capabilities || [],
        trigger_events: data.trigger_events || []
      };
      
      // Log the final transformed data structure for debugging
      console.log('Final transformed data for submission:', {
        has_config_schema: !!transformedData.configuration_schema,
        config_schema_type: typeof transformedData.configuration_schema,
        config_schema_structure: typeof transformedData.configuration_schema === 'object' ?
          Object.keys(transformedData.configuration_schema) : 'not an object',
        has_default_values: !!transformedData.default_config_values
      });
      
      // Add additional validation before submission - only configuration_schema is required
      if (!transformedData.configuration_schema) {
        throw new Error('Configuration schema is required');
      }
      
      // No requirement for mastra_api_base_url anymore - it can be empty
      
      await onSubmit(transformedData);
      
      toast({
        title: `Agent type ${isCreating ? 'created' : 'updated'} successfully`,
        variant: "default",
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      
      // Extract error message
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      
      // Parse and categorize the error
      const parsedError = parseFormError(error);
      
      // Log structured error information for debugging
      console.error('Structured form error:', parsedError);
      
      // Add the error to the form errors array
      setFormErrors(prev => [
        ...prev,
        parsedError.message
      ]);
      
      // Add any field-specific errors to the form state
      if (parsedError.fieldErrors && Object.keys(parsedError.fieldErrors).length > 0) {
        Object.entries(parsedError.fieldErrors).forEach(([fieldName, errorMessage]) => {
          form.setError(fieldName as keyof AgentTypeFormValues, {
            type: 'manual',
            message: errorMessage
          });
        });
      }
      
      // Determine which tab contains errors and switch to it
      const tabWithError = determineTabWithError(parsedError);
      if (tabWithError && tabWithError !== activeTab) {
        setActiveTab(tabWithError);
      }
      
      toast({
        title: parsedError.title || 'Form Validation Error',
        description: parsedError.message,
        variant: "destructive",
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={(e) => {
        console.log('📝 Form onSubmit triggered!');
        console.log('Form errors:', form.formState.errors);
        console.log('Form is valid:', form.formState.isValid);
        console.log('Form is submitting:', form.formState.isSubmitting);
        
        // Log specific error details
        if (form.formState.errors.mastra_agent_id) {
          console.log('❌ mastra_agent_id error:', form.formState.errors.mastra_agent_id);
        }
        
        return form.handleSubmit(handleFormSubmit)(e);
      }} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 mb-6">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="static" className="whitespace-nowrap">Default Values</TabsTrigger>
            <TabsTrigger value="presentation">Presentation</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          {formErrors.length > 0 && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Form submission failed</AlertTitle>
              <AlertDescription>
                <ul className="list-disc list-inside">
                  {formErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
              <div className="mt-2 text-xs text-muted">
                {activeTab !== 'basic' && (
                  <p>Note: Errors shown above may be related to fields in the current tab.
                  Please fix any highlighted fields before submitting.</p>
                )}
              </div>
            </Alert>
          )}

          {/* Basic Information Tab */}
          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Enter basic details about this agent type</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Name field */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Agent Type Name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description field */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe what this agent type does" 
                          {...field} 
                          value={field.value || ''} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Version field */}
                <FormField
                  control={form.control}
                  name="version"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Version</FormLabel>
                      <FormControl>
                        <Input placeholder="1.0.0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status field */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statusOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Category field */}
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., Sales, Support, etc." 
                          {...field} 
                          value={field.value || ''} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Is Public field */}
                <FormField
                  control={form.control}
                  name="is_public"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Make Public</FormLabel>
                        <CardDescription>
                          If enabled, this agent type will be available to all organizations
                        </CardDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Pricing Card */}
            <Card>
              <CardHeader>
                <CardTitle>Pricing</CardTitle>
                <CardDescription>Set the pricing structure for this agent type</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="pricing"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Pricing Structure
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>Define the pricing structure for different token types and operations.</p>
                                <p className="mt-2">Required fields:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>llm_completion_token: Credits per completion token</li>
                                  <li>llm_prompt_token: Credits per prompt token</li>
                                </ul>
                                <p className="mt-2">Optional fields:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>tool_call: Credits per tool call</li>
                                  <li>embedding_token: Credits per embedding token</li>
                                  <li>storage_gb: Credits per GB storage</li>
                                </ul>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <JSONEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          height="200px"
                          placeholder={`{
  "llm_completion_token": 0.01,
  "llm_prompt_token": 0.005,
  "tool_call": 0.1,
  "embedding_token": 0.0001
}`}
                        />
                      </FormControl>
                      <FormDescription>
                        Define pricing as a JSON object with token types and their credit costs
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Available Deployment Channels Card */}
            <Card>
              <CardHeader>
                <CardTitle>Available Deployment Channels</CardTitle>
                <CardDescription>Select the channels where this agent can be deployed</CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="available_channels"
                  render={() => (
                    <FormItem>
                      <div className="mb-4">
                        <FormLabel>Available Channels</FormLabel>
                        <FormDescription>
                          Select all channels where this agent type can be deployed
                        </FormDescription>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {channelOptions.map((option) => (
                          <FormField
                            key={option.value}
                            control={form.control}
                            name="available_channels"
                            render={({ field }) => {
                              return (
                                <FormItem
                                  key={option.value}
                                  className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                                >
                                  <FormControl>
                                    <Checkbox
                                      checked={(() => {
                                        // Debug logging to trace checkbox state
                                        const isChecked = Array.isArray(field.value) && field.value.includes(option.value);
                                        console.log(`Checkbox ${option.value} checked state:`, isChecked,
                                          'field.value:', field.value);
                                        return isChecked;
                                      })()}
                                      onCheckedChange={(checked) => {
                                        console.log(`Checkbox ${option.value} changed to:`, checked,
                                          'current field.value:', field.value);
                                        
                                        // Ensure field.value is always an array
                                        const currentValue = Array.isArray(field.value) ? field.value : [];
                                        
                                        return checked
                                          ? field.onChange([...currentValue, option.value])
                                          : field.onChange(
                                              currentValue.filter(
                                                (value) => value !== option.value
                                              )
                                            )
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    {option.label}
                                  </FormLabel>
                                </FormItem>
                              )
                            }}
                          />
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Configuration Tab */}
          <TabsContent value="config" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Configuration Schema</CardTitle>
                <CardDescription>
                  Define the JSON Schema that governs the static parameters that can be overridden. This schema will be used to generate the form in the Default Configuration Values tab.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="configuration_schema"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Configuration Schema
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>Define the structure and validation rules for static parameters that can be overridden in Activations/Clones.</p>
                                <p className="mt-2">This schema drives the "Default Configuration Values" tab and override forms.</p>
                                <p className="mt-2">Example schema fields:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>temperature: Controls randomness of output</li>
                                  <li>max_tokens: Maximum length of response</li>
                                  <li>system_prompt: Base instructions for the agent</li>
                                  <li>allowed_tools: List of tools the agent can access</li>
                                </ul>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <JSONEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          height="300px"
                          placeholder={`{
  "type": "object",
  "properties": {
    "temperature": {
      "type": "number",
      "title": "Temperature",
      "description": "Controls randomness of output",
      "default": 0.7,
      "minimum": 0,
      "maximum": 2
    }
  }
}`}
                        />
                      </FormControl>
                      <FormDescription>
                        Define the schema using standard JSON Schema format
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Runtime Context Schema</CardTitle>
                <CardDescription>
                  Define the expected structure of dynamic data the agent will receive with each interaction from the Dreamcrew backend. This includes user, organization, session, and other context data.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="runtime_context_schema"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Runtime Context Schema
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>Define the expected structure and types of dynamic data the agent receives with each interaction.</p>
                                <p className="mt-2">Core fields provided by Dreamcrew:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>user: Current user profile information</li>
                                  <li>organization: Current organization details</li>
                                  <li>session: Session information</li>
                                  <li>static_overrides_ref: Reference to activated configuration</li>
                                </ul>
                                <p className="mt-2">You can define additional expected fields</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <JSONEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          height="250px"
                          placeholder={`{
  "type": "object",
  "properties": {
    "user": {
      "type": "object",
      "description": "Current user data"
    },
    "organization": {
      "type": "object",
      "description": "Current organization data"
    }
  }
}`}
                        />
                      </FormControl>
                      <FormDescription>
                        Define the expected structure for runtime context data
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Knowledge Source Config Schema</CardTitle>
                <CardDescription>
                  Define how knowledge sources (e.g., specific document IDs, tags) are linked or configured for agents using Retrieval-Augmented Generation (RAG). This schema determines how knowledge bases integrate with the agent.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="knowledge_source_config_schema"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Knowledge Source Config Schema
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>Define how knowledge sources are linked for Retrieval-Augmented Generation.</p>
                                <p className="mt-2">Common configuration options:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>Document IDs or collections</li>
                                  <li>Tags for content filtering</li>
                                  <li>Vector search parameters</li>
                                  <li>Relevance thresholds</li>
                                  <li>Content snippet lengths</li>
                                </ul>
                                <p className="mt-2">This schema enables configuring what knowledge the agent can access.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <JSONEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          height="250px"
                          placeholder={`{
  "type": "object",
  "properties": {
    "sources": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": { "type": "string" },
          "type": { "enum": ["document", "database", "api"] }
        }
      }
    }
  }
}`}
                        />
                      </FormControl>
                      <FormDescription>
                        Optional: Define the schema for knowledge source configuration
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Human-in-the-Loop Schema</CardTitle>
                <CardDescription>
                  Define the data structure for Human-in-the-Loop (HITL) tasks when using workflows with suspend capabilities. This schema determines what information is provided to human reviewers and how they can interact with suspended workflows.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="human_in_the_loop_schema"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Human-in-the-Loop Schema
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>Define the data structure for human intervention in agent workflows.</p>
                                <p className="mt-2">Common HITL configuration options:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>Task type (approval, input, review)</li>
                                  <li>Timeout settings</li>
                                  <li>User role requirements</li>
                                  <li>Required input fields</li>
                                  <li>Verification procedures</li>
                                </ul>
                                <p className="mt-2">This schema allows defining when and how humans can intervene in agent operations.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <JSONEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          height="250px"
                          placeholder={`{
  "type": "object",
  "properties": {
    "task_type": {
      "type": "string",
      "enum": ["approval", "input", "review"]
    },
    "timeout_seconds": { "type": "number" }
  }
}`}
                        />
                      </FormControl>
                      <FormDescription>
                        Optional: Define the schema for human-in-the-loop interactions
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Default Configuration Values Tab (renamed from Static Overrides) */}
          <TabsContent value="static" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Default Configuration Values</CardTitle>
                <CardDescription>
                  Set default values for the parameters defined in the Configuration Schema
                </CardDescription>
              </CardHeader>
              <CardContent>
                {form.watch('configuration_schema') ? (
                  <div>
                    {/* Register default_config_values field explicitly */}
                    <FormField
                      control={form.control}
                      name="default_config_values"
                      render={({ field }) => (
                        <>
                          <SchemaForm
                            schema={
                              (() => {
                                try {
                                  // More robust schema parsing with validation and logging
                                  const configSchemaStr = form.watch('configuration_schema');
                                  console.log('Parsing configuration schema for default values:', configSchemaStr);
                                  
                                  if (!configSchemaStr || typeof configSchemaStr !== 'string') {
                                    console.warn('Invalid or missing configuration schema string');
                                    return null;
                                  }
                                  
                                  const parsedSchema = JSON.parse(configSchemaStr);
                                  
                                  // Validate that the schema has properties field
                                  if (!parsedSchema.properties) {
                                    console.warn('Schema missing properties field:', parsedSchema);
                                    // Ensure it has the minimal required structure
                                    return {
                                      type: "object",
                                      properties: {}
                                    };
                                  }
                                  
                                  console.log('Successfully parsed schema with properties:',
                                    Object.keys(parsedSchema.properties));
                                  return parsedSchema;
                                } catch (e) {
                                  console.error('Error parsing configuration schema:', e);
                                  return null;
                                }
                              })()
                            }
                            defaultValues={field.value || {}} // Ensure we at least have an empty object if value is null or undefined
                            onSubmit={(values) => {
                              console.log('SchemaForm submitted values:', values);
                              // Update the form field value
                              field.onChange(values);
                            }}
                            autoSubmit={true} // Auto-submit when any field changes
                            wrapInForm={false}
                          />
                          <div className="mt-4 mb-2 text-sm text-slate-500">
                            <p>Default values applied from schema will be shown above. Any missing values will use schema defaults.</p>
                          </div>
                          <FormItem className="mt-4">
                            <FormLabel>Raw Default Values (JSON)</FormLabel>
                            <FormControl>
                              <JSONEditor
                                value={JSON.stringify(field.value || {}, null, 2)}
                                onChange={(value) => {
                                  try {
                                    field.onChange(JSON.parse(value));
                                  } catch (e) {
                                    // Keep the raw string if it's not valid JSON
                                  }
                                }}
                                height="200px"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        </>
                      )}
                    />
                  </div>
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Configuration Schema Required</AlertTitle>
                    <AlertDescription>
                      Please define a Configuration Schema in the Configuration tab first.
                      This will generate the form fields for setting default values.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Presentation Tab */}
          <TabsContent value="presentation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Presentation Configuration</CardTitle>
                <CardDescription>
                  Configure how the agent appears and sounds to users
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Explicitly register presentation_config with the form */}
                <FormField
                  control={form.control}
                  name="presentation_config"
                  render={({ field }) => (
                    <>
                      <PresentationConfigForm
                        value={field.value || {}}
                        onChange={(value) => {
                          // Debug the received value with explicit type checking
                          console.log('[TRACE-AGENTFORM] PresentationConfigForm onChange:',
                            value === undefined ? 'undefined' :
                            value === null ? 'null' :
                            (value && typeof value === 'object' && 'avatar' in value) ?
                              `Object with avatar type: ${(value as { avatar?: { type?: string } }).avatar?.type || 'undefined'}` :
                              value
                          );
                          
                          // Ensure we're never setting undefined or null
                          const safeValue = (value === undefined || value === null) ? {} : value;
                          
                          // Ensure key sections exist with proper initializations
                          const enhancedValue = { ...(safeValue as Record<string, unknown>) };
                          
                          // Validate key presentation sections exist
                          ['avatar', 'voice', 'branding', 'style', 'personality', 'greetingMedia'].forEach(key => {
                            if (!enhancedValue[key] || typeof enhancedValue[key] !== 'object') {
                              enhancedValue[key] = {};
                            }
                          });
                          
                          // Update both fields for backward compatibility with enhanced safety
                          field.onChange(enhancedValue);
                          form.setValue('presentation_overrides', enhancedValue, { shouldDirty: false });
                        }}
                      />
                    </>
                  )}
                />
                
                {/* Validation status indicator */}
                <div className="mt-4 text-right">
                  {validationStatus.presentation ?
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      <Check className="h-3 w-3 mr-1" /> Valid Configuration
                    </Badge> :
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                      <AlertCircle className="h-3 w-3 mr-1" /> Incomplete Configuration
                    </Badge>
                  }
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Advanced Tab */}
          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Technical Parameters</CardTitle>
                <CardDescription>Configure technical and operational parameters for this agent type</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Agent ID field */}
                <FormField
                  control={form.control}
                  name="id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Agent ID
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>This is the unique identifier for this agent type.</p>
                                <p className="mt-2">In the database, this maps to the 'id' column which serves as both the primary key and the Mastra agent identifier.</p>
                                <p className="mt-2">For Mastra integration, this ID will be used to identify the specific agent type when making API calls.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={isCreating ? "Auto-generated if empty (e.g., seo-expert-agent)" : ""}
                          {...field}
                          value={field.value || ''}
                          readOnly={!isCreating} // Make read-only after creation
                        />
                      </FormControl>
                      <FormDescription>
                        {isCreating
                          ? "Unique identifier for this agent type. Leave blank to auto-generate. Use kebab-case format (e.g., 'seo-expert-agent')."
                          : "Unique identifier for this agent type. Cannot be changed after creation."}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Mastra Agent ID field */}
                <FormField
                  control={form.control}
                  name="mastra_agent_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Mastra Agent ID
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>External Mastra agent identifier for voice system integration.</p>
                                <p className="mt-2">This is a separate field from the Agent ID and is used specifically for integrating with external Mastra voice systems.</p>
                                <p className="mt-2">Examples:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>voice-agent-seo-expert</li>
                                  <li>mastra-sales-assistant-v2</li>
                                  <li>customer-support-bot</li>
                                </ul>
                                <p className="mt-2">This field can be left empty if not using voice integration.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="voice-agent-seo-expert"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        Optional: External identifier for Mastra voice system integration. Can be set later if needed.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Mastra API Base URL field */}
                <FormField
                  control={form.control}
                  name="mastra_api_base_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-2">
                          Mastra API Base URL
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-md">
                                <p>The base URL where your Mastra agent is deployed and accessible.</p>
                                <p className="mt-2">Examples:</p>
                                <ul className="list-disc pl-4 mt-1">
                                  <li>https://seo-expert.mastra.cloud</li>
                                  <li>https://api.yourcompany.com/agents/v1</li>
                                  <li>https://localhost:3000 (for development)</li>
                                </ul>
                                <p className="mt-2">This field can be left empty if the agent is not yet deployed.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://seo-expert.mastra.cloud"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        Optional: Base URL endpoint for the deployed Mastra agent. Can be set later after deployment.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Agent Operational Mode field */}
                <FormField
                  control={form.control}
                  name="agent_operational_mode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Operational Mode</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select operational mode" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {operationalModeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Determines how the agent operates (user-facing or background processing)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Capabilities field - Tag Input */}
                <FormField
                  control={form.control}
                  name="capabilities"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Capabilities</FormLabel>
                      <FormControl>
                        <div className="flex flex-wrap gap-2 p-2 border rounded-md">
                          {field.value?.map((capability, index) => (
                            <Badge key={index} className="bg-primary text-primary-foreground">
                              {capability}
                              <button
                                type="button"
                                className="ml-1 text-primary-foreground hover:text-primary-foreground/80"
                                onClick={() => {
                                  const newCapabilities = [...field.value || []];
                                  newCapabilities.splice(index, 1);
                                  field.onChange(newCapabilities);
                                }}
                              >
                                ×
                              </button>
                            </Badge>
                          ))}
                          <Input
                            className="flex-1 min-w-[120px] border-none shadow-none focus-visible:ring-0"
                            placeholder="Add capability (e.g., rag:query_knowledge_base) and press Enter"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                const target = e.target as HTMLInputElement;
                                if (target.value.trim() !== '') {
                                  const newCapabilities = [...field.value || []];
                                  newCapabilities.push(target.value.trim());
                                  field.onChange(newCapabilities);
                                  target.value = '';
                                }
                              }
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Define the agent's capabilities (e.g., rag:query_knowledge_base)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Trigger Events field */}
                <FormField
                  control={form.control}
                  name="trigger_events"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Trigger Events</FormLabel>
                      <FormControl>
                        <div className="flex flex-wrap gap-2 p-2 border rounded-md">
                          {field.value?.map((event, index) => (
                            <Badge key={index} className="bg-primary text-primary-foreground">
                              {event}
                              <button
                                type="button"
                                className="ml-1 text-primary-foreground hover:text-primary-foreground/80"
                                onClick={() => {
                                  const newEvents = [...field.value || []];
                                  newEvents.splice(index, 1);
                                  field.onChange(newEvents);
                                }}
                              >
                                ×
                              </button>
                            </Badge>
                          ))}
                          <Input
                            className="flex-1 min-w-[120px] border-none shadow-none focus-visible:ring-0"
                            placeholder="Add trigger event and press Enter"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                const target = e.target as HTMLInputElement;
                                if (target.value.trim() !== '') {
                                  const newEvents = [...field.value || []];
                                  newEvents.push(target.value.trim());
                                  field.onChange(newEvents);
                                  target.value = '';
                                }
                              }
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        For autonomous agents, define events that trigger execution
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Form submission section */}
        <div>
          {/* Form validation summary */}
          <Card className="mb-4">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Form Validation Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="flex items-center space-x-2">
                  {validationStatus.basic ?
                    <Check className="h-4 w-4 text-green-500" /> :
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  }
                  <span>Basic Info</span>
                </div>
                <div className="flex items-center space-x-2">
                  {validationStatus.config ?
                    <Check className="h-4 w-4 text-green-500" /> :
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  }
                  <span>Configuration</span>
                </div>
                <div className="flex items-center space-x-2">
                  {validationStatus.static ?
                    <Check className="h-4 w-4 text-green-500" /> :
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  }
                  <span>Default Values</span>
                </div>
                <div className="flex items-center space-x-2">
                  {validationStatus.presentation ?
                    <Check className="h-4 w-4 text-green-500" /> :
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  }
                  <span>Presentation</span>
                </div>
                <div className="flex items-center space-x-2">
                  {validationStatus.advanced ?
                    <Check className="h-4 w-4 text-green-500" /> :
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  }
                  <span>Advanced</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form action buttons */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline">Cancel</Button>
            <Button
              type="submit"
              onClick={() => {
                console.log('🔘 Save Changes button clicked!');
                console.log('Button disabled state:', isSubmitting ||
                  (!initialData && !isCreating) ||
                  (initialData && (
                    !validationStatus.basic ||
                    !validationStatus.config ||
                    !validationStatus.advanced
                  ))
                );
                console.log('Validation status:', validationStatus);
                console.log('isSubmitting:', isSubmitting);
                console.log('initialData exists:', !!initialData);
                console.log('isCreating:', isCreating);
              }}
              disabled={isSubmitting ||
                (!initialData && !isCreating) || // Disable if editing but no data loaded yet
                (initialData && ( // Only apply validation if we have data
                  !validationStatus.basic ||
                  !validationStatus.config ||
                  !validationStatus.advanced
                ))
              }
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              {isCreating ? 'Create Agent Type' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};