import React from 'react';
import { ConnectionState } from '../../hooks/useRepRoomSync';

interface ConnectionStatusBarProps {
  connectionState: ConnectionState;
  participantCount: number;
  lastSyncTimestamp: Date | null;
  reconnectAttempts: number;
  error: string | null;
  onRetry: () => void;
  className?: string;
}

export const ConnectionStatusBar: React.FC<ConnectionStatusBarProps> = ({
  connectionState,
  participantCount,
  lastSyncTimestamp,
  reconnectAttempts,
  error,
  onRetry,
  className = '',
}) => {
  // Get status color based on connection state
  const getStatusColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
      case 'reconnecting':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'disconnected':
      default:
        return 'bg-gray-400';
    }
  };

  // Get status text
  const getStatusText = () => {
    switch (connectionState) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'reconnecting':
        return `Reconnecting... (${reconnectAttempts})`;
      case 'error':
        return 'Connection Error';
      case 'disconnected':
      default:
        return 'Disconnected';
    }
  };

  // Get status icon
  const getStatusIcon = () => {
    switch (connectionState) {
      case 'connected':
        return (
          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'connecting':
      case 'reconnecting':
        return (
          <svg className="w-4 h-4 text-yellow-600 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'disconnected':
      default:
        return (
          <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414L8.586 11l-3.293 3.293a1 1 0 001.414 1.414L10 12.414l3.293 3.293a1 1 0 001.414-1.414L11.414 11l3.293-3.293z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: Date | null) => {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    
    if (diff < 60000) { // Less than 1 minute
      return 'Just now';
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    } else {
      return timestamp.toLocaleTimeString();
    }
  };

  return (
    <div className={`bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between text-sm ${className}`}>
      {/* Left side - Connection status */}
      <div className="flex items-center space-x-3">
        {/* Status indicator */}
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor()} ${connectionState === 'connecting' || connectionState === 'reconnecting' ? 'animate-pulse' : ''}`}></div>
          <div className="flex items-center space-x-1">
            {getStatusIcon()}
            <span className="font-medium text-gray-700">{getStatusText()}</span>
          </div>
        </div>

        {/* Participants count */}
        <div className="flex items-center space-x-1 text-gray-600">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
          </svg>
          <span>{participantCount} online</span>
        </div>
      </div>

      {/* Center - Last sync info */}
      <div className="flex items-center space-x-1 text-gray-500">
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
        </svg>
        <span>Last sync: {formatTimestamp(lastSyncTimestamp)}</span>
      </div>

      {/* Right side - Error message and retry button */}
      <div className="flex items-center space-x-3">
        {/* Error message */}
        {error && (
          <div className="flex items-center space-x-1 text-red-600 max-w-xs">
            <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="truncate text-xs">{error}</span>
          </div>
        )}

        {/* Retry button */}
        {(connectionState === 'error' || connectionState === 'disconnected') && (
          <button
            onClick={onRetry}
            className="flex items-center space-x-1 px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
            <span>Retry</span>
          </button>
        )}

        {/* Connection indicator for mobile */}
        <div className="sm:hidden">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}></div>
        </div>
      </div>
    </div>
  );
};

// Connection status summary component for minimal display
export const ConnectionStatusIndicator: React.FC<{
  connectionState: ConnectionState;
  participantCount: number;
  className?: string;
}> = ({ connectionState, participantCount, className = '' }) => {
  const getStatusColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
      case 'reconnecting':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'disconnected':
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor()} ${connectionState === 'connecting' || connectionState === 'reconnecting' ? 'animate-pulse' : ''}`}></div>
      <span className="text-xs text-gray-600">{participantCount} online</span>
    </div>
  );
};