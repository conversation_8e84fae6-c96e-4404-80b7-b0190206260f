
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import type { ConfigEnv, Plugin, PluginOption, HtmlTagDescriptor } from "vite";
import fs from "fs";

// Enhanced initialization order plugin to fix the "Cannot access 'E' before initialization" error
const initializationOrderPlugin = (): Plugin => {
  return {
    name: 'initialization-order-fix',
    enforce: 'pre',
    // More robust transformation to ensure proper module initialization order
    transform(code, id) {
      // Only apply to JavaScript/TypeScript files
      if (!id.match(/\.[jt]sx?$/)) return null;
      
      // Add a more comprehensive initialization helper at the top of each file
      return {
        code: `
// Lovable.dev initialization order fix - Enhanced version
// This ensures proper module initialization and prevents "Cannot access before initialization" errors
;(function() {
  // Self-executing function to create proper initialization scope
  // This creates a new evaluation context that helps resolve initialization order issues
})();

${code}`,
        map: null
      };
    }
  };
};

// VAD Model and WebAssembly MIME Type Plugin to fix ONNX and WASM file serving
const vadModelMimePlugin = (): Plugin => {
  return {
    name: 'vad-model-mime-fix',
    configureServer(server) {
      // Serve WASM files from node_modules with correct MIME type
      server.middlewares.use('/ort-wasm-simd-threaded.wasm', (req, res, next) => {
        const wasmPath = path.resolve(__dirname, 'node_modules/onnxruntime-web/dist/ort-wasm-simd-threaded.wasm');
        if (fs.existsSync(wasmPath)) {
          res.setHeader('Content-Type', 'application/wasm');
          res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
          res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
          res.setHeader('Access-Control-Allow-Origin', '*');
          fs.createReadStream(wasmPath).pipe(res);
        } else {
          next();
        }
      });

      server.middlewares.use('/ort-wasm-simd.wasm', (req, res, next) => {
        const wasmPath = path.resolve(__dirname, 'node_modules/onnxruntime-web/dist/ort-wasm-simd.wasm');
        if (fs.existsSync(wasmPath)) {
          res.setHeader('Content-Type', 'application/wasm');
          res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
          res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
          res.setHeader('Access-Control-Allow-Origin', '*');
          fs.createReadStream(wasmPath).pipe(res);
        } else {
          next();
        }
      });

      server.middlewares.use('/ort-wasm-threaded.wasm', (req, res, next) => {
        const wasmPath = path.resolve(__dirname, 'node_modules/onnxruntime-web/dist/ort-wasm-threaded.wasm');
        if (fs.existsSync(wasmPath)) {
          res.setHeader('Content-Type', 'application/wasm');
          res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
          res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
          res.setHeader('Access-Control-Allow-Origin', '*');
          fs.createReadStream(wasmPath).pipe(res);
        } else {
          next();
        }
      });

      server.middlewares.use('/ort-wasm.wasm', (req, res, next) => {
        const wasmPath = path.resolve(__dirname, 'node_modules/onnxruntime-web/dist/ort-wasm.wasm');
        if (fs.existsSync(wasmPath)) {
          res.setHeader('Content-Type', 'application/wasm');
          res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
          res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
          res.setHeader('Access-Control-Allow-Origin', '*');
          fs.createReadStream(wasmPath).pipe(res);
        } else {
          next();
        }
      });

      // General middleware for any other WASM/ONNX files
      server.middlewares.use((req, res, next) => {
        if (req.url?.endsWith('.onnx')) {
          res.setHeader('Content-Type', 'application/octet-stream');
          res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
          res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
          res.setHeader('Access-Control-Allow-Origin', '*');
        } else if (req.url?.endsWith('.wasm')) {
          // Critical: WebAssembly files need application/wasm MIME type
          res.setHeader('Content-Type', 'application/wasm');
          res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
          res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
          res.setHeader('Access-Control-Allow-Origin', '*');
        } else if (req.url?.includes('vad.worklet')) {
          res.setHeader('Content-Type', 'application/javascript');
        }
        next();
      });
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv) => {
  const isDevelopment = mode === 'development';
  
  // Create a properly typed custom plugin for environment variables
  const envPlugin: Plugin = {
    name: 'env-plugin',
    transformIndexHtml: {
      enforce: 'pre' as const,
      transform(html: string) {
        // Create a script that exposes environment variables to window.__VITE_ENV__
        // Use Vite's loadEnv to properly load environment variables
        const env = loadEnv(mode, process.cwd(), '');
        
        // Read .env file directly as backup
        const envVars: Record<string, string | undefined> = {};
        try {
          const envPath = path.resolve(process.cwd(), '.env');
          if (fs.existsSync(envPath)) {
            const envContent = fs.readFileSync(envPath, 'utf8');
            const lines = envContent.split('\n');
            
            for (const line of lines) {
              const trimmedLine = line.trim();
              if (trimmedLine && !trimmedLine.startsWith('#') && trimmedLine.includes('=')) {
                const [key, ...valueParts] = trimmedLine.split('=');
                const value = valueParts.join('=').trim();
                if (key.startsWith('VITE_')) {
                  envVars[key] = value;
                }
              }
            }
          }
        } catch (error) {
          console.warn('Could not read .env file:', error);
        }
        
        // Merge with loadEnv results (loadEnv takes precedence)
        Object.entries(env)
          .filter(([key]) => key.startsWith('VITE_'))
          .forEach(([key, value]) => {
            envVars[key] = value;
          });
        
        // Also include process.env VITE_ variables as final fallback
        Object.entries(process.env)
          .filter(([key]) => key.startsWith('VITE_'))
          .forEach(([key, value]) => {
            if (!envVars[key]) {
              envVars[key] = value;
            }
          });
          
        // Return the properly typed transform result
        const tags: HtmlTagDescriptor[] = [
          {
            tag: 'script',
            injectTo: 'head' as const, // Must be one of the allowed values
            children: `window.__VITE_ENV__ = ${JSON.stringify(envVars)};`
          }
        ];
        
        return {
          html,
          tags
        };
      }
    }
  };
  
  // Use local backend server for development
  const apiBaseUrl = 'http://localhost:3002';

  return {
    server: {
      host: "::",
      port: process.env.VITE_PORT ? parseInt(process.env.VITE_PORT) : 8080,
      allowedHosts: [
        'b7cc7c31-a72b-46d2-a487-bf30760363cc.lovableproject.com',
        'b60bdbb2-b1c9-4a77-bc6f-24e19309b6d7.lovableproject.com'
      ],
      // Configure MIME types for VAD model files
      middlewareMode: false,
      fs: {
        strict: false
      },
      proxy: {
        // Proxy voice token requests to the Express server
        '/api/rep-rooms': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/rep-rooms:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying request:', req.method, req.url, '->', apiBaseUrl);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Proxy response:', proxyRes.statusCode, 'for', req.url);
            });
          }
        },
        // Proxy CopilotKit requests to the Express server (which then proxies to Mastra)
        '/api/copilotkit': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/copilotkit:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying CopilotKit request:', req.method, req.url, '->', apiBaseUrl);
            });
          }
        },
        // Proxy TTS requests to the Express server
        '/api/tts': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/tts:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying TTS request:', req.method, req.url, '->', apiBaseUrl);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Proxy response for TTS:', proxyRes.statusCode, 'for', req.url);
            });
          }
        },
        // Proxy voice token requests to the Express server
        '/api/rep-room-voice-token': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/rep-room-voice-token:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying voice token request:', req.method, req.url, '->', apiBaseUrl);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Proxy response for voice token:', proxyRes.statusCode, 'for', req.url);
            });
          }
        },
        // Proxy public rep room config requests to the Express server
        '/api/public-rep-room-config': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/public-rep-room-config:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying public rep room config request:', req.method, req.url, '->', apiBaseUrl);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Proxy response for public rep room config:', proxyRes.statusCode, 'for', req.url);
            });
          }
        },
        // Proxy get-agent-config requests to the Express server
        '/api/get-agent-config': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/get-agent-config:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying get-agent-config request:', req.method, req.url, '->', apiBaseUrl);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Proxy response for get-agent-config:', proxyRes.statusCode, 'for', req.url);
            });
          }
        },
        // Proxy voice token requests to the Express server (UnifiedVoiceContext)
        '/api/voice-token': {
          target: apiBaseUrl,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error for /api/voice-token:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Proxying voice token request:', req.method, req.url, '->', apiBaseUrl);
            });
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Proxy response for voice token:', proxyRes.statusCode, 'for', req.url);
            });
          }
        }
      }
    },
    plugins: [
      // Adding the initialization order fix plugin BEFORE React plugin is crucial
      initializationOrderPlugin(),
      // Add VAD model MIME type fix plugin
      vadModelMimePlugin(),
      react({
        tsDecorators: true,
        jsxImportSource: 'react',
        plugins: [],
      }),
      isDevelopment && componentTagger(),
      // Add our custom env plugin
      envPlugin
    ].filter(Boolean) as PluginOption[],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "react": path.resolve(__dirname, "./node_modules/react"),
      },
    },
    build: {
      outDir: 'dist',
      sourcemap: isDevelopment,
      minify: isDevelopment ? 'esbuild' : 'terser',
      chunkSizeWarningLimit: 1000,
      assetsInlineLimit: 4096,
      rollupOptions: {
        output: {
          // Disable manual chunks to prevent initialization order issues
          // This will create more chunks but will avoid the "Cannot access before initialization" error
          manualChunks: {
            'vendor-react': ['react', 'react-dom', 'react-router-dom'],
            'vendor-ui': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-label', '@radix-ui/react-popover', '@radix-ui/react-select', '@radix-ui/react-slot', '@radix-ui/react-toast'],
            'vendor-supabase': ['@supabase/supabase-js']
          },
          chunkFileNames: 'assets/[name].[hash].js',
          entryFileNames: 'assets/[name].[hash].js',
          assetFileNames: 'assets/[name].[hash][extname]',
          // Add hoisting to improve initialization order
          hoistTransitiveImports: false,
          // Ensure proper execution order
          compact: false,
          // Prevent variable name mangling that can cause initialization issues
          minifyInternalExports: false
        },
        // Explicitly handle circular dependencies
        onwarn(warning, warn) {
          if (warning.code === 'CIRCULAR_DEPENDENCY') {
            // Allowing circular dependencies with cautious monitoring
            return;
          }
          warn(warning);
        }
      },
      // Explicitly setting circular dependency handling
      target: 'es2020', // Changed from es2015 to support BigInt literals
      commonjsOptions: {
        include: [/node_modules/],
        transformMixedEsModules: true
      }
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@supabase/supabase-js',
        'clsx',
        'tailwind-merge',
        'vaul',
        'lucide-react'
      ],
      esbuildOptions: {
        jsx: 'automatic',
        // Make sure to keep these imports in order
        mainFields: ['module', 'main'],
        preserveSymlinks: false
      }
    },
    envPrefix: 'VITE_',
    esbuild: {
      loader: 'tsx',
      include: ['src/**/*.ts', 'src/**/*.tsx'],
      exclude: [
        '**/*.test.ts', 
        '**/*.test.tsx',
        '**/*.d.ts',
        '**/*.stories.tsx'
      ],
      target: ['es2020', 'chrome80', 'firefox78', 'safari14'],
      jsx: 'automatic',
      // Add legalComments to ensure proper handling
      legalComments: 'none'
    }
  };
});
