**Project:** DreamCrew Platform
**Issue Type:** Feature
**Key:** `REP-106-ROOM-SESS-001`
**Title:** Implement Persistent LiveKit Sessions for Rep Rooms v3 (/rroom-v3) with Fly.io Agent Coordination

**Assignee:** Unassigned
**Reporter:** AI Spec Generator
**Priority:** Highest
**Status:** To Do
**Labels:** `rep-room`, `livekit`, `voice`, `ui-ux`, `backend`, `fly-io-agent`, `feature`, `session-management`
**Due Date:** (To be determined)

**Description:**

The current Rep Room voice functionality initiates a LiveKit session only when a user clicks the microphone button. This needs to be enhanced to establish the Rep Room as a persistent (or easily re-creatable) collaborative space linked to an ongoing LiveKit session. Users should join this LiveKit room automatically *upon landing on the Rep Room page* (e.g., via a URL like `/rroom-v3/{slug}/{sessionId}`), allowing them to immediately observe, receive updates, and participate via text even before activating their microphone.

The Python voice agent deployed on Fly.io (e.g., `dreamcrew-voice-agent`) will act as the primary AI presence and coordinator within these LiveKit sessions, configuring itself based on metadata passed during session initiation.

This change aims to create a more immersive and collaborative environment, enabling features like users joining already active sessions, passive observation, and real-time synchronized experiences for all participants.

**Acceptance Criteria:**

1.  **URL-Based Session Access**:
    *   Users can access a specific Rep Room session using a URL format like `/rroom-v3/{slug}/{sessionId}`.
    *   The frontend correctly parses `{slug}` for room configuration and `{sessionId}` to identify the LiveKit session.
2.  **Automatic LiveKit Connection on Page Load**:
    *   On loading `/rroom-v3/{slug}/{sessionId}`, the frontend automatically:
        *   Fetches Rep Room public configuration via `GET /api/public-rep-room-config?slug={slug}`.
        *   Requests a LiveKit token from `POST /api/rep-room-voice-token`, providing `rep_room_slug={slug}` and `session_id_from_url={sessionId}`.
        *   Successfully connects to the LiveKit room specified in the backend's response using the LiveKit Client SDK.
    *   A "Joining session..." loading state is displayed during this process.
3.  **Passive Participation Mode**:
    *   Upon LiveKit connection, the user's microphone is **OFF** by default.
    *   The user can immediately view the participant list, shared presentation panel, and real-time chat.
4.  **Voice Activation**:
    *   A clear "Enable Mic" / "Join Voice" button allows the user to transmit audio.
    *   UI provides distinct visual feedback for microphone states (inactive, connecting, active/listening, muted, error).
5.  **Backend Token Endpoint Update (`POST /api/rep-room-voice-token`)**:
    *   Accepts `rep_room_slug` (required) and `session_id_from_url` (optional) in the request body.
    *   **If `session_id_from_url` is provided:**
        *   The `liveKitRoomName` for the token is consistently derived (e.g., `rrs-{rep_room_slug}-{session_id_from_url}`).
    *   **If `session_id_from_url` is NOT provided (user initiating a new view/session):**
        *   A new `sessionId` is generated by the backend.
        *   The `liveKitRoomName` is derived (e.g., `rrs-{rep_room_slug}-{newSessionId}`).
    *   **Crucially, the `metadata` field within the generated LiveKit token (for the frontend user) must be populated with comprehensive information for the Fly.io Python agent.** This metadata should include:
        *   `rep_room_slug`
        *   `sessionId` (the one being used/generated for this view)
        *   `agent_clone_id` (from the rep room's configuration)
        *   `organization_id` (from the rep room's configuration)
        *   The full agent blueprint/configuration details (e.g., `config.agent` from `/api/public-rep-room-config`) or enough identifiers for the Python agent to fetch/determine its full operational config.
        *   `is_initiator: true` (boolean flag if this token is for the first user joining/creating this `liveKitRoomName` concept, if discernible).
    *   The API response to the frontend includes `{ token, url (LiveKit WS URL), room_name (LiveKit room name), session_id, participant_name, participant_identity }`.
6.  **New Session Initiation Flow (e.g., via a "Start New Session" button on `/rroom-v3/{slug}` or when first user hits `/rroom-v3/{slug}/{newSessionId}`):**
    *   A mechanism (either a new dedicated backend endpoint like `POST /api/create-rep-room-session` or logic within `/api/rep-room-voice-token`) generates a new unique `sessionId`.
    *   The frontend is then directed to `/rroom-v3/{slug}/{newSessionId}`. The subsequent call to `/api/rep-room-voice-token` (as in AC2) will use this new `sessionId`.
    *   The Fly.io Python agent is expected to detect and join this newly conceptualized LiveKit room based on LiveKit server events (e.g., webhooks for `room_started` or `participant_joined`) and the rich `room_metadata` associated with the room/first participant.
7.  **Real-time Updates**:
    *   Participant list, chat messages, and presentation panel content update in real-time for all users in the same LiveKit session, primarily using LiveKit's capabilities (tracks and data channels).
8.  **Error Handling**:
    *   Graceful error handling for invalid session IDs, connection failures, token generation failures, etc., with clear user feedback.

**Technical Tasks:**

**Frontend (e.g., `RepRoomSessionPage.tsx` or similar for `/rroom-v3/{slug}/{sessionId}`):**

1.  **(Task 1.1)** Implement URL parsing for `{slug}` and `{sessionId}`.
2.  **(Task 1.2)** On component mount:
    *   Display a "Joining session..." loading state.
    *   Fetch general Rep Room config via `/api/public-rep-room-config` using `{slug}`.
    *   Fetch LiveKit credentials via `POST /api/rep-room-voice-token` using `{slug}`, `{sessionId}`, and participant details.
3.  **(Task 1.3)** Integrate LiveKit Client SDK:
    *   Instantiate and manage a `Room` object.
    *   Implement `room.connect(url, token)` using credentials from backend.
    *   Handle LiveKit room connection state changes (`RoomEvent.ConnectionStateChanged`) and update UI.
4.  **(Task 1.4)** Implement dynamic participant list:
    *   Subscribe to `RoomEvent.ParticipantConnected`, `RoomEvent.ParticipantDisconnected`, `RoomEvent.ActiveSpeakersChanged`, `RoomEvent.TrackMuted`, `RoomEvent.TrackUnmuted`.
    *   Display participants with real-time status.
5.  **(Task 1.5)** Implement user microphone controls:
    *   Provide "Enable Mic" / "Disable Mic" (or Mute/Unmute) functionality.
    *   Publish/unpublish local audio track to LiveKit accordingly.
6.  **(Task 1.6)** Synchronize chat messages using LiveKit Data Channels:
    *   Publish user's text messages (and client-side STT final transcripts) via reliable data channel.
    *   Subscribe to data channel messages to display incoming chat from other participants (human or AI).
7.  **(Task 1.7)** Synchronize presentation panel updates using LiveKit Data Channels for structured agent-driven UI changes.
8.  **(Task 1.8)** Handle client-side STT if `isMicEnabled`, sending final transcripts as per Task 1.6.
9.  **(Task 1.9)** Handle agent audio:
    *   Subscribe to remote audio tracks from participants identified as AI agents.
    *   Play these tracks.
    *   If agent TTS is provided via `/api/tts` (less ideal in this model but possible), fetch and play audio upon receiving agent text message via data channel.

**Backend API (`api-service/copilotkit-server.js`):**

1.  **(Task 2.1)** Modify `POST /api/rep-room-voice-token`:
    *   Add `session_id_from_url` (string, optional) to the request body schema.
    *   If `session_id_from_url` is provided, derive `liveKitRoomName` (e.g., `rrs-{rep_room_slug}-{session_id_from_url}`).
    *   If `session_id_from_url` is NOT provided, generate a new `sessionId` (e.g., UUID) and derive `liveKitRoomName` (e.g., `rrs-{rep_room_slug}-{newSessionId}`).
    *   Fetch the full rep room configuration (including agent details) using `rep_room_slug`.
    *   In `generateLiveKitToken` function, populate the `metadata` option of `AccessToken` with:
        *   `rep_room_slug: rep_room_slug`
        *   `sessionId: (session_id_from_url || newSessionId)`
        *   `agent_clone_id: fetched_agent_clone_id`
        *   `organization_id: fetched_organization_id`
        *   `agent_blueprint_id: fetched_agent_blueprint_id`
        *   `full_agent_config: { ...fetched_agent_details... }` (or necessary parts for Python agent to self-configure)
        *   `is_initiator: (boolean, true if session_id_from_url was not provided)`
    *   Return the `sessionId` (used or newly generated) in the JSON response to the frontend.
2.  **(Task 2.2)** (Optional, based on final design for new session initiation) Implement `POST /api/create-rep-room-session`:
    *   Accepts `rep_room_slug`.
    *   Generates a new unique `sessionId`.
    *   **No direct call to Fly.io agent from here.** The act of the first user joining via `/api/rep-room-voice-token` with this new `sessionId` will effectively signal the need for the room via its metadata and LiveKit events.
    *   Returns `{ slug: rep_room_slug, sessionId: newSessionId }`.

**Fly.io Python Agent (`server/agent_stable.py`):**

1.  **(Task 3.1)** Agent instances connect to the configured `LIVEKIT_URL`.
2.  **(Task 3.2)** Implement robust logic to detect and join relevant Rep Room sessions:
    *   **Primary Method: LiveKit Webhooks.** Configure LiveKit Cloud to send webhooks (e.g., `room_started`, `participant_joined`) to an endpoint on the Fly.io agent service.
        *   On receiving a webhook for a room with a name matching the Rep Room pattern (e.g., `rrs-*`):
            *   Extract the `room_metadata` from the webhook payload (LiveKit includes this).
            *   Use `agent_clone_id`, `organization_id`, `full_agent_config` from the metadata to configure the agent's "personality," Mastra endpoint, TTS voice, etc., for *this specific room instance*.
            *   Generate its own LiveKit server-side token and join the `liveKitRoomName` from the webhook.
    *   **Fallback/Alternative (If webhooks are problematic/delayed):** Agent instances on Fly.io could, on startup or periodically, query an internal list of "expected rooms" (perhaps managed by `copilotkit-server.js` writing to Supabase) or use a naming convention to join rooms. This is less event-driven.
3.  **(Task 3.3)** Handle multiple room contexts if a single Python agent process is designed to serve multiple LiveKit rooms concurrently. Otherwise, ensure Fly.io scales agent instances appropriately (likely 1 agent process per active LiveKit room).
4.  **(Task 3.4)** Send and receive chat messages and structured UI data via LiveKit Data Channels using the `room_metadata.sessionId` for context if needed.
5.  **(Task 3.5)** Publish its TTS audio output as an audio track to the LiveKit room.
6.  **(Task 3.6)** Consume user audio tracks for STT.

**Out of Scope for this specific issue:**

*   Detailed design and implementation of how the Fly.io Python agent pool is managed or scaled.
*   Specifics of the LiveKit Webhook receiver endpoint on the Fly.io agent if that's the chosen method.
*   Advanced multi-agent handoff logic within the LiveKit room (focus is on getting the primary agent in).

**Design Considerations / Open Questions:**

*   **Definitive method for Fly.io Agent Room Discovery**: Webhooks are preferred. If not feasible, an alternative (e.g., Supabase Realtime subscription by the agent, or a queue) needs to be designed for the Python agent to know which LiveKit rooms to join.
*   **Consistency of `liveKitRoomName` derivation**: Ensure frontend, backend API, and potentially Python agent (if it needs to predict names) use the exact same logic for `liveKitRoomName` based on `slug` and `sessionId`.
*   **Security of `room_metadata`**: Is any information in the token's `metadata` sensitive? It's readable by anyone who can decode the user's JWT. Critical agent configs should be fetched server-side by the Python agent using IDs from metadata, not embedded directly if too sensitive. The current plan embeds `full_agent_config` - review sensitivity.
*   **Race Conditions**: What if multiple users try to "create" a view for the same new `sessionId` almost simultaneously? The backend should handle this gracefully (e.g., first one effectively creates the session context, subsequent ones join). The `sessionId` generation and token issuance should be robust.

**Testing Strategy:**

*   **Unit Tests**:
    *   Frontend: URL parsing, LiveKit connection logic (mocked SDK), token request.
    *   Backend: `POST /api/rep-room-voice-token` with/without `session_id_from_url`, `generateLiveKitToken` (especially metadata population).
    *   Python Agent: Metadata parsing, self-configuration logic.
*   **Integration Tests**:
    *   Frontend -> Backend API (`/api/rep-room-voice-token`).
    *   Backend API -> LiveKit token generation.
    *   Python Agent -> LiveKit connection using metadata from a simulated user token.
*   **E2E Tests**:
    *   Scenario 1 (New Session): User A navigates to `/rroom-v3/{slug}/new` (or similar trigger), gets redirected to `/rroom-v3/{slug}/{sessionId1}`. Verify User A connects, Python Agent joins using metadata from User A's token, interaction is possible.
    *   Scenario 2 (Join Existing Session): User B navigates to `/rroom-v3/{slug}/{sessionId1}`. Verify User B connects to the same LiveKit room, sees User A and Python Agent, and can participate.
    *   Test with anonymous users for public rooms.
    *   Test with authenticated users.

**Impacted Files (Likely):**

*   `src/pages/RepRoomPageUnifiedFixed.tsx` (or new session-aware page `src/pages/RepRoomSessionPage.tsx`)
*   `src/hooks/useLiveKitVoiceEnhanced.ts` (or new hook `useLiveKitSession.ts`)
*   `api-service/copilotkit-server.js` (significant changes to `/api/rep-room-voice-token`, potential new endpoint)
*   `server/agent_stable.py` (logic for room discovery/joining based on webhooks/metadata, context management)
*   `docs/architecture/session_management.md` (New or updated documentation)

