import React from 'react';
import { ConnectionStatus } from '../../hooks/useConnectionState';

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus;
  isOnline: boolean;
  reconnectAttempts?: number;
  maxReconnectAttempts?: number;
  latency?: number | null;
  error?: string | null;
  onReconnect?: () => void;
  className?: string;
  showDetails?: boolean;
}

/**
 * ConnectionStatusIndicator Component - Phase 6: Error Handling & Edge Cases
 * 
 * Features:
 * - Visual connection status indicator
 * - Real-time status updates
 * - Reconnection controls
 * - Latency display
 * - Error messages
 * - Offline detection
 */
export function ConnectionStatusIndicator({
  status,
  isOnline,
  reconnectAttempts = 0,
  maxReconnectAttempts = 5,
  latency,
  error,
  onReconnect,
  className = '',
  showDetails = false
}: ConnectionStatusIndicatorProps) {
  
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          color: 'green',
          icon: '●',
          text: 'Connected',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-200'
        };
      case 'connecting':
        return {
          color: 'yellow',
          icon: '◐',
          text: 'Connecting...',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-200'
        };
      case 'reconnecting':
        return {
          color: 'orange',
          icon: '◑',
          text: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`,
          bgColor: 'bg-orange-100',
          textColor: 'text-orange-800',
          borderColor: 'border-orange-200'
        };
      case 'disconnected':
        return {
          color: 'gray',
          icon: '○',
          text: 'Disconnected',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200'
        };
      case 'failed':
        return {
          color: 'red',
          icon: '●',
          text: 'Connection Failed',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200'
        };
      case 'offline':
        return {
          color: 'red',
          icon: '⚠',
          text: 'Offline',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200'
        };
      default:
        return {
          color: 'gray',
          icon: '?',
          text: 'Unknown',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200'
        };
    }
  };

  const config = getStatusConfig();

  const formatLatency = (latency: number | null) => {
    if (latency === null) return null;
    if (latency < 100) return `${Math.round(latency)}ms`;
    return `${Math.round(latency)}ms`;
  };

  const getLatencyColor = (latency: number | null) => {
    if (latency === null) return 'text-gray-500';
    if (latency < 100) return 'text-green-600';
    if (latency < 300) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!showDetails) {
    // Compact indicator
    return (
      <div
        data-testid="connection-indicator"
        className={`flex items-center space-x-2 ${className}`}
      >
        <div className={`w-3 h-3 rounded-full flex items-center justify-center text-xs ${
          status === 'connecting' || status === 'reconnecting' ? 'animate-pulse' : ''
        }`}>
          <span className={`text-${config.color}-500`}>{config.icon}</span>
        </div>
        <span className={`text-sm ${config.textColor}`}>
          {config.text}
        </span>
        {latency !== null && status === 'connected' && (
          <span className={`text-xs ${getLatencyColor(latency)}`}>
            {formatLatency(latency)}
          </span>
        )}
      </div>
    );
  }

  // Detailed indicator
  return (
    <div
      data-testid="connection-indicator"
      className={`rounded-lg border p-3 ${config.bgColor} ${config.borderColor} ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full flex items-center justify-center text-xs ${
            status === 'connecting' || status === 'reconnecting' ? 'animate-pulse' : ''
          }`}>
            <span className={`text-${config.color}-500`}>{config.icon}</span>
          </div>
          <span className={`font-medium ${config.textColor}`}>
            {config.text}
          </span>
        </div>

        {/* Reconnect button for failed states */}
        {(status === 'failed' || status === 'disconnected') && onReconnect && isOnline && (
          <button
            onClick={onReconnect}
            className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Reconnect
          </button>
        )}
      </div>

      {/* Additional details */}
      <div className="mt-2 space-y-1">
        {/* Latency */}
        {latency !== null && status === 'connected' && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">Latency:</span>
            <span className={getLatencyColor(latency)}>
              {formatLatency(latency)}
            </span>
          </div>
        )}

        {/* Online status */}
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600">Network:</span>
          <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
            {isOnline ? 'Online' : 'Offline'}
          </span>
        </div>

        {/* Reconnection attempts */}
        {status === 'reconnecting' && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">Attempts:</span>
            <span className="text-orange-600">
              {reconnectAttempts}/{maxReconnectAttempts}
            </span>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
            <div className="font-medium text-red-800 mb-1">Error:</div>
            <div className="text-red-700">{error}</div>
          </div>
        )}

        {/* Offline message */}
        {!isOnline && (
          <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
            <div className="text-yellow-800">
              You appear to be offline. Connection will resume when network is restored.
            </div>
          </div>
        )}

        {/* Failed state help */}
        {status === 'failed' && isOnline && (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
            <div className="text-blue-800">
              Connection failed after {maxReconnectAttempts} attempts. 
              {onReconnect && (
                <span> Click "Reconnect" to try again or refresh the page.</span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Preset configurations for common use cases
export function LiveKitConnectionIndicator({
  status,
  isOnline,
  reconnectAttempts,
  maxReconnectAttempts,
  latency,
  error,
  onReconnect,
  className
}: Omit<ConnectionStatusIndicatorProps, 'showDetails'>) {
  return (
    <ConnectionStatusIndicator
      status={status}
      isOnline={isOnline}
      reconnectAttempts={reconnectAttempts}
      maxReconnectAttempts={maxReconnectAttempts}
      latency={latency}
      error={error}
      onReconnect={onReconnect}
      className={className}
      showDetails={false}
    />
  );
}

export function DetailedConnectionIndicator({
  status,
  isOnline,
  reconnectAttempts,
  maxReconnectAttempts,
  latency,
  error,
  onReconnect,
  className
}: Omit<ConnectionStatusIndicatorProps, 'showDetails'>) {
  return (
    <ConnectionStatusIndicator
      status={status}
      isOnline={isOnline}
      reconnectAttempts={reconnectAttempts}
      maxReconnectAttempts={maxReconnectAttempts}
      latency={latency}
      error={error}
      onReconnect={onReconnect}
      className={className}
      showDetails={true}
    />
  );
}

export default ConnectionStatusIndicator;