import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotChat } from '@copilotkit/react-ui';
import { Toaster } from 'sonner';
import { Bot, Zap, Settings, ExternalLink, Plus, FolderOpen } from 'lucide-react';
import { useSupabase } from '../contexts/supabase-context';
import { ChatProvider } from '../contexts/ChatContext';
import { ProjectProvider, useProject } from '../contexts/ProjectContext';
import { NotificationProvider } from '../contexts/NotificationContext';
import { useUserAgents } from '../hooks/useUserAgents';
import { AgentConfig } from '../types/chat';
import { ArtifactsDisplay, Artifact } from '../components/playground/ArtifactsDisplay';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { cn } from '../lib/utils';
// Mock projects data for demonstration
const mockProjects = [
  { id: '1', name: 'Website Redesign', status: 'active' },
  { id: '2', name: 'Mobile App Development', status: 'active' },
  { id: '3', name: 'Marketing Campaign', status: 'active' },
];
// Inner component that can use ProjectProvider context
function PlaygroundContent() {
  const { session } = useSupabase();
  const navigate = useNavigate();
  const { agents } = useUserAgents();
  const { state: projectState, setProject } = useProject();
  // Selected agent state
  const [selectedAgent, setSelectedAgent] = useState<AgentConfig | null>(null);
  // Artifacts state
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  // Set initial agent when agents are loaded
  useEffect(() => {
    if (agents.length > 0 && !selectedAgent) {
      // Transform UserAgent to AgentConfig
      const firstAgent: AgentConfig = {
        id: agents[0].id,
        name: agents[0].name,
        type: agents[0].type as AgentConfig['type'],
        description: agents[0].description,
        capabilities: agents[0].capabilities,
        status: agents[0].status as AgentConfig['status'],
        tools: agents[0].tools,
      };
      setSelectedAgent(firstAgent);
    }
  }, [agents, selectedAgent]);
  const copilotKitConfig = {
    runtimeUrl: 'https://kjkehonxatogcwrybslr.supabase.co/functions/v1/copilotkit-runtime-handler',
    publicApiKey: "disabled", // Disable branding
    headers: {
      'Authorization': `Bearer ${session?.access_token || ''}`,
      'Content-Type': 'application/json',
    },
  };
  // Handle project selection
  const handleProjectSelect = (projectId: string) => {
    if (projectId === 'new') {
      // Navigate to project creation
      navigate('/project-management');
    } else if (projectId === 'manage') {
      // Navigate to project management
      navigate('/project-management');
    } else {
      // For now, just log the selection since we don't have full project management
      console.log('Selected project:', projectId);
    }
  };
  // Handle agent selection
  const handleAgentSelect = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    if (agent) {
      const agentConfig: AgentConfig = {
        id: agent.id,
        name: agent.name,
        type: agent.type as AgentConfig['type'],
        description: agent.description,
        capabilities: agent.capabilities,
        status: agent.status as AgentConfig['status'],
        tools: agent.tools,
      };
      setSelectedAgent(agentConfig);
    }
  };
  // Generate welcome message based on context
  const getWelcomeMessage = () => {
    const currentProject = projectState.currentProject;
    const agentName = selectedAgent ? selectedAgent.name : 'AI assistant';
    if (currentProject) {
      return `Hello! I'm ${agentName}, ready to help you with **${currentProject.name}**.
I can assist you with:
• Creating and managing tasks
• Generating documents and code
• Analyzing project progress
• Coordinating team workflows
All substantial outputs will appear as interactive artifacts on the right. What would you like to work on?`;
    }
    return `Hello! I'm ${agentName}, your AI assistant.
To get started:
• **Select a project** from the dropdown above, or I can help you create a new one
• Ask me to generate documents, code, plans, or analysis
• All substantial outputs will appear as interactive artifacts on the right
What would you like to work on today?`;
  };
  return (
    <CopilotKit {...copilotKitConfig}>
      <div className="h-screen flex flex-col bg-background">
        {/* Clean Header with Project & Agent Selection */}
        <header className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
          <div className="container mx-auto px-6">
            <div className="flex items-center justify-between py-4">
              {/* Left: Title */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Bot className="h-6 w-6 text-primary" />
                  <h1 className="text-xl font-semibold">AI Playground</h1>
                  <Badge variant="secondary" className="gap-1">
                    <Zap className="h-3 w-3" />
                    Powered by AI
                  </Badge>
                </div>
              </div>
              {/* Center: Project Selector */}
              <div className="flex-1 flex justify-center max-w-md">
                <Select
                  value={projectState.currentProject?.id || ''}
                  onValueChange={handleProjectSelect}
                >
                  <SelectTrigger className="w-full">
                    <div className="flex items-center gap-2">
                      <FolderOpen className="h-4 w-4" />
                      <SelectValue placeholder="Select a project..." />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {mockProjects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500" />
                          {project.name}
                        </div>
                      </SelectItem>
                    ))}
                    <SelectItem value="new">
                      <div className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Create New Project
                      </div>
                    </SelectItem>
                    <SelectItem value="manage">
                      <div className="flex items-center gap-2">
                        <ExternalLink className="h-4 w-4" />
                        Manage Projects
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* Right: Agent Selector & Settings */}
              <div className="flex items-center gap-3">
                <Select
                  value={selectedAgent?.id || ''}
                  onValueChange={handleAgentSelect}
                >
                  <SelectTrigger className="w-48">
                    <div className="flex items-center gap-2">
                      <Bot className="h-4 w-4" />
                      <SelectValue placeholder="Select agent..." />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        <div className="flex items-center gap-2">
                          <div className={cn(
                            "w-2 h-2 rounded-full",
                            agent.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                          )} />
                          {agent.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </header>
        {/* Main Content - Two-Panel Layout */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Single Unified Chat Interface */}
          <div className="flex-1 min-w-0 flex flex-col">
            <div className="flex-1 p-4">
              <CopilotChat
                instructions={getWelcomeMessage()}
                labels={{
                  title: selectedAgent?.name || "AI Assistant",
                  initial: getWelcomeMessage(),
                }}
                className="h-full"
                onInProgress={(inProgress) => {
                  // Handle progress state if needed
                }}
                onSubmitMessage={(message) => {
                  // Handle message submission
                  // Check if we should generate an artifact
                  if (shouldGenerateArtifact(message)) {
                    setTimeout(() => generateMockArtifact(message), 1000);
                  }
                }}
              />
            </div>
          </div>
          {/* Right Panel - Enhanced Artifacts Display */}
          <div className="w-96 border-l bg-muted/30">
            <ArtifactsDisplay
              artifacts={artifacts}
              className="h-full"
            />
          </div>
        </div>
        {/* Clean Footer */}
        <footer className="border-t bg-card/80 backdrop-blur supports-[backdrop-filter]:bg-card/80">
          <div className="container mx-auto px-6 py-3">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-4">
                <span>Dreamcrew Platform</span>
                <span>•</span>
                <span>AI-Powered Workspace</span>
                {projectState.currentProject && (
                  <>
                    <span>•</span>
                    <span>Project: {projectState.currentProject.name}</span>
                  </>
                )}
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500" />
                <span>Connected</span>
              </div>
            </div>
          </div>
        </footer>
        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--card))',
              border: '1px solid hsl(var(--border))',
              color: 'hsl(var(--card-foreground))',
            },
          }}
        />
      </div>
    </CopilotKit>
  );
  // Helper functions
  function shouldGenerateArtifact(content: string): boolean {
    const keywords = ['create', 'generate', 'write', 'build', 'design', 'plan', 'analyze', 'code', 'document'];
    return keywords.some(keyword => content.toLowerCase().includes(keyword));
  }
  function generateMockArtifact(content: string) {
    let artifactType: Artifact['type'] = 'document';
    let artifactContent = '';
    let title = '';
    if (content.toLowerCase().includes('code')) {
      artifactType = 'code';
      title = 'Generated Code Sample';
      artifactContent = `// Example React Component
import React from 'react';
export function ProjectCard({ project }) {
  return (
    <div className="project-card">
      <h3>{project.name}</h3>
      <p>{project.description}</p>
      <div className="project-status">
        Status: {project.status}
      </div>
    </div>
  );
}`;
    } else if (content.toLowerCase().includes('plan')) {
      artifactType = 'plan';
      title = 'Project Implementation Plan';
      artifactContent = `# Project Implementation Plan
## Phase 1: Planning & Analysis
- Define project scope and objectives
- Identify key stakeholders and requirements
- Create detailed project timeline
- Risk assessment and mitigation strategies
## Phase 2: Development & Implementation
- Set up development environment
- Implement core features and functionality
- Conduct regular testing and quality assurance
- Iterative development with stakeholder feedback
## Phase 3: Testing & Deployment
- Comprehensive testing (unit, integration, user acceptance)
- Deploy to staging environment for final validation
- Production deployment with monitoring
- Post-deployment support and maintenance
## Success Metrics
- On-time delivery
- Budget adherence
- Quality standards met
- Stakeholder satisfaction`;
    } else {
      title = 'Generated Document';
      artifactContent = `# Project Overview
This document provides a comprehensive overview of your project requirements and recommended next steps.
## Executive Summary
Clear project objectives with well-defined scope and realistic timeline expectations.
## Key Requirements
- **Functional Requirements**: Core features and capabilities needed
- **Technical Requirements**: Technology stack and infrastructure needs
- **Business Requirements**: ROI expectations and success criteria
## Recommended Approach
1. **Discovery Phase**: Gather detailed requirements and constraints
2. **Planning Phase**: Create detailed project plan and resource allocation
3. **Execution Phase**: Implement solution with regular checkpoints
4. **Validation Phase**: Test and validate against requirements
## Next Steps
1. Review and approve this document
2. Finalize project scope and timeline
3. Begin implementation phase
4. Establish regular progress monitoring
## Resource Allocation
- Project timeline: To be determined based on scope
- Team requirements: Based on technical complexity
- Budget considerations: Aligned with business objectives`;
    }
    const newArtifact: Artifact = {
      id: `artifact_${Date.now()}`,
      type: artifactType,
      title,
      content: artifactContent,
      description: `Generated based on your request: "${content.substring(0, 50)}..."`,
      metadata: {
        createdAt: new Date(),
        size: artifactContent.length,
        format: artifactType === 'code' ? 'javascript' : 'markdown',
        language: artifactType === 'code' ? 'javascript' : undefined,
        tags: ['generated', 'ai-created'],
      },
      isNew: true,
    };
    setArtifacts(prev => [newArtifact, ...prev]);
    // Remove the "new" flag after a delay
    setTimeout(() => {
      setArtifacts(prev => prev.map(artifact =>
        artifact.id === newArtifact.id
          ? { ...artifact, isNew: false }
          : artifact
      ));
    }, 3000);
  }
}
// Main component that provides context
export function PlaygroundPageImproved() {
  return (
    <NotificationProvider>
      <ProjectProvider>
        <ChatProvider>
          <PlaygroundContent />
        </ChatProvider>
      </ProjectProvider>
    </NotificationProvider>
  );
}
export default PlaygroundPageImproved;