import React, { useState } from 'react';
import { Bot, User, AlertCircle, RefreshCw } from 'lucide-react';
import { ChatMessage as ChatMessageType } from '../../types/rep-room';
import { ErrorBoundary } from '../common/ErrorBoundary';

interface ChatMessageProps {
  message: ChatMessageType;
  className?: string;
  onRetry?: (messageId: string) => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  className = '',
  onRetry
}) => {
  const [imageError, setImageError] = useState(false);
  const [retrying, setRetrying] = useState(false);
  
  const isAgent = message.type === 'agent';
  const isTyping = message.isTyping;
  const hasError = message.error || message.failed;

  // Typing indicator animation
  const TypingIndicator = () => (
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
    </div>
  );

  // Handle retry action
  const handleRetry = async () => {
    if (!onRetry || !message.id) return;
    
    setRetrying(true);
    try {
      await onRetry(message.id);
    } catch (error) {
      console.error('[ChatMessage] Retry failed:', error);
    } finally {
      setRetrying(false);
    }
  };

  // Safe timestamp formatting
  const formatTimestamp = (timestamp: string | number | Date) => {
    try {
      return new Date(timestamp).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.warn('[ChatMessage] Invalid timestamp:', timestamp);
      return 'Invalid time';
    }
  };

  // Safe content rendering
  const renderContent = () => {
    try {
      if (isTyping) {
        return <TypingIndicator />;
      }
      
      if (hasError) {
        return (
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">
              {message.error || 'Message failed to send'}
            </span>
            {onRetry && (
              <button
                onClick={handleRetry}
                disabled={retrying}
                className="text-xs underline hover:no-underline disabled:opacity-50"
              >
                {retrying ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  'Retry'
                )}
              </button>
            )}
          </div>
        );
      }

      return (
        <div className="whitespace-pre-wrap text-sm leading-relaxed">
          {message.content || 'No content'}
        </div>
      );
    } catch (error) {
      console.error('[ChatMessage] Error rendering content:', error);
      return (
        <div className="text-red-600 text-sm">
          Error displaying message
        </div>
      );
    }
  };

  return (
    <ErrorBoundary
      fallback={
        <div className="flex justify-center mb-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 max-w-md">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">Failed to render message</span>
            </div>
          </div>
        </div>
      }
      onError={(error) => {
        console.error('[ChatMessage] Component error:', error);
      }}
    >
      <div className={`flex ${isAgent ? 'justify-start' : 'justify-end'} mb-4 ${className}`}>
        <div className={`flex max-w-[80%] ${isAgent ? 'flex-row' : 'flex-row-reverse'}`}>
          {/* Avatar */}
          <div className="flex-shrink-0 mx-2">
            {message.avatar && !imageError ? (
              <img
                src={message.avatar}
                alt={message.sender || 'User'}
                className="w-8 h-8 rounded-full"
                onError={() => setImageError(true)}
                onLoad={() => setImageError(false)}
              />
            ) : (
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                isAgent ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                {isAgent ? (
                  <Bot className="w-4 h-4 text-blue-600" />
                ) : (
                  <User className="w-4 h-4 text-gray-600" />
                )}
              </div>
            )}
          </div>

          {/* Message Content */}
          <div className="flex flex-col">
            {/* Sender Name */}
            <div className={`text-xs text-gray-500 mb-1 ${isAgent ? 'text-left' : 'text-right'}`}>
              {message.sender || (isAgent ? 'Agent' : 'User')}
            </div>

            {/* Message Bubble */}
            <div className={`
              px-4 py-2 rounded-lg max-w-full break-words shadow-sm
              ${hasError
                ? 'bg-red-50 border border-red-200 text-red-900'
                : isAgent
                  ? 'bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 rounded-tl-none border border-gray-200'
                  : 'bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-tr-none'
              }
              ${isTyping ? 'animate-pulse' : ''}
              transition-all duration-200 hover:shadow-md
            `}>
              {renderContent()}
            </div>

            {/* Timestamp */}
            <div className={`text-xs text-gray-400 mt-1 ${isAgent ? 'text-left' : 'text-right'}`}>
              {formatTimestamp(message.timestamp)}
              {hasError && (
                <span className="text-red-500 ml-1">• Failed</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};