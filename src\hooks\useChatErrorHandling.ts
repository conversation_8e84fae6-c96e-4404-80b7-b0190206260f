import { useState, useCallback } from 'react';
import { ChatMessage } from '../types/rep-room';
import { withRetry } from '../utils/retryMechanism';

interface ChatErrorHandlingOptions {
  maxRetries?: number;
  onError?: (error: Error, messageId: string) => void;
  onRetrySuccess?: (messageId: string) => void;
  onRetryFailure?: (error: Error, messageId: string) => void;
}

interface ChatErrorState {
  failedMessages: Set<string>;
  retryingMessages: Set<string>;
  errorMessages: Map<string, string>;
}

export const useChatErrorHandling = (options: ChatErrorHandlingOptions = {}) => {
  const {
    maxRetries = 3,
    onError,
    onRetrySuccess,
    onRetryFailure
  } = options;

  const [errorState, setErrorState] = useState<ChatErrorState>({
    failedMessages: new Set(),
    retryingMessages: new Set(),
    errorMessages: new Map()
  });

  // Mark a message as failed
  const markMessageFailed = useCallback((messageId: string, error: string) => {
    setErrorState(prev => {
      const newFailedMessages = new Set(prev.failedMessages);
      newFailedMessages.add(messageId);
      
      const newErrorMessages = new Map(prev.errorMessages);
      newErrorMessages.set(messageId, error);
      
      return {
        ...prev,
        failedMessages: newFailedMessages,
        errorMessages: newErrorMessages
      };
    });
    
    if (onError) {
      onError(new Error(error), messageId);
    }
  }, [onError]);

  // Clear error for a message
  const clearMessageError = useCallback((messageId: string) => {
    setErrorState(prev => {
      const newFailedMessages = new Set(prev.failedMessages);
      const newErrorMessages = new Map(prev.errorMessages);
      newFailedMessages.delete(messageId);
      newErrorMessages.delete(messageId);
      
      return {
        ...prev,
        failedMessages: newFailedMessages,
        errorMessages: newErrorMessages
      };
    });
  }, []);

  // Retry a failed message
  const retryMessage = useCallback(async (
    messageId: string,
    retryFunction: () => Promise<void>
  ) => {
    setErrorState(prev => {
      const newRetryingMessages = new Set(prev.retryingMessages);
      newRetryingMessages.add(messageId);
      return {
        ...prev,
        retryingMessages: newRetryingMessages
      };
    });

    try {
      await withRetry(
        retryFunction,
        {
          maxAttempts: maxRetries,
          initialDelay: 1000,
          onRetry: (error, attempt) => {
            console.log(`[ChatErrorHandling] Retrying message ${messageId} (attempt ${attempt}):`, error.message);
          }
        }
      );

      // Success - clear error state
      clearMessageError(messageId);
      
      if (onRetrySuccess) {
        onRetrySuccess(messageId);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Retry failed';
      markMessageFailed(messageId, errorMessage);
      
      if (onRetryFailure) {
        onRetryFailure(error instanceof Error ? error : new Error(errorMessage), messageId);
      }
    } finally {
      setErrorState(prev => {
        const newRetryingMessages = new Set(prev.retryingMessages);
        newRetryingMessages.delete(messageId);
        return {
          ...prev,
          retryingMessages: newRetryingMessages
        };
      });
    }
  }, [maxRetries, clearMessageError, markMessageFailed, onRetrySuccess, onRetryFailure]);

  // Enhanced message with error state
  const enhanceMessageWithErrorState = useCallback((message: ChatMessage): ChatMessage => {
    const hasError = errorState.failedMessages.has(message.id);
    const isRetrying = errorState.retryingMessages.has(message.id);
    const errorMessage = errorState.errorMessages.get(message.id);

    return {
      ...message,
      failed: hasError,
      error: errorMessage,
      retryCount: (message.retryCount || 0) + (isRetrying ? 1 : 0)
    };
  }, [errorState]);

  // Batch enhance messages
  const enhanceMessages = useCallback((messages: ChatMessage[]): ChatMessage[] => {
    return messages.map(enhanceMessageWithErrorState);
  }, [enhanceMessageWithErrorState]);

  // Safe message sending with error handling
  const sendMessageSafely = useCallback(async (
    messageContent: string,
    sendFunction: (content: string) => Promise<ChatMessage>,
    tempMessageId?: string
  ): Promise<ChatMessage | null> => {
    try {
      const message = await sendFunction(messageContent);
      
      // Clear any previous errors for this message
      if (tempMessageId) {
        clearMessageError(tempMessageId);
      }
      
      return message;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      const messageId = tempMessageId || `temp-${Date.now()}`;
      
      markMessageFailed(messageId, errorMessage);
      
      // Return a failed message object
      return {
        id: messageId,
        sender: 'User',
        content: messageContent,
        timestamp: new Date(),
        type: 'human',
        failed: true,
        error: errorMessage
      };
    }
  }, [clearMessageError, markMessageFailed]);

  // Get error statistics
  const getErrorStats = useCallback(() => {
    return {
      totalFailedMessages: errorState.failedMessages.size,
      currentlyRetrying: errorState.retryingMessages.size,
      hasErrors: errorState.failedMessages.size > 0,
      hasRetrying: errorState.retryingMessages.size > 0
    };
  }, [errorState]);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrorState({
      failedMessages: new Set(),
      retryingMessages: new Set(),
      errorMessages: new Map()
    });
  }, []);

  return {
    // State
    errorState,
    
    // Actions
    markMessageFailed,
    clearMessageError,
    retryMessage,
    sendMessageSafely,
    clearAllErrors,
    
    // Utilities
    enhanceMessageWithErrorState,
    enhanceMessages,
    getErrorStats,
    
    // Computed state
    isMessageFailed: (messageId: string) => errorState.failedMessages.has(messageId),
    isMessageRetrying: (messageId: string) => errorState.retryingMessages.has(messageId),
    getMessageError: (messageId: string) => errorState.errorMessages.get(messageId)
  };
};

export default useChatErrorHandling;