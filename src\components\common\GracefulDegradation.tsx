import React, { useState, useEffect, useCallback } from 'react';
import { AlertTriangle, Wifi, WifiOff, RefreshCw, Settings } from 'lucide-react';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'degraded' | 'unknown';
  lastChecked: Date;
  error?: string;
}

interface GracefulDegradationProps {
  services: {
    name: string;
    healthCheckUrl?: string;
    fallbackComponent?: React.ComponentType;
    isRequired?: boolean;
  }[];
  onServiceStatusChange?: (serviceName: string, status: ServiceStatus) => void;
  showStatusIndicator?: boolean;
  className?: string;
  children: React.ReactNode;
}

interface FallbackModeProps {
  services: ServiceStatus[];
  onRetry: () => void;
  onEnterOfflineMode: () => void;
  isRetrying: boolean;
}

const FallbackMode: React.FC<FallbackModeProps> = ({
  services,
  onRetry,
  onEnterOfflineMode,
  isRetrying
}) => {
  const failedServices = services.filter(s => s.status === 'offline' || s.status === 'degraded');
  const criticalFailures = failedServices.filter(s => s.name.includes('required'));

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-8 h-8 text-yellow-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Service Unavailable
          </h2>
          <p className="text-gray-600">
            Some services are currently unavailable. You can try again or continue in limited mode.
          </p>
        </div>

        {/* Service Status List */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Service Status</h3>
          <div className="space-y-2">
            {services.map((service) => (
              <div key={service.name} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm text-gray-700">{service.name}</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    service.status === 'online' ? 'bg-green-500' :
                    service.status === 'degraded' ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`} />
                  <span className={`text-xs ${
                    service.status === 'online' ? 'text-green-600' :
                    service.status === 'degraded' ? 'text-yellow-600' :
                    'text-red-600'
                  }`}>
                    {service.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Error Details */}
        {criticalFailures.length > 0 && (
          <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded">
            <h4 className="text-sm font-medium text-red-800 mb-2">Critical Issues</h4>
            <ul className="text-xs text-red-700 space-y-1">
              {criticalFailures.map((service) => (
                <li key={service.name}>
                  {service.name}: {service.error || 'Service unavailable'}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={onRetry}
            disabled={isRetrying}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {isRetrying ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            <span>{isRetrying ? 'Retrying...' : 'Try Again'}</span>
          </button>
          
          <button
            onClick={onEnterOfflineMode}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 flex items-center justify-center space-x-2"
          >
            <WifiOff className="w-4 h-4" />
            <span>Continue in Limited Mode</span>
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500 text-center">
          Last checked: {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export const GracefulDegradation: React.FC<GracefulDegradationProps> = ({
  services,
  onServiceStatusChange,
  showStatusIndicator = true,
  className = '',
  children
}) => {
  const [serviceStatuses, setServiceStatuses] = useState<Map<string, ServiceStatus>>(new Map());
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [showFallback, setShowFallback] = useState(false);

  // Health check function
  const checkServiceHealth = useCallback(async (service: typeof services[0]): Promise<ServiceStatus> => {
    const status: ServiceStatus = {
      name: service.name,
      status: 'unknown',
      lastChecked: new Date()
    };

    if (!service.healthCheckUrl) {
      status.status = 'online'; // Assume online if no health check
      return status;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(service.healthCheckUrl, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        status.status = 'online';
      } else if (response.status >= 500) {
        status.status = 'offline';
        status.error = `Server error: ${response.status}`;
      } else {
        status.status = 'degraded';
        status.error = `Service degraded: ${response.status}`;
      }
    } catch (error) {
      status.status = 'offline';
      status.error = error instanceof Error ? error.message : 'Unknown error';
    }

    return status;
  }, []);

  // Check all services
  const checkAllServices = useCallback(async () => {
    const statusPromises = services.map(checkServiceHealth);
    const statuses = await Promise.all(statusPromises);
    
    const statusMap = new Map<string, ServiceStatus>();
    statuses.forEach(status => {
      statusMap.set(status.name, status);
      onServiceStatusChange?.(status.name, status);
    });
    
    setServiceStatuses(statusMap);

    // Determine if we should show fallback
    const requiredServices = services.filter(s => s.isRequired);
    const hasRequiredServiceFailures = requiredServices.some(service => {
      const status = statusMap.get(service.name);
      return status?.status === 'offline';
    });

    setShowFallback(hasRequiredServiceFailures && !isOfflineMode);
  }, [services, checkServiceHealth, onServiceStatusChange, isOfflineMode]);

  // Initial health check
  useEffect(() => {
    checkAllServices();
  }, [checkAllServices]);

  // Periodic health checks
  useEffect(() => {
    const interval = setInterval(checkAllServices, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [checkAllServices]);

  // Retry handler
  const handleRetry = useCallback(async () => {
    setIsRetrying(true);
    await checkAllServices();
    setIsRetrying(false);
  }, [checkAllServices]);

  // Enter offline mode
  const handleEnterOfflineMode = useCallback(() => {
    setIsOfflineMode(true);
    setShowFallback(false);
  }, []);

  // Exit offline mode
  const handleExitOfflineMode = useCallback(() => {
    setIsOfflineMode(false);
    checkAllServices();
  }, [checkAllServices]);

  // Get overall system status
  const getOverallStatus = useCallback(() => {
    const statuses = Array.from(serviceStatuses.values());
    
    if (statuses.length === 0) return 'unknown';
    if (statuses.every(s => s.status === 'online')) return 'online';
    if (statuses.some(s => s.status === 'offline')) return 'degraded';
    if (statuses.some(s => s.status === 'degraded')) return 'degraded';
    
    return 'online';
  }, [serviceStatuses]);

  // Show fallback mode if required services are down
  if (showFallback) {
    return (
      <FallbackMode
        services={Array.from(serviceStatuses.values())}
        onRetry={handleRetry}
        onEnterOfflineMode={handleEnterOfflineMode}
        isRetrying={isRetrying}
      />
    );
  }

  return (
    <div className={`graceful-degradation ${className}`}>
      {/* Status Indicator */}
      {showStatusIndicator && (
        <div className="fixed top-4 left-4 z-50">
          <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg text-sm ${
            isOfflineMode ? 'bg-gray-800 text-white' :
            getOverallStatus() === 'online' ? 'bg-green-100 text-green-800' :
            getOverallStatus() === 'degraded' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {isOfflineMode ? (
              <>
                <WifiOff className="w-4 h-4" />
                <span>Offline Mode</span>
                <button
                  onClick={handleExitOfflineMode}
                  className="ml-2 text-xs underline hover:no-underline"
                >
                  Reconnect
                </button>
              </>
            ) : (
              <>
                <Wifi className="w-4 h-4" />
                <span>
                  {getOverallStatus() === 'online' ? 'All Systems Online' :
                   getOverallStatus() === 'degraded' ? 'Limited Functionality' :
                   'System Issues'}
                </span>
              </>
            )}
          </div>
        </div>
      )}

      {/* Offline Mode Banner */}
      {isOfflineMode && (
        <div className="bg-gray-800 text-white p-3 text-center">
          <div className="flex items-center justify-center space-x-2">
            <WifiOff className="w-4 h-4" />
            <span className="text-sm">
              You're in offline mode. Some features may be limited.
            </span>
            <button
              onClick={handleExitOfflineMode}
              className="text-xs underline hover:no-underline ml-2"
            >
              Try to reconnect
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      {children}
    </div>
  );
};

export default GracefulDegradation;