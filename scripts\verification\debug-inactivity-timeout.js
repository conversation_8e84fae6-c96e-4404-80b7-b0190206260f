#!/usr/bin/env node

/**
 * Debug Inactivity Timeout Script
 * 
 * This script helps debug the voice inactivity timeout by:
 * 1. Checking if the voice agent is running
 * 2. Testing the inactivity manager configuration
 * 3. Providing debug information about the timeout implementation
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '../..');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkVoiceAgentStatus() {
  log('\n🔍 Checking Voice Agent Status...', 'blue');
  
  // Check if voice agent is running on port 3001
  return new Promise((resolve) => {
    import('http').then(({ default: http }) => {
      const req = http.request({
        hostname: 'localhost',
        port: 3001,
        path: '/health',
        method: 'GET',
        timeout: 5000
      }, (res) => {
        if (res.statusCode === 200) {
          log('✅ Voice agent is running on port 3001', 'green');
          resolve(true);
        } else {
          log(`❌ Voice agent responded with status: ${res.statusCode}`, 'red');
          resolve(false);
        }
      });
      
      req.on('error', (err) => {
        log(`❌ Voice agent is not running: ${err.message}`, 'red');
        log('💡 Start the voice agent with: cd api/voice-processing-agent && npm start', 'yellow');
        resolve(false);
      });
      
      req.on('timeout', () => {
        log('❌ Voice agent health check timed out', 'red');
        resolve(false);
      });
      
      req.end();
    });
  });
}

function checkEnvironmentConfig() {
  log('\n📋 Checking Environment Configuration...', 'blue');
  
  const envPath = path.join(projectRoot, 'api/voice-processing-agent/.env');
  
  if (!fs.existsSync(envPath)) {
    log('❌ Voice agent .env file not found', 'red');
    return false;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  const configs = {
    'VOICE_IDLE_TIMEOUT_MINUTES': '1.5',
    'VOICE_INACTIVITY_TIMEOUT_SECONDS': '90'
  };
  
  let allConfigsFound = true;
  
  for (const [key, expectedValue] of Object.entries(configs)) {
    const line = envLines.find(line => line.startsWith(`${key}=`));
    if (line) {
      const value = line.split('=')[1];
      if (value === expectedValue) {
        log(`✅ ${key}=${value}`, 'green');
      } else {
        log(`⚠️ ${key}=${value} (expected: ${expectedValue})`, 'yellow');
      }
    } else {
      log(`❌ ${key} not found in .env`, 'red');
      allConfigsFound = false;
    }
  }
  
  return allConfigsFound;
}

function checkInactivityManagerConfig() {
  log('\n🔧 Checking Inactivity Manager Configuration...', 'blue');
  
  const managerPath = path.join(projectRoot, 'src/services/voiceInactivityManager.ts');
  
  if (!fs.existsSync(managerPath)) {
    log('❌ VoiceInactivityManager not found', 'red');
    return false;
  }
  
  const content = fs.readFileSync(managerPath, 'utf8');
  
  const checks = [
    { pattern: 'timeoutSeconds: 90', description: '90-second timeout' },
    { pattern: 'warningSeconds: 75', description: '75-second warning threshold' },
    { pattern: 'checkIntervalMs: 5000', description: '5-second check interval' },
    { pattern: 'recordSpeechActivity', description: 'Speech activity tracking' },
    { pattern: 'recordInteractionActivity', description: 'Interaction activity tracking' }
  ];
  
  let allChecksPass = true;
  
  for (const check of checks) {
    if (content.includes(check.pattern)) {
      log(`✅ ${check.description}`, 'green');
    } else {
      log(`❌ ${check.description} not found`, 'red');
      allChecksPass = false;
    }
  }
  
  return allChecksPass;
}

function checkVoiceContextIntegration() {
  log('\n🔗 Checking Voice Context Integration...', 'blue');
  
  const contextPath = path.join(projectRoot, 'src/contexts/rroom/UnifiedVoiceContext.tsx');
  
  if (!fs.existsSync(contextPath)) {
    log('❌ UnifiedVoiceContext not found', 'red');
    return false;
  }
  
  const content = fs.readFileSync(contextPath, 'utf8');
  
  const checks = [
    { pattern: 'VoiceInactivityManager', description: 'Inactivity manager import' },
    { pattern: 'inactivityManagerRef', description: 'Inactivity manager ref' },
    { pattern: 'inactivityTimeRemaining', description: 'Time remaining state' },
    { pattern: 'inactivityWarningActive', description: 'Warning active state' },
    { pattern: 'onWarning: () =>', description: 'Warning callback' },
    { pattern: 'onTimeout: async () =>', description: 'Timeout callback' },
    { pattern: 'recordSpeechActivity', description: 'Speech activity integration' },
    { pattern: 'checkIntervalMs: 1000', description: '1-second UI update interval' }
  ];
  
  let allChecksPass = true;
  
  for (const check of checks) {
    if (content.includes(check.pattern)) {
      log(`✅ ${check.description}`, 'green');
    } else {
      log(`❌ ${check.description} not found`, 'red');
      allChecksPass = false;
    }
  }
  
  return allChecksPass;
}

function checkUIComponents() {
  log('\n🎨 Checking UI Components...', 'blue');
  
  const warningPath = path.join(projectRoot, 'src/components/voice/InactivityWarning.tsx');
  const sessionPagePath = path.join(projectRoot, 'src/pages/RepRoomSessionPage.tsx');
  
  let allChecksPass = true;
  
  // Check InactivityWarning component
  if (fs.existsSync(warningPath)) {
    log('✅ InactivityWarning component exists', 'green');
    
    const warningContent = fs.readFileSync(warningPath, 'utf8');
    if (warningContent.includes('inactivityWarningActive')) {
      log('✅ Warning component checks inactivityWarningActive', 'green');
    } else {
      log('❌ Warning component missing inactivityWarningActive check', 'red');
      allChecksPass = false;
    }
  } else {
    log('❌ InactivityWarning component not found', 'red');
    allChecksPass = false;
  }
  
  // Check RepRoomSessionPage integration
  if (fs.existsSync(sessionPagePath)) {
    const pageContent = fs.readFileSync(sessionPagePath, 'utf8');
    
    if (pageContent.includes('import { InactivityWarning }')) {
      log('✅ InactivityWarning imported in RepRoomSessionPage', 'green');
    } else {
      log('❌ InactivityWarning not imported in RepRoomSessionPage', 'red');
      allChecksPass = false;
    }
    
    if (pageContent.includes('<InactivityWarning')) {
      log('✅ InactivityWarning component rendered in RepRoomSessionPage', 'green');
    } else {
      log('❌ InactivityWarning component not rendered in RepRoomSessionPage', 'red');
      allChecksPass = false;
    }
  } else {
    log('❌ RepRoomSessionPage not found', 'red');
    allChecksPass = false;
  }
  
  return allChecksPass;
}

function generateTestInstructions() {
  log('\n📋 Testing Instructions:', 'blue');
  log('1. Open the Rep Room session: http://localhost:8080/rroom-v3/t1/d77rasho4rj', 'cyan');
  log('2. Connect to voice by clicking the microphone button', 'cyan');
  log('3. Wait for 75 seconds without speaking or interacting', 'cyan');
  log('4. You should see a yellow warning appear in the top-right corner', 'cyan');
  log('5. Wait another 15 seconds (90 seconds total) to see the timeout', 'cyan');
  log('6. The session should automatically disconnect', 'cyan');
  
  log('\n🔍 Debug Console Commands:', 'blue');
  log('Open browser console and run these commands to check state:', 'cyan');
  log('- Check inactivity state: window.voiceContext?.state', 'cyan');
  log('- Check remaining time: window.voiceContext?.state?.inactivityTimeRemaining', 'cyan');
  log('- Check warning active: window.voiceContext?.state?.inactivityWarningActive', 'cyan');
  
  log('\n⚡ Quick Test (Shortened Timeout):', 'blue');
  log('To test faster, temporarily modify the timeout in UnifiedVoiceContext.tsx:', 'cyan');
  log('- Change timeoutSeconds: 90 to timeoutSeconds: 10', 'cyan');
  log('- Change warningSeconds: 75 to warningSeconds: 5', 'cyan');
  log('- Save and test with 10-second timeout', 'cyan');
}

async function main() {
  log('🐛 Voice Inactivity Timeout Debug Tool', 'bold');
  log('='.repeat(50), 'blue');
  
  const voiceAgentRunning = await checkVoiceAgentStatus();
  const envConfigOk = checkEnvironmentConfig();
  const managerConfigOk = checkInactivityManagerConfig();
  const contextIntegrationOk = checkVoiceContextIntegration();
  const uiComponentsOk = checkUIComponents();
  
  log('\n' + '='.repeat(50), 'blue');
  
  if (voiceAgentRunning && envConfigOk && managerConfigOk && contextIntegrationOk && uiComponentsOk) {
    log('🎉 All checks passed! Inactivity timeout should be working.', 'green');
    
    if (!voiceAgentRunning) {
      log('\n⚠️ Voice agent needs to be restarted to pick up .env changes:', 'yellow');
      log('cd api/voice-processing-agent && npm start', 'cyan');
    }
    
    generateTestInstructions();
  } else {
    log('❌ Some checks failed. Review the issues above.', 'red');
    
    if (!voiceAgentRunning) {
      log('\n🚀 Start the voice agent:', 'yellow');
      log('cd api/voice-processing-agent && npm start', 'cyan');
    }
  }
  
  process.exit(0);
}

// Run the debug tool
main().catch(console.error);