import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ErrorBoundary } from '../../components/common/ErrorBoundary';

// Mock console methods
const originalConsoleError = console.error;
const originalConsoleLog = console.log;
const mockConsoleError = jest.fn();
const mockConsoleLog = jest.fn();

// Mock window methods
const mockReload = jest.fn();
const mockLocationAssign = jest.fn();

// Test component that throws errors on demand
interface ThrowErrorProps {
  shouldThrow?: boolean;
  errorMessage?: string;
}

const ThrowError: React.FC<ThrowErrorProps> = ({ 
  shouldThrow = false, 
  errorMessage = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div data-testid="child-component">Child component rendered successfully</div>;
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    // Mock console methods
    console.error = mockConsoleError;
    console.log = mockConsoleLog;
    
    // Mock window methods
    Object.defineProperty(window, 'location', {
      value: {
        reload: mockReload,
        href: '',
      },
      writable: true,
    });
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original console methods
    console.error = originalConsoleError;
    console.log = originalConsoleLog;
  });

  describe('Normal Rendering', () => {
    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should render multiple children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
        </ErrorBoundary>
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
    });
  });

  describe('Error Catching and Fallback UI', () => {
    it('should catch errors and display default fallback UI', () => {
      // Suppress React error boundary warnings in test output
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Test error message" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText(/We encountered an unexpected error/)).toBeInTheDocument();
      expect(screen.queryByTestId('child-component')).not.toBeInTheDocument();
      expect(screen.queryByTestId('error-boundary')).not.toBeInTheDocument();

      spy.mockRestore();
    });

    it('should display custom fallback UI when provided', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const customFallback = <div data-testid="custom-fallback">Custom error message</div>;

      render(
        <ErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom error message')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();

      spy.mockRestore();
    });

    it('should generate unique error ID for each error', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      // First error boundary instance
      const { unmount } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      const firstErrorId = screen.getByText(/Error ID:/).textContent;
      unmount();

      // Second error boundary instance with different error
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Different error" />
        </ErrorBoundary>
      );

      const secondErrorId = screen.getByText(/Error ID:/).textContent;
      expect(firstErrorId).not.toEqual(secondErrorId);

      spy.mockRestore();
    });
  });

  describe('Error Logging and Reporting', () => {
    it('should log error details to console', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Console test error" />
        </ErrorBoundary>
      );

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[ErrorBoundary] Caught an error:',
        expect.any(Error)
      );
      expect(mockConsoleError).toHaveBeenCalledWith(
        '[ErrorBoundary] Error info:',
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );

      spy.mockRestore();
    });

    it('should call custom error handler when provided', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const mockOnError = jest.fn();

      render(
        <ErrorBoundary onError={mockOnError}>
          <ThrowError shouldThrow={true} errorMessage="Custom handler test" />
        </ErrorBoundary>
      );

      expect(mockOnError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );

      spy.mockRestore();
    });

    it('should report error with detailed information', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Report test error" />
        </ErrorBoundary>
      );

      expect(mockConsoleLog).toHaveBeenCalledWith(
        '[ErrorBoundary] Error report:',
        expect.objectContaining({
          message: 'Report test error',
          stack: expect.any(String),
          componentStack: expect.any(String),
          timestamp: expect.any(String),
          userAgent: expect.any(String),
          url: expect.any(String),
          errorId: expect.any(String)
        })
      );

      spy.mockRestore();
    });
  });

  describe('Error Recovery Actions', () => {
    it('should reset error state when retry button is clicked', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      const retryButton = screen.getByText('Try Again');
      
      // Verify the retry button calls the reset function
      // The actual recovery would happen when the component is re-rendered with non-throwing children
      fireEvent.click(retryButton);

      // After clicking retry, the error boundary should still show error state
      // because the children haven't changed - this is the correct behavior
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      spy.mockRestore();
    });

    it('should reload page when reload button is clicked', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      const reloadButton = screen.getByText('Reload Page');
      fireEvent.click(reloadButton);

      expect(mockReload).toHaveBeenCalled();

      spy.mockRestore();
    });

    it('should navigate to home when go home button is clicked', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      const goHomeButton = screen.getByText('Go Home');
      fireEvent.click(goHomeButton);

      expect(window.location.href).toBe('/');

      spy.mockRestore();
    });
  });

  describe('Reset Mechanisms', () => {
    it('should reset error state when resetKeys change', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { rerender } = render(
        <ErrorBoundary resetKeys={['key1']}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Change resetKeys to trigger reset
      rerender(
        <ErrorBoundary resetKeys={['key2']}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();

      spy.mockRestore();
    });

    it('should not reset when resetKeys remain the same', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { rerender } = render(
        <ErrorBoundary resetKeys={['key1']}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Keep same resetKeys - should not reset
      rerender(
        <ErrorBoundary resetKeys={['key1']}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.queryByTestId('child-component')).not.toBeInTheDocument();

      spy.mockRestore();
    });

    it('should reset error state when resetOnPropsChange is true and props change', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { rerender } = render(
        <ErrorBoundary resetOnPropsChange={true} fallback={<div>Error 1</div>}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Error 1')).toBeInTheDocument();

      // Change props to trigger reset
      rerender(
        <ErrorBoundary resetOnPropsChange={true} fallback={<div>Error 2</div>}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('child-component')).toBeInTheDocument();
      expect(screen.queryByText('Error 1')).not.toBeInTheDocument();

      spy.mockRestore();
    });
  });

  describe('Development vs Production Mode', () => {
    const originalNodeEnv = process.env.NODE_ENV;

    afterEach(() => {
      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should show error details in development mode', () => {
      process.env.NODE_ENV = 'development';
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Dev mode error" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Error Details (Development)')).toBeInTheDocument();
      expect(screen.getByText(/Error: Dev mode error/)).toBeInTheDocument();

      spy.mockRestore();
    });

    it('should hide error details in production mode', () => {
      process.env.NODE_ENV = 'production';
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Prod mode error" />
        </ErrorBoundary>
      );

      expect(screen.queryByText('Error Details (Development)')).not.toBeInTheDocument();
      expect(screen.queryByText(/Error: Prod mode error/)).not.toBeInTheDocument();

      spy.mockRestore();
    });
  });

  describe('Component Lifecycle and Cleanup', () => {
    it('should clear timeout on component unmount', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const clearTimeoutSpy = jest.spyOn(window, 'clearTimeout');

      const { unmount } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      unmount();

      // Note: clearTimeout might not be called if no timeout was set
      // This test ensures the cleanup logic exists
      expect(clearTimeoutSpy).toHaveBeenCalledTimes(0); // No timeout was set in this case

      spy.mockRestore();
      clearTimeoutSpy.mockRestore();
    });

    it('should maintain error state across re-renders until reset', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Re-render with same props - error state should persist
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.queryByTestId('child-component')).not.toBeInTheDocument();

      spy.mockRestore();
    });
  });

  describe('Edge Cases', () => {
    it('should handle errors with no message', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const ThrowEmptyError = () => {
        throw new Error();
      };

      render(
        <ErrorBoundary>
          <ThrowEmptyError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      spy.mockRestore();
    });

    it('should handle errors with no stack trace', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const ThrowNoStackError = () => {
        const error = new Error('No stack error');
        error.stack = undefined;
        throw error;
      };

      render(
        <ErrorBoundary>
          <ThrowNoStackError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      spy.mockRestore();
    });

    it('should handle multiple consecutive errors', () => {
      const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="First error" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Reset and throw another error
      fireEvent.click(screen.getByText('Try Again'));

      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Second error" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      spy.mockRestore();
    });
  });
});