# Agent Types Complete Guide

A comprehensive guide for junior developers about agent types (agent blueprints) in the multi-tenant AI platform.

## Table of Contents

1. [What Are Agent Types?](#what-are-agent-types)
2. [How Agent Types Work Technically](#how-agent-types-work-technically)
3. [Agent Types vs Agent Clones vs Activations](#agent-types-vs-agent-clones-vs-activations)
4. [Database Schema Overview](#database-schema-overview)
5. [Configuration Structure Deep Dive](#configuration-structure-deep-dive)
6. [Creating Agent Types](#creating-agent-types)
7. [Managing Agent Types](#managing-agent-types)
8. [JSON Schema Examples](#json-schema-examples)
9. [Best Practices](#best-practices)
10. [Common Patterns](#common-patterns)
11. [Troubleshooting](#troubleshooting)

---

## What Are Agent Types?

Agent types (also called agent blueprints) are **master templates** that define the structure, behavior, and capabilities of AI agents in our multi-tenant platform. Think of them as "classes" in object-oriented programming - they define what an agent can do, but they're not the actual running instances.

### Key Concepts

- **Agent Type**: The blueprint/template (stored in [`agents`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1262) table)
- **Tenant Activation**: When a tenant "activates" an agent type for use (stored in [`tenant_agent_activations`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1674) table)
- **User Clone**: A user's personalized instance of an activated agent (stored in [`user_agent_clones`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1697) table)
- **Rep Room**: A public-facing interface for an agent clone (stored in [`rep_rooms`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1640) table)

### Purpose and Benefits

1. **Standardization**: Ensures consistent agent behavior across the platform
2. **Reusability**: One agent type can be used by multiple tenants
3. **Governance**: House admins control what agents are available
4. **Customization**: Tenants and users can customize within defined parameters
5. **Scalability**: Easy to deploy new agent capabilities platform-wide

---

## How Agent Types Work Technically

### Architecture Overview

```mermaid
graph TD
    A[House Admin] -->|Creates| B[Agent Type]
    B -->|Published| C[Available to Organizations]
    C -->|Agency Exposes| D[Available to Tenants]
    D -->|Tenant Activates| E[Tenant Activation]
    E -->|User Clones| F[User Agent Clone]
    F -->|Public Interface| G[Rep Room]
    
    B -.->|Configuration Schema| H[Defines Customization Rules]
    E -.->|Tenant Config| I[Tenant-level Overrides]
    F -.->|User Config| J[User-level Personalization]
```

### Multi-Tenant Hierarchy

The platform follows a strict organizational hierarchy:

1. **House** (Platform Owner - Mylove)
   - Creates and manages agent types
   - Controls global platform settings

2. **Super Agency** 
   - Can expose House-created agents to child agencies

3. **Agency**
   - Can expose agents to child tenants
   - Manages tenant relationships

4. **Tenant** (End Customer)
   - Activates exposed agents for their organization
   - Users within tenant can create personal clones

### Status Lifecycle

Agent types follow this status progression:

```
draft → pending → published → deprecated → archived
```

- **draft**: Being developed, not visible to organizations
- **pending**: Awaiting approval (future feature)
- **published**: Available for activation by organizations
- **deprecated**: Still functional but not recommended for new activations
- **archived**: No longer available for new activations

---

## Agent Types vs Agent Clones vs Activations

Understanding the relationship between these concepts is crucial:

### Agent Type (Blueprint)
```sql
-- Example from agents table
{
  "id": "keywordResearchAgent",
  "name": "SEO Keyword Research Agent",
  "status": "published",
  "configuration": {
    "configuration_schema": {...},
    "default_config_values": {...},
    "presentation_config": {...}
  }
}
```

### Tenant Activation (Organization-level Instance)
```sql
-- Example from tenant_agent_activations table
{
  "id": "uuid-activation-123",
  "tenant_id": "tenant-uuid",
  "agent_id": "keywordResearchAgent",
  "status": "active",
  "custom_parameters": {
    "company_name": "ACME Corp",
    "industry": "technology"
  }
}
```

### User Clone (Personal Instance)
```sql
-- Example from user_agent_clones table
{
  "id": "uuid-clone-456",
  "user_id": "user-uuid",
  "tenant_activation_id": "uuid-activation-123",
  "name": "My SEO Assistant",
  "custom_parameters": {
    "preferred_language": "en",
    "expertise_level": "advanced"
  }
}
```

---

## Database Schema Overview

### Core Tables

#### [`agents`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1262) Table
The master registry of all agent types:

```sql
CREATE TABLE agents (
    id text PRIMARY KEY,                    -- Unique identifier (e.g., "keywordResearchAgent")
    name text NOT NULL,                     -- Display name
    name_i18n jsonb,                       -- Internationalized names
    description text,                       -- Agent description
    description_i18n jsonb,                -- Internationalized descriptions
    version text DEFAULT '1.0.0',          -- Version tracking
    status agent_status DEFAULT 'draft',   -- Lifecycle status
    configuration jsonb,                    -- Main configuration object
    pricing jsonb DEFAULT '{}',             -- Pricing information
    is_public boolean DEFAULT false,        -- Public visibility
    created_by_org_id uuid NOT NULL,       -- Creating organization
    customizable_parameters jsonb,         -- Legacy field
    voice_config jsonb,                    -- Voice settings
    applicable_metrics jsonb,              -- Performance metrics
    chat_ui_settings jsonb,                -- Chat interface settings
    phone_settings jsonb,                  -- Phone integration settings
    category text,                         -- Agent category
    mastra_agent_id text,                  -- Agent ID used in CopilotKit integration
    mastra_api_base_url text,              -- Runtime URL for the deployed Mastra agent
    agent_type text,                       -- Type classification for the agent
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    published_at timestamp with time zone
);
```

#### Key Relationships

```sql
-- Agent exposures (which orgs can see which agents)
organization_agent_exposures
├── organization_id → organizations.id
├── agent_id → agents.id
└── exposed_by_org_id → organizations.id

-- Tenant activations (org activates an agent type)
tenant_agent_activations
├── tenant_id → organizations.id
├── agent_id → agents.id
└── activated_by_user_id → users.id

-- User clones (user personalizes an activation)
user_agent_clones
├── user_id → users.id
├── tenant_id → organizations.id
├── tenant_activation_id → tenant_agent_activations.id
└── phone_number_id → phone_numbers.id

-- Rep rooms (public interface for clones)
rep_rooms
├── user_agent_clone_id → user_agent_clones.id
└── public_slug (unique URL identifier)
```

---

## Configuration Structure Deep Dive

The [`configuration`](../../supabase/migrations/20250428000000_fresh_remote_schema.sql:1270) field in the agents table is a complex JSONB object that defines how the agent behaves and can be customized.

### Core Configuration Schema

```typescript
interface AgentConfiguration {
  // Schema definitions
  configuration_schema: ConfigurationSchema;
  runtime_context_schema: RuntimeContextSchema;
  knowledge_source_config_schema: KnowledgeSourceSchema;
  human_in_the_loop_schema: HumanInTheLoopSchema;
  
  // Default values and presentation
  default_config_values: DefaultConfigValues;
  presentation_config: PresentationConfig;
  
  // Operational settings
  available_channels: Channel[];
  avatar_type: string;
  capabilities: string[];
  agent_operational_mode: OperationalMode;
  
  // Advanced configurations
  static_overrides: StaticOverrides;
  presentation_overrides: PresentationOverrides;
}
```

### Configuration Schema
Defines what parameters can be customized by tenants and users:

```json
{
  "configuration_schema": {
    "type": "object",
    "properties": {
      "company_name": {
        "type": "string",
        "title": "Company Name",
        "description": "The name of your company",
        "default": ""
      },
      "industry": {
        "type": "string",
        "title": "Industry",
        "enum": ["technology", "healthcare", "finance", "retail"],
        "description": "Your company's industry"
      },
      "expertise_level": {
        "type": "string",
        "title": "Expertise Level",
        "enum": ["beginner", "intermediate", "advanced"],
        "default": "intermediate"
      }
    },
    "required": ["company_name"]
  }
}
```

### Runtime Context Schema
Defines dynamic context that gets passed to the agent during conversations:

```json
{
  "runtime_context_schema": {
    "type": "object",
    "properties": {
      "user_tier": {
        "type": "string",
        "enum": ["free", "pro", "enterprise"]
      },
      "user_language": {
        "type": "string",
        "default": "en"
      },
      "session_context": {
        "type": "object",
        "properties": {
          "current_project": {"type": "string"},
          "user_goals": {"type": "array", "items": {"type": "string"}}
        }
      }
    }
  }
}
```

### Presentation Configuration
Controls how the agent appears in different interfaces:

```json
{
  "presentation_config": {
    "avatar": {
      "type": "character",
      "character_id": "professional_consultant",
      "style": "modern"
    },
    "personality": {
      "tone": "professional",
      "formality": "business_casual",
      "expertise_display": "confident"
    },
    "branding": {
      "primary_color": "#2563eb",
      "accent_color": "#3b82f6",
      "logo_position": "top_left"
    }
  }
}
```

### Available Channels
Specifies where the agent can be used:

```json
{
  "available_channels": [
    "chat",           // Web chat interface
    "voice",          // Voice conversations
    "phone",          // Phone calls
    "api",            // Direct API access
    "rep_room"        // Public rep room interface
  ]
}
```

### Operational Modes

```typescript
type OperationalMode = 
  | "interactive_user_facing"     // Direct user interaction
  | "background_processing"       // Background tasks
  | "api_service"                // API-only service
  | "hybrid";                    // Multiple modes
```

---

## Creating Agent Types

### Prerequisites

1. **Permissions**: Must have `house_admin` or `house_manager` role
2. **Organization**: Must be part of the House organization
3. **Understanding**: Familiarity with JSON Schema and agent configuration

### Step-by-Step Creation Process

#### 1. Design the Agent Configuration

Start by defining what your agent will do and what can be customized:

```json
{
  "id": "customerSupportAgent",
  "name": "Customer Support Agent",
  "description": "AI agent specialized in customer support and issue resolution",
  "category": "customer_service",
  "configuration": {
    "configuration_schema": {
      "type": "object",
      "properties": {
        "company_name": {
          "type": "string",
          "title": "Company Name",
          "description": "Your company name for personalized responses"
        },
        "support_hours": {
          "type": "string",
          "title": "Support Hours",
          "description": "Business hours for support (e.g., '9 AM - 5 PM EST')"
        },
        "escalation_email": {
          "type": "string",
          "format": "email",
          "title": "Escalation Email",
          "description": "Email for complex issue escalation"
        }
      },
      "required": ["company_name", "escalation_email"]
    },
    "default_config_values": {
      "company_name": "",
      "support_hours": "24/7",
      "escalation_email": ""
    },
    "available_channels": ["chat", "voice", "rep_room"],
    "agent_operational_mode": "interactive_user_facing"
  }
}
```

#### 2. Use the Agent Types API

```typescript
// POST to /agent-types-create
const response = await fetch('/functions/v1/agent-types-create', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(agentTypeData)
});
```

#### 3. Validate and Test

After creation, the agent type will be in `draft` status. Test thoroughly before publishing:

```typescript
// Update status to published
const publishResponse = await fetch(`/functions/v1/agent-types-update`, {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    id: 'customerSupportAgent',
    status: 'published'
  })
});
```

---

## Managing Agent Types

### Lifecycle Management

#### Updating Agent Types
Use the [`agent-types-update`](../../supabase/functions/agent-types-update/index.ts:52) function:

```typescript
const updateData = {
  id: 'customerSupportAgent',
  name: 'Enhanced Customer Support Agent',
  configuration: {
    // Updated configuration
    configuration_schema: {
      // New schema with additional fields
    }
  }
};
```

#### Version Management
While the platform supports versioning, currently it's managed manually:

```json
{
  "version": "2.1.0",
  "changelog": {
    "2.1.0": "Added multi-language support",
    "2.0.0": "Major configuration schema update",
    "1.0.0": "Initial release"
  }
}
```

### Status Transitions

```typescript
// Draft → Published
await updateAgentType(agentId, { status: 'published' });

// Published → Deprecated (still works, but not recommended)
await updateAgentType(agentId, { status: 'deprecated' });

// Deprecated → Archived (no new activations allowed)
await updateAgentType(agentId, { status: 'archived' });
```

### Exposure Management

Control which organizations can see and activate agent types:

```sql
-- Expose agent to specific organization
INSERT INTO organization_agent_exposures (
  organization_id,
  agent_id,
  exposed_by_org_id,
  enabled
) VALUES (
  'target-org-uuid',
  'customerSupportAgent',
  'house-org-uuid',
  true
);
```

---

## JSON Schema Examples

### Complete Agent Type Example

Based on the [`keywordResearchAgent`](../../supabase/migrations/20250613135500_add_mastra_config_to_rep_rooms.sql:14) referenced in the codebase:

```json
{
  "id": "keywordResearchAgent",
  "name": "SEO Keyword Research Agent",
  "description": "Advanced AI agent for SEO keyword research and content optimization",
  "version": "1.0.0",
  "status": "published",
  "category": "seo_marketing",
  "is_public": false,
  "created_by_org_id": "house-org-uuid",
  "configuration": {
    "configuration_schema": {
      "type": "object",
      "properties": {
        "target_audience": {
          "type": "string",
          "title": "Target Audience",
          "description": "Primary target audience for keyword research",
          "examples": ["B2B professionals", "E-commerce shoppers", "Local customers"]
        },
        "industry_focus": {
          "type": "string",
          "title": "Industry Focus",
          "description": "Specific industry or niche",
          "examples": ["Technology", "Healthcare", "Finance", "Retail"]
        },
        "content_type": {
          "type": "array",
          "title": "Content Types",
          "description": "Types of content to optimize for",
          "items": {
            "type": "string",
            "enum": ["blog_posts", "product_pages", "landing_pages", "social_media"]
          },
          "default": ["blog_posts"]
        },
        "competitor_domains": {
          "type": "array",
          "title": "Competitor Domains",
          "description": "List of competitor websites to analyze",
          "items": {
            "type": "string",
            "format": "uri"
          },
          "maxItems": 5
        },
        "geographic_focus": {
          "type": "string",
          "title": "Geographic Focus",
          "description": "Target geographic region",
          "enum": ["global", "usa", "europe", "asia", "local"],
          "default": "global"
        }
      },
      "required": ["target_audience", "industry_focus"]
    },
    "runtime_context_schema": {
      "type": "object",
      "properties": {
        "user_tier": {
          "type": "string",
          "enum": ["free", "pro", "enterprise"],
          "description": "User subscription tier affects feature availability"
        },
        "monthly_search_limit": {
          "type": "integer",
          "description": "Remaining keyword searches for the month"
        },
        "user_language": {
          "type": "string",
          "default": "en",
          "description": "User's preferred language for responses"
        },
        "current_project": {
          "type": "object",
          "properties": {
            "name": {"type": "string"},
            "domain": {"type": "string"},
            "primary_keywords": {
              "type": "array",
              "items": {"type": "string"}
            }
          }
        }
      }
    },
    "default_config_values": {
      "target_audience": "",
      "industry_focus": "",
      "content_type": ["blog_posts"],
      "competitor_domains": [],
      "geographic_focus": "global"
    },
    "presentation_config": {
      "avatar": {
        "type": "professional",
        "style": "modern_consultant",
        "color_scheme": "blue_professional"
      },
      "personality": {
        "tone": "expert",
        "communication_style": "data_driven",
        "expertise_level": "senior_consultant"
      },
      "interface_elements": {
        "show_confidence_scores": true,
        "display_search_volume": true,
        "include_trend_analysis": true
      }
    },
    "available_channels": ["chat", "api", "rep_room"],
    "avatar_type": "professional_consultant",
    "capabilities": [
      "keyword_research",
      "competitor_analysis",
      "content_optimization",
      "trend_analysis",
      "search_volume_estimation"
    ],
    "agent_operational_mode": "interactive_user_facing"
  },
  "pricing": {
    "credits_per_completion_token": 0.002,
    "credits_per_prompt_token": 0.001
  },
  "voice_config": {
    "enabled": false,
    "voice_id": null,
    "speech_rate": 1.0
  },
  "chat_ui_settings": {
    "welcome_message": "Hello! I'm your SEO keyword research specialist. I can help you discover high-value keywords, analyze competitors, and optimize your content strategy. What would you like to research today?",
    "placeholder_text": "Ask me about keywords, competitors, or content optimization...",
    "suggested_prompts": [
      "Find keywords for my blog about sustainable technology",
      "Analyze my competitor's top-performing content",
      "Suggest long-tail keywords for my product pages"
    ]
  },
  "applicable_metrics": [
    "response_accuracy",
    "keyword_relevance_score",
    "user_satisfaction",
    "task_completion_rate"
  ]
}
```

### Tenant Activation Example

When a tenant activates the keyword research agent:

```json
{
  "id": "activation-uuid-123",
  "tenant_id": "tenant-uuid-456",
  "agent_id": "keywordResearchAgent",
  "status": "active",
  "custom_parameters": {
    "target_audience": "Small business owners",
    "industry_focus": "Local services",
    "content_type": ["blog_posts", "landing_pages"],
    "geographic_focus": "usa"
  },
  "voice_settings": null,
  "chat_ui_settings": {
    "welcome_message": "Welcome to ACME Corp's SEO assistant! I'm here to help you dominate local search results."
  },
  "mcp_server_ids": ["mcp-server-uuid-789"],
  "activated_at": "2024-01-15T10:30:00Z"
}
```

### User Clone Example

When a user creates a personal clone:

```json
{
  "id": "clone-uuid-789",
  "user_id": "user-uuid-101",
  "tenant_id": "tenant-uuid-456",
  "tenant_activation_id": "activation-uuid-123",
  "name": "My SEO Research Assistant",
  "status": "active",
  "custom_parameters": {
    "target_audience": "Tech startup founders",
    "industry_focus": "SaaS technology",
    "content_type": ["blog_posts"],
    "competitor_domains": [
      "https://competitor1.com",
      "https://competitor2.com"
    ],
    "geographic_focus": "global"
  },
  "chat_ui_settings": {
    "welcome_message": "Hi! I'm your personal SEO assistant, specialized in SaaS keyword research. Ready to boost your organic traffic?",
    "suggested_prompts": [
      "Find keywords for SaaS landing pages",
      "Analyze competitor content strategies",
      "Research long-tail keywords for our blog"
    ]
  }
}
```

---

## Best Practices

### Configuration Design

#### 1. Keep Schemas Simple and Intuitive
```json
// Good: Clear, simple properties
{
  "company_name": {
    "type": "string",
    "title": "Company Name",
    "description": "Your company name for personalized responses"
  }
}

// Avoid: Overly complex nested structures
{
  "advanced_config": {
    "type": "object",
    "properties": {
      "nested_level_1": {
        "type": "object",
        "properties": {
          "nested_level_2": {
            // Too deep!
          }
        }
      }
    }
  }
}
```

#### 2. Provide Sensible Defaults
```json
{
  "default_config_values": {
    "response_length": "medium",
    "formality_level": "professional",
    "include_examples": true,
    "max_suggestions": 5
  }
}
```

#### 3. Use Validation and Constraints
```json
{
  "email_address": {
    "type": "string",
    "format": "email",
    "title": "Contact Email"
  },
  "max_results": {
    "type": "integer",
    "minimum": 1,
    "maximum": 100,
    "default": 10
  }
}
```

### Security Considerations

#### 1. Validate All Inputs
```typescript
// Always validate configuration against schema
function validateConfiguration(config: any, schema: any): boolean {
  // Use a JSON Schema validator
  return ajv.validate(schema, config);
}
```

#### 2. Sanitize User Inputs
```typescript
// Sanitize strings to prevent injection attacks
function sanitizeString(input: string): string {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .trim();
}
```

#### 3. Implement Rate Limiting
```typescript
// Limit API calls per user/tenant
const rateLimits = {
  free: { requests: 100, window: '1h' },
  pro: { requests: 1000, window: '1h' },
  enterprise: { requests: 10000, window: '1h' }
};
```

### Performance Optimization

#### 1. Minimize Configuration Size
- Keep configuration objects under 1MB
- Use references instead of duplicating large objects
- Compress repeated patterns

#### 2. Cache Frequently Accessed Data
```typescript
// Cache agent configurations
const configCache = new Map<string, AgentConfiguration>();

async function getAgentConfig(agentId: string): Promise<AgentConfiguration> {
  if (configCache.has(agentId)) {
    return configCache.get(agentId)!;
  }
  
  const config = await fetchFromDatabase(agentId);
  configCache.set(agentId, config);
  return config;
}
```

#### 3. Use Efficient Queries
```sql
-- Good: Specific field selection
SELECT id, name, configuration->>'configuration_schema' as schema
FROM agents 
WHERE status = 'published';

-- Avoid: Selecting all data when not needed
SELECT * FROM agents;
```

---

## Advanced Technical Parameters

### CopilotKit Field Mappings

The three key fields in the agents table enable seamless integration with CopilotKit runtime:

#### `mastra_agent_id` → CopilotKit `agent` Parameter

```typescript
// Agent type configuration
const agentType = {
  id: "keywordResearchAgent",
  mastra_agent_id: "seo-keyword-research-v1",  // Maps to CopilotKit agent parameter
  // ... other fields
};

// CopilotKit usage
const copilotConfig = {
  agent: agentType.mastra_agent_id,  // "seo-keyword-research-v1"
  // ... other config
};
```

#### `mastra_api_base_url` → CopilotKit `runtimeUrl` Parameter

```typescript
// Agent type configuration
const agentType = {
  id: "keywordResearchAgent",
  mastra_api_base_url: "https://mastra-runtime.example.com",  // Maps to CopilotKit runtimeUrl
  // ... other fields
};

// CopilotKit usage
const copilotConfig = {
  runtimeUrl: agentType.mastra_api_base_url,  // "https://mastra-runtime.example.com"
  // ... other config
};
```

#### `agent_type` → Runtime Behavior Classification

```typescript
// Agent type configuration with classification
const agentType = {
  id: "keywordResearchAgent",
  agent_type: "task_specific",  // Enables specialized routing and behavior
  // ... other fields
};

// Runtime behavior based on agent_type
function getAgentBehavior(agentType: string) {
  switch (agentType) {
    case "conversational":
      return { mode: "chat", features: ["general_conversation"] };
    case "task_specific":
      return { mode: "focused", features: ["specialized_tools", "domain_expertise"] };
    case "workflow":
      return { mode: "guided", features: ["step_by_step", "progress_tracking"] };
    default:
      return { mode: "standard", features: ["basic_interaction"] };
  }
}
```

#### Complete CopilotKit Integration Example

```typescript
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";

function AgentInterface({ userAgentClone }: { userAgentClone: UserAgentClone }) {
  const agentType = userAgentClone.tenant_activation.agent;
  
  return (
    <CopilotKit
      agent={agentType.mastra_agent_id}           // From agents.mastra_agent_id
      runtimeUrl={agentType.mastra_api_base_url}  // From agents.mastra_api_base_url
      conversationId={userAgentClone.id}
      // Additional context based on agent_type
      agentMetadata={{
        type: agentType.agent_type,
        capabilities: agentType.configuration.capabilities,
        customParameters: userAgentClone.custom_parameters
      }}
    >
      <CopilotSidebar
        defaultOpen={true}
        labels={{
          title: agentType.name,
          initial: `Hello! I'm your ${agentType.name}. How can I help you today?`
        }}
      />
      {/* Your application content */}
    </CopilotKit>
  );
}
```

#### Runtime Context Integration

These fields work together to provide rich runtime context:

```typescript
// Runtime context passed to Mastra agent
const runtimeContext = {
  agent_id: agentType.mastra_agent_id,
  agent_type: agentType.agent_type,
  base_url: agentType.mastra_api_base_url,
  user_config: userAgentClone.custom_parameters,
  tenant_config: userAgentClone.tenant_activation.custom_parameters,
  session_data: {
    user_id: userAgentClone.user_id,
    tenant_id: userAgentClone.tenant_id,
    conversation_id: conversationId
  }
};
```

---

## Common Patterns

### Dynamic Agent Behavior

Based on the [`dynamic-agents.md`](../../docs/mastra_guides/dynamic-agents.md) guide, agents can adapt their behavior using runtime context:

```typescript
// Example: Adjust response based on user tier
function buildAgentInstructions(baseInstructions: string, context: RuntimeContext): string {
  let instructions = baseInstructions;
  
  if (context.user_tier === 'free') {
    instructions += "\nLimit responses to 3 suggestions maximum.";
  } else if (context.user_tier === 'enterprise') {
    instructions += "\nProvide detailed analysis with data sources and confidence scores.";
  }
  
  if (context.user_language !== 'en') {
    instructions += `\nRespond in ${context.user_language}.`;
  }
  
  return instructions;
}
```

### Multi-Channel Support

Configure agents to work across different interfaces:

```json
{
  "available_channels": ["chat", "voice", "phone", "api"],
  "channel_specific_config": {
    "voice": {
      "speech_rate": 0.9,
      "use_ssml": true,
      "max_response_length": 200
    },
    "chat": {
      "use_markdown": true,
      "show_typing_indicator": true,
      "max_response_length": 1000
    },
    "phone": {
      "enable_dtmf": true,
      "transfer_capability": true,
      "recording_enabled": false
    }
  }
}
```

### Hierarchical Configuration

Allow configuration inheritance from tenant to user level:

```typescript
function mergeConfigurations(
  agentDefaults: Configuration,
  tenantConfig: Configuration,
  userConfig: Configuration
): Configuration {
  return {
    ...agentDefaults,
    ...tenantConfig,
    ...userConfig,
    // Special handling for arrays
    capabilities: [
      ...agentDefaults.capabilities,
      ...(tenantConfig.capabilities || []),
      ...(userConfig.capabilities || [])
    ]
  };
}
```

### Conditional Features

Enable features based on subscription tier:

```json
{
  "runtime_context_schema": {
    "properties": {
      "user_tier": {
        "type": "string",
        "enum": ["free", "pro", "enterprise"]
      }
    }
  },
  "tier_based_features": {
    "free": {
      "max_queries_per_day": 10,
      "features": ["basic_search"]
    },
    "pro": {
      "max_queries_per_day": 100,
      "features": ["basic_search", "competitor_analysis"]
    },
    "enterprise": {
      "max_queries_per_day": 1000,
      "features": ["basic_search", "competitor_analysis", "custom_reports", "api_access"]
    }
  }
}
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Configuration Validation Errors

**Problem**: Agent type creation fails with validation errors.

**Solution**:
```typescript
// Check configuration schema validity
function validateConfigurationSchema(schema: any): string[] {
  const errors: string[] = [];
  
  if (!schema.type || schema.type !== 'object') {
    errors.push('Configuration schema must be of type "object"');
  }
  
  if (!schema.properties) {
    errors.push('Configuration schema must have "properties" field');
  }
  
  // Validate each property
  for (const [key, prop] of Object.entries(schema.properties || {})) {
    if (!prop.type) {
      errors.push(`Property "${key}" must have a type`);
    }
  }
  
  return errors;
}
```

#### 2. Agent Not Visible to Organizations

**Problem**: Published agent type not showing up for tenants.

**Diagnosis**:
```sql
-- Check if agent is properly exposed
SELECT 
  a.id,
  a.status,
  oae.organization_id,
  oae.enabled
FROM agents a
LEFT JOIN organization_agent_exposures oae ON a.id = oae.agent_id
WHERE a.id = 'your-agent-id';
```

**Solution**:
```sql
-- Ensure proper exposure
INSERT INTO organization_agent_exposures (
  organization_id,
  agent_id,
  exposed_by_org_id,
  enabled
) VALUES (
  'target-org-uuid',
  'your-agent-id',
  'house-org-uuid',
  true
);
```

#### 3. Runtime Context Not Working

**Problem**: Dynamic behavior based on runtime context not functioning.

**Check**:
```typescript
// Verify runtime context is properly passed
function debugRuntimeContext(context: RuntimeContext) {
  console.log('Runtime Context:', {
    user_tier: context.user_tier,
    user_language: context.user_language,
    session_data: context.session_data
  });
  
  // Validate against schema
  const isValid = validateRuntimeContext(context);
  if (!isValid) {
    console.error('Invalid runtime context structure');
  }
}
```

#### 4. Performance Issues

**Problem**: Slow agent responses or configuration loading.

**Optimization**:
```typescript
// Implement configuration caching
class AgentConfigCache {
  private cache = new Map<string, { config: any, timestamp: number }>();
  private TTL = 5 * 60 * 1000; // 5 minutes
  
  get(agentId: string): any | null {
    const cached = this.cache.get(agentId);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(agentId);
      return null;
    }
    
    return cached.config;
  }
  
  set(agentId: string, config: any): void {
    this.cache.set(agentId, {
      config,
now()
    });
  }
}
```

#### 5. Permission Denied Errors

**Problem**: Users can't create, update, or delete agent types.

**Check Permissions**:
```sql
-- Verify user role and organization
SELECT 
  u.id,
  u.role,
  o.type as org_type,
  o.id as org_id
FROM users u
JOIN organizations o ON u.organization_id = o.id
WHERE u.id = 'user-uuid';
```

**Required Roles**:
- **Create/Update/Delete Agent Types**: `house_admin`, `house_manager`
- **Expose Agents**: `house_admin`, `super_agency_admin`, `agency_admin`
- **Activate Agents**: Any tenant user with proper permissions

#### 6. Configuration Merge Issues

**Problem**: User or tenant configurations not properly merging with agent defaults.

**Debug Configuration Merge**:
```typescript
function debugConfigurationMerge(
  agentConfig: any,
  tenantConfig: any,
  userConfig: any
) {
  console.log('Agent Default Config:', agentConfig);
  console.log('Tenant Override Config:', tenantConfig);
  console.log('User Override Config:', userConfig);
  
  const merged = mergeConfigurations(agentConfig, tenantConfig, userConfig);
  console.log('Final Merged Config:', merged);
  
  return merged;
}
```

### Debugging Tools

#### 1. Configuration Validator
```typescript
import Ajv from 'ajv';

function validateAgentConfiguration(config: any): ValidationResult {
  const ajv = new Ajv();
  
  const schema = {
    type: 'object',
    required: ['configuration_schema', 'default_config_values'],
    properties: {
      configuration_schema: { type: 'object' },
      default_config_values: { type: 'object' },
      available_channels: { 
        type: 'array',
        items: { 
          type: 'string',
          enum: ['chat', 'voice', 'phone', 'api', 'rep_room']
        }
      }
    }
  };
  
  const validate = ajv.compile(schema);
  const valid = validate(config);
  
  return {
    valid,
    errors: validate.errors || []
  };
}
```

#### 2. Runtime Context Debugger
```typescript
function debugRuntimeContext(context: RuntimeContext, agentId: string) {
  console.group(`Runtime Context Debug - Agent: ${agentId}`);
  
  console.log('Context Keys:', Object.keys(context));
  console.log('User Tier:', context.user_tier);
  console.log('Language:', context.user_language);
  console.log('Session Data:', context.session_data);
  
  // Validate against agent's runtime context schema
  const agent = getAgentConfiguration(agentId);
  if (agent?.configuration?.runtime_context_schema) {
    const validation = validateRuntimeContext(
      context, 
      agent.configuration.runtime_context_schema
    );
    console.log('Schema Validation:', validation);
  }
  
  console.groupEnd();
}
```

### Monitoring and Logging

#### 1. Agent Performance Metrics
```typescript
interface AgentMetrics {
  agent_id: string;
  total_activations: number;
  active_clones: number;
  avg_response_time: number;
  error_rate: number;
  user_satisfaction: number;
}

async function getAgentMetrics(agentId: string): Promise<AgentMetrics> {
  // Query metrics from database
  const metrics = await supabaseClient
    .from('agent_performance_metrics')
    .select('*')
    .eq('agent_id', agentId)
    .single();
    
  return metrics.data;
}
```

#### 2. Configuration Change Tracking
```sql
-- Track configuration changes
CREATE TABLE agent_configuration_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id text REFERENCES agents(id),
  changed_by uuid REFERENCES users(id),
  old_configuration jsonb,
  new_configuration jsonb,
  change_summary text,
  created_at timestamp with time zone DEFAULT now()
);
```

---

## Advanced Topics

### Custom MCP Server Integration

Agent types can integrate with custom MCP (Model Context Protocol) servers for specialized functionality:

```json
{
  "mcp_integration": {
    "required_servers": [
      {
        "server_id": "seo-tools-mcp",
        "version": "^1.0.0",
        "capabilities": ["keyword_research", "competitor_analysis"]
      }
    ],
    "optional_servers": [
      {
        "server_id": "content-generator-mcp",
        "version": "^2.0.0",
        "capabilities": ["content_generation"]
      }
    ]
  }
}
```

### Multi-Language Support

Implement internationalization for agent types:

```json
{
  "name": "SEO Keyword Research Agent",
  "name_i18n": {
    "en": "SEO Keyword Research Agent",
    "es": "Agente de Investigación de Palabras Clave SEO",
    "fr": "Agent de Recherche de Mots-clés SEO",
    "de": "SEO-Keyword-Recherche-Agent"
  },
  "description_i18n": {
    "en": "Advanced AI agent for SEO keyword research and content optimization",
    "es": "Agente de IA avanzado para investigación de palabras clave SEO y optimización de contenido",
    "fr": "Agent IA avancé pour la recherche de mots-clés SEO et l'optimisation de contenu",
    "de": "Fortgeschrittener KI-Agent für SEO-Keyword-Recherche und Content-Optimierung"
  }
}
```

### Voice Integration

Configure agents for voice interactions:

```json
{
  "voice_config": {
    "enabled": true,
    "voice_id": "professional_female_en",
    "speech_rate": 1.0,
    "pitch": 0.0,
    "volume": 0.8,
    "ssml_enabled": true,
    "interruption_handling": "graceful",
    "silence_detection": {
      "enabled": true,
      "threshold_ms": 2000
    }
  },
  "phone_settings": {
    "enabled": true,
    "dtmf_enabled": true,
    "transfer_capability": true,
    "recording_enabled": false,
    "max_call_duration": 1800
  }
}
```

### API Integration Patterns

Configure agents for API-only usage:

```json
{
  "api_configuration": {
    "rate_limits": {
      "requests_per_minute": 60,
      "requests_per_hour": 1000,
      "requests_per_day": 10000
    },
    "authentication": {
      "required": true,
      "methods": ["bearer_token", "api_key"]
    },
    "response_formats": ["json", "xml", "csv"],
    "webhook_support": {
      "enabled": true,
      "events": ["task_completed", "error_occurred"]
    }
  }
}
```

### CopilotKit Integration

Agent types integrate seamlessly with CopilotKit to provide conversational AI interfaces. The three key fields enable this integration:

#### Field Mappings

- **`mastra_agent_id`**: Maps to the "agent" parameter in CopilotKit configuration
- **`mastra_api_base_url`**: Maps to the "runtimeUrl" in CopilotKit configuration
- **`agent_type`**: Provides type classification for routing and behavior

#### CopilotKit Configuration Example

```typescript
// Example CopilotKit configuration using agent type fields
const copilotKitConfig = {
  agent: agentType.mastra_agent_id,           // From agents.mastra_agent_id
  runtimeUrl: agentType.mastra_api_base_url,  // From agents.mastra_api_base_url
  // Additional configuration...
};
```

#### SecureCopilotKitComponent Integration

```typescript
import { SecureCopilotKitComponent } from '@/components/copilotkit/SecureCopilotKitComponent';

function AgentInterface({ agentClone }: { agentClone: UserAgentClone }) {
  const agentType = agentClone.tenant_activation.agent;
  
  return (
    <SecureCopilotKitComponent
      agent={agentType.mastra_agent_id}
      runtimeUrl={agentType.mastra_api_base_url}
      agentType={agentType.agent_type}
      conversationId={agentClone.id}
      userId={agentClone.user_id}
      tenantId={agentClone.tenant_id}
    />
  );
}
```

#### Runtime Integration Flow

1. **Agent Type Creation**: House admin sets `mastra_agent_id`, `mastra_api_base_url`, and `agent_type`
2. **Tenant Activation**: Tenant activates the agent type for their organization
3. **User Clone**: User creates a personal clone of the activated agent
4. **CopilotKit Runtime**: Frontend uses the agent type fields to connect to the Mastra runtime
5. **Conversation Flow**: CopilotKit handles the conversation using the specified agent and runtime URL

#### Agent Type Classification

The `agent_type` field enables different behaviors and routing:

```typescript
// Example agent type classifications
type AgentTypeClassification =
  | "conversational"      // General chat agents
  | "task_specific"       // Specialized task agents (SEO, support, etc.)
  | "workflow"           // Multi-step workflow agents
  | "analytical"         // Data analysis and reporting agents
  | "creative"           // Content creation agents
  | "integration"        // Third-party service integration agents;
```

#### Configuration in Agent Type

```json
{
  "id": "keywordResearchAgent",
  "name": "SEO Keyword Research Agent",
  "mastra_agent_id": "seo-keyword-research-v1",
  "mastra_api_base_url": "https://mastra-runtime.example.com",
  "agent_type": "task_specific",
  "configuration": {
    // Standard configuration...
  }
}
```

This integration enables seamless connection between the platform's agent management system and CopilotKit's conversational interface, providing users with a consistent and powerful AI interaction experience.

---

## Migration and Versioning

### Schema Evolution

When updating agent configurations, follow these patterns:

#### 1. Backward Compatible Changes
```typescript
// Adding optional fields is safe
const newSchema = {
  ...existingSchema,
  properties: {
    ...existingSchema.properties,
    new_optional_field: {
      type: 'string',
      title: 'New Feature',
      description: 'Optional new functionality'
    }
  }
};
```

#### 2. Breaking Changes
```typescript
// For breaking changes, increment major version
const migrationPlan = {
  from_version: '1.0.0',
  to_version: '2.0.0',
  breaking_changes: [
    {
      field: 'old_field_name',
      action: 'renamed',
      new_field: 'new_field_name',
      migration_function: 'migrateOldFieldToNew'
    }
  ]
};
```

#### 3. Configuration Migration
```typescript
async function migrateAgentConfiguration(
  agentId: string,
  fromVersion: string,
  toVersion: string
) {
  const currentConfig = await getAgentConfiguration(agentId);
  const migrationRules = getMigrationRules(fromVersion, toVersion);
  
  let migratedConfig = { ...currentConfig };
  
  for (const rule of migrationRules) {
    migratedConfig = await applyMigrationRule(migratedConfig, rule);
  }
  
  // Validate migrated configuration
  const validation = validateAgentConfiguration(migratedConfig);
  if (!validation.valid) {
    throw new Error(`Migration failed: ${validation.errors.join(', ')}`);
  }
  
  // Update with new version
  await updateAgentConfiguration(agentId, {
    ...migratedConfig,
    version: toVersion
  });
}
```

---

## Testing Agent Types

### Unit Testing Configuration

```typescript
describe('Agent Configuration', () => {
  test('should validate configuration schema', () => {
    const config = {
      configuration_schema: {
        type: 'object',
        properties: {
          company_name: { type: 'string' }
        }
      },
      default_config_values: {
        company_name: ''
      }
    };
    
    const result = validateAgentConfiguration(config);
    expect(result.valid).toBe(true);
  });
  
  test('should merge configurations correctly', () => {
    const agentDefaults = { setting1: 'default', setting2: 'default' };
    const tenantConfig = { setting1: 'tenant_override' };
    const userConfig = { setting2: 'user_override' };
    
    const merged = mergeConfigurations(agentDefaults, tenantConfig, userConfig);
    
    expect(merged).toEqual({
      setting1: 'tenant_override',
      setting2: 'user_override'
    });
  });
});
```

### Integration Testing

```typescript
describe('Agent Type Lifecycle', () => {
  test('should create, activate, and clone agent', async () => {
    // Create agent type
    const agentType = await createAgentType(testAgentData);
    expect(agentType.status).toBe('draft');
    
    // Publish agent type
    await updateAgentType(agentType.id, { status: 'published' });
    
    // Activate for tenant
    const activation = await activateAgentForTenant(
      agentType.id,
      testTenantId,
      tenantConfig
    );
    expect(activation.status).toBe('active');
    
    // Create user clone
    const clone = await createUserClone(
      activation.id,
      testUserId,
      userConfig
    );
    expect(clone.status).toBe('active');
  });
});
```

---

## Conclusion

Agent types are the foundation of our multi-tenant AI platform, providing a flexible and scalable way to deploy AI capabilities across organizations. This guide has covered:

- **Conceptual Understanding**: What agent types are and how they fit into the platform architecture
- **Technical Implementation**: Database schemas, configuration structures, and API usage
- **Practical Examples**: Real-world JSON configurations and code samples
- **Best Practices**: Security, performance, and maintainability guidelines
- **Troubleshooting**: Common issues and debugging techniques

### Key Takeaways

1. **Agent Types are Templates**: They define capabilities but aren't running instances
2. **Multi-Level Customization**: Configuration can be overridden at tenant and user levels
3. **Security First**: Always validate inputs and implement proper access controls
4. **Performance Matters**: Cache configurations and optimize database queries
5. **Plan for Evolution**: Design schemas that can evolve without breaking existing implementations

### Next Steps

- Review the [Dynamic Agents Guide](../mastra_guides/dynamic-agents.md) for runtime behavior patterns
- Explore the [Agent Runtime Context](../mastra_guides/Agent-Runtime-Context.md) documentation
- Check the [CopilotKit Integration Guide](../copilotkit/) for frontend implementation
- Study the database migrations in [`supabase/migrations/`](../../supabase/migrations/) for schema details

### Getting Help

- **Database Issues**: Check the Supabase logs and RLS policies
- **Configuration Problems**: Use the validation tools provided in this guide
- **Performance Issues**: Implement caching and optimize queries
- **Permission Errors**: Verify user roles and organization hierarchy

Remember: Agent types are powerful tools that enable consistent, scalable AI deployment across our platform. Take time to understand the configuration schemas and test thoroughly before publishing to production.
      timestamp: Date.