import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import { MessageCir<PERSON>, Mic, MicOff, Send } from 'lucide-react';
import { ChatMessage as ChatMessageType } from '../../types/rep-room';
import { ChatMessage } from './ChatMessage';
import { CopilotChat } from '@copilotkit/react-ui';
import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core';
import { useUnifiedVoice, VoiceMessage } from '../../contexts/rroom/UnifiedVoiceContext';

interface CopilotKitConversationFlowProps {
  messages: ChatMessageType[];
  isVoiceActive: boolean;
  isVoiceConnecting?: boolean;
  onToggleVoice?: () => void;
  onSendMessage?: (message: string) => void;
  className?: string;
  agentName?: string;
  sessionId?: string;
}

export const CopilotKitConversationFlow: React.FC<CopilotKitConversationFlowProps> = ({
  messages,
  isVoiceActive,
  isVoiceConnecting = false,
  onToggleVoice,
  onSendMessage,
  className = '',
  agentName = 'AI Assistant',
  sessionId
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [inputMessage, setInputMessage] = React.useState('');
  const [showCopilotChat, setShowCopilotChat] = React.useState(true);
  const [processedVoiceMessages, setProcessedVoiceMessages] = React.useState<Set<string>>(new Set());
  
  // Get voice messages from UnifiedVoiceContext
  const { state: voiceState } = useUnifiedVoice();

  // Convert voice messages to chat message format
  const convertVoiceMessageToChatMessage = useCallback((voiceMessage: VoiceMessage): ChatMessageType => {
    return {
      id: voiceMessage.id,
      sender: voiceMessage.role === 'assistant' ? agentName : 'You',
      content: voiceMessage.content,
      timestamp: new Date(voiceMessage.timestamp),
      type: voiceMessage.role === 'assistant' ? 'agent' : 'human',
      isTyping: false,
      avatar: voiceMessage.role === 'assistant' ? '' : undefined
    };
  }, [agentName]);

  // Merge voice messages with regular messages
  const allMessages = useMemo(() => {
    const voiceMessages = (voiceState.visibleMessages || []).map(convertVoiceMessageToChatMessage);
    const combinedMessages = [...messages, ...voiceMessages];
    
    // Remove duplicates by ID and sort by timestamp
    const uniqueMessages = combinedMessages.reduce((acc, message) => {
      if (!acc.find(m => m.id === message.id)) {
        acc.push(message);
      }
      return acc;
    }, [] as ChatMessageType[]);
    
    return uniqueMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }, [messages, voiceState.visibleMessages, convertVoiceMessageToChatMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [allMessages]);

  // CRITICAL FIX: Automatically process voice transcripts and send to CopilotKit
  useEffect(() => {
    console.log('[CopilotKitConversationFlow] 🔍 Voice state check:', {
      hasVisibleMessages: !!voiceState.visibleMessages,
      visibleMessagesLength: voiceState.visibleMessages?.length || 0,
      visibleMessages: voiceState.visibleMessages,
      processedCount: processedVoiceMessages.size
    });

    if (!voiceState.visibleMessages || voiceState.visibleMessages.length === 0) {
      console.log('[CopilotKitConversationFlow] ⚠️ No visible voice messages found');
      return;
    }

    // Find new user voice messages that haven't been processed yet
    const newUserVoiceMessages = voiceState.visibleMessages.filter(
      (voiceMessage: VoiceMessage) =>
        voiceMessage.role === 'user' &&
        !processedVoiceMessages.has(voiceMessage.id)
    );

    console.log('[CopilotKitConversationFlow] 🔍 Filtered voice messages:', {
      totalMessages: voiceState.visibleMessages.length,
      userMessages: voiceState.visibleMessages.filter(m => m.role === 'user').length,
      newUserMessages: newUserVoiceMessages.length,
      processedIds: Array.from(processedVoiceMessages)
    });

    if (newUserVoiceMessages.length === 0) {
      console.log('[CopilotKitConversationFlow] ℹ️ No new user voice messages to process');
      return;
    }

    console.log('[CopilotKitConversationFlow] 🎤 Processing new voice transcripts:', newUserVoiceMessages);

    // Process each new voice message
    newUserVoiceMessages.forEach((voiceMessage: VoiceMessage) => {
      console.log('[CopilotKitConversationFlow] 🚀 Sending voice transcript to CopilotKit:', voiceMessage.content);
      
      // Send the voice transcript as a regular message to trigger CopilotKit response
      if (onSendMessage) {
        onSendMessage(voiceMessage.content);
      } else {
        console.warn('[CopilotKitConversationFlow] ⚠️ onSendMessage callback not available');
      }

      // Mark this message as processed
      setProcessedVoiceMessages(prev => {
        const newSet = new Set(prev);
        newSet.add(voiceMessage.id);
        return newSet;
      });
    });

  }, [voiceState.visibleMessages, processedVoiceMessages, onSendMessage]);

  // Stabilize handlers with useCallback to prevent re-registration
  const sendMessageHandler = useCallback(async ({ message }: { message: string }) => {
    if (onSendMessage) {
      onSendMessage(message);
    }
    return `Message sent: ${message}`;
  }, [onSendMessage]);

  const toggleVoiceHandler = useCallback(async ({ enable }: { enable: boolean }) => {
    if (onToggleVoice && enable !== isVoiceActive) {
      onToggleVoice();
    }
    return `Voice mode ${enable ? 'enabled' : 'disabled'}`;
  }, [onToggleVoice, isVoiceActive]);

  // Make session context available to CopilotKit
  useCopilotReadable({
    description: "Current Rep Room session information",
    value: {
      sessionId,
      agentName,
      isVoiceActive,
      messageCount: allMessages.length,
      lastMessage: allMessages[allMessages.length - 1]?.content || null,
      voiceMessageCount: voiceState.visibleMessages?.length || 0
    }
  });

  // Register CopilotKit action for sending messages with stable dependencies
  useCopilotAction({
    name: "sendMessage",
    description: "Send a message in the Rep Room conversation",
    parameters: [
      {
        name: "message",
        type: "string",
        description: "The message content to send",
        required: true
      }
    ],
    handler: sendMessageHandler
  }, [sendMessageHandler]);

  // Register CopilotKit action for voice control with stable dependencies
  useCopilotAction({
    name: "toggleVoice",
    description: "Toggle voice mode on or off in the Rep Room",
    parameters: [
      {
        name: "enable",
        type: "boolean",
        description: "Whether to enable or disable voice mode",
        required: true
      }
    ],
    handler: toggleVoiceHandler
  }, [toggleVoiceHandler]);

  const handleSendMessage = () => {
    if (inputMessage.trim() && onSendMessage) {
      onSendMessage(inputMessage.trim());
      setInputMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className={`bg-white border-r border-gray-200 flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageCircle className="w-5 h-5 text-gray-600" />
            <h2 className="font-semibold text-gray-900">Conversation</h2>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* CopilotKit Chat Toggle */}
            <button
              onClick={() => setShowCopilotChat(!showCopilotChat)}
              className={`
                p-1.5 rounded-md transition-colors text-xs
                ${showCopilotChat 
                  ? 'bg-blue-100 text-blue-600 hover:bg-blue-200' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
              title="Toggle AI Assistant"
            >
              AI
            </button>
            
            {/* Voice Toggle - Now controls mute/unmute, room auto-connects */}
            {onToggleVoice && (
              <button
                onClick={onToggleVoice}
                className={`
                  p-1.5 rounded-full transition-all duration-200
                  ${isVoiceConnecting
                    ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200'
                    : isVoiceActive
                      ? 'bg-green-100 text-green-600 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
                title={
                  isVoiceConnecting
                    ? 'Connecting to voice...'
                    : isVoiceActive
                      ? 'Mute microphone'
                      : 'Unmute microphone'
                }
              >
                {isVoiceConnecting ? (
                  <Mic className="w-4 h-4 animate-pulse" />
                ) : isVoiceActive ? (
                  <Mic className="w-4 h-4" />
                ) : (
                  <MicOff className="w-4 h-4" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* Voice Status Indicator - Real-time status from UnifiedVoiceContext */}
        {(isVoiceActive || isVoiceConnecting || voiceState.isConnected) && (
          <div className={`mt-2 flex items-center space-x-2 text-sm ${
            isVoiceConnecting || voiceState.isConnecting
              ? 'text-yellow-600'
              : voiceState.isSpeaking
                ? 'text-blue-600'
                : voiceState.isListening
                  ? 'text-green-600'
                  : voiceState.isConnected
                    ? 'text-gray-600'
                    : 'text-red-600'
          }`}>
            <div
              className={`w-2 h-2 rounded-full cursor-help ${
                voiceState.isSpeaking ? 'animate-pulse bg-blue-500' :
                voiceState.isListening ? 'animate-pulse bg-green-500' :
                isVoiceConnecting || voiceState.isConnecting ? 'animate-pulse bg-yellow-500' :
                voiceState.isConnected ? 'bg-green-500' : 'bg-red-500'
              }`}
              title={
                isVoiceConnecting || voiceState.isConnecting
                  ? 'Establishing connection to voice room...'
                  : voiceState.isSpeaking
                    ? 'Voice Activity Detected: Your speech is being processed and transcribed'
                    : voiceState.isListening
                      ? 'Microphone Active: Ready to detect and process your speech'
                      : voiceState.isConnected
                        ? 'Voice Ready: Connected to voice room, microphone ready for activation'
                        : 'Voice Disconnected: Not connected to voice room'
              }
            />
            <span
              className="cursor-help"
              title={
                isVoiceConnecting || voiceState.isConnecting
                  ? 'Please wait while we establish your voice connection'
                  : voiceState.isSpeaking
                    ? 'Your speech is being detected and transcribed in real-time'
                    : voiceState.isListening
                      ? 'Microphone is active and monitoring for speech. Start speaking to interact with the agent'
                      : voiceState.isConnected
                        ? 'Voice connection established. Click the microphone button to start speaking'
                        : 'Voice features are currently unavailable. Try refreshing or check your connection'
              }
            >
              {isVoiceConnecting || voiceState.isConnecting
                ? 'Connecting to voice...'
                : voiceState.isSpeaking
                  ? 'Speaking detected'
                  : voiceState.isListening
                    ? 'Listening - microphone active'
                    : voiceState.isConnected
                      ? 'Connected - microphone ready'
                      : 'Voice disconnected'
              }
            </span>
            {/* Participant count indicator */}
            {voiceState.isConnected && voiceState.totalParticipants > 0 && (
              <span
                className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full cursor-help"
                title={`${voiceState.totalParticipants} participant${voiceState.totalParticipants !== 1 ? 's' : ''} currently in this voice session`}
              >
                {voiceState.totalParticipants} participant{voiceState.totalParticipants !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        {/* Show CopilotKit Chat if enabled */}
        {showCopilotChat ? (
          <div className="h-full">
            <CopilotChat
              instructions={`You are an AI assistant in a Rep Room session with ${agentName}. 
                Help the user with their conversation and provide insights about the ongoing discussion.
                You can send messages on behalf of the user and control voice settings.
                Current session: ${sessionId || 'demo'}`}
              labels={{
                title: "AI Assistant",
                initial: `Hi! I'm here to help you in your conversation with ${agentName}. I can help you craft messages, analyze the conversation, or control voice settings.`,
              }}
            />
          </div>
        ) : (
          /* Regular message view */
          <div className="p-4 space-y-2">
            {allMessages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <MessageCircle className="w-12 h-12 mb-4 text-gray-300" />
                <p className="text-center">
                  Start a conversation with {agentName}
                </p>
                <p className="text-sm text-center mt-1">
                  {isVoiceActive ? 'Speak or type your message' : 'Type your message below'}
                </p>
                <button
                  onClick={() => setShowCopilotChat(true)}
                  className="mt-3 text-sm text-blue-600 hover:text-blue-700"
                >
                  Or get help from AI Assistant
                </button>
              </div>
            ) : (
              <>
                {allMessages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                  />
                ))}
                <div ref={messagesEndRef} />
              </>
            )}
          </div>
        )}
      </div>

      {/* Input Area - Only show when not in CopilotKit mode */}
      {!showCopilotChat && onSendMessage && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-end space-x-2">
            <div className="flex-1">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={isVoiceActive ? "Voice mode active - speak your message" : "Type your message..."}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500"
                rows={1}
                style={{ minHeight: '40px', maxHeight: '120px' }}
                disabled={isVoiceActive}
              />
            </div>
            
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isVoiceActive}
              className={`
                p-2 rounded-lg transition-colors
                ${inputMessage.trim() && !isVoiceActive
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
              title="Send message"
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
          
          {/* Character count for long messages */}
          {inputMessage.length > 200 && (
            <div className="text-xs text-gray-500 mt-1 text-right">
              {inputMessage.length}/500
            </div>
          )}
        </div>
      )}
    </div>
  );
};