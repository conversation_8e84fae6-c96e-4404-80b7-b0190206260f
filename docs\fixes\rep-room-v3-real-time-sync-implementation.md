# Rep Room v3 Real-Time Synchronization System Implementation

## Overview

This document describes the implementation of the real-time synchronization system for Rep Room v3, which enables seamless collaboration between multiple participants through WebSocket-based communication.

## Components Implemented

### 1. useRepRoomSync Hook (`src/hooks/useRepRoomSync.ts`)

A comprehensive React hook that manages real-time synchronization with the following features:

#### Key Features:
- **WebSocket Connection Management**: Automatic connection, disconnection, and reconnection logic
- **Message Queue**: Offline message handling with automatic retry mechanism
- **State Synchronization**: Real-time sync of participants, messages, and agent states
- **Error Handling**: Robust error handling with connection state tracking
- **Heartbeat System**: Automatic ping/pong to maintain connection health

#### Connection States:
- `disconnected`: No active connection
- `connecting`: Attempting to establish connection
- `connected`: Successfully connected and ready
- `reconnecting`: Attempting to reconnect after connection loss
- `error`: Connection failed with error

#### Event Handlers:
- `onParticipantJoined`: Triggered when a new participant joins
- `onParticipantLeft`: Triggered when a participant leaves
- `onMessageReceived`: Triggered when a new message is received
- `onStateUpdate`: Triggered when room state is updated
- `onAgentStatusChange`: Triggered when agent status changes
- `onConnectionStateChange`: Triggered when connection state changes
- `onError`: Triggered when errors occur

#### Sync Methods:
- `sendChatMessage(message)`: Send chat messages to other participants
- `updateAgentStatus(agentId, status)`: Update agent status across clients
- `syncStateUpdate(updates)`: Sync partial state updates
- `joinSession(participant)`: Notify others of participant joining
- `leaveSession(participantId)`: Notify others of participant leaving

### 2. ConnectionStatusBar Component (`src/components/rep-room/ConnectionStatusBar.tsx`)

A comprehensive status bar component that displays real-time connection information:

#### Features:
- **Connection State Indicator**: Visual indicator with color-coded status
- **Participant Count**: Shows number of online participants
- **Last Sync Timestamp**: Displays when last sync occurred
- **Error Messages**: Shows connection errors with details
- **Retry Button**: Manual reconnection option
- **Responsive Design**: Adapts to different screen sizes

#### Status Colors:
- 🟢 Green: Connected
- 🟡 Yellow: Connecting/Reconnecting
- 🔴 Red: Error
- ⚪ Gray: Disconnected

#### Status Icons:
- ✅ Checkmark: Connected
- 🔄 Spinner: Connecting/Reconnecting
- ⚠️ Warning: Error
- ❌ X: Disconnected

### 3. Enhanced RepRoomInterfaceEnhanced Component

Updated the main interface component to integrate real-time synchronization:

#### Integration Features:
- **Auto-Connect**: Automatically connects to sync server on mount
- **Message Sync**: Sends chat messages to other participants in real-time
- **Participant Sync**: Syncs participant joins/leaves across clients
- **Status Bar**: Displays connection status at the top of the interface
- **Error Handling**: Graceful handling of sync errors

#### Layout Changes:
- Added ConnectionStatusBar at the top
- Modified layout to use flex-col for vertical stacking
- Maintained responsive design for all panels

### 4. Demo WebSocket Server (`api/rep-room-sync.js`)

A demonstration WebSocket server implementation for testing:

#### Features:
- **Session Management**: Separate rooms for different sessions
- **Message Broadcasting**: Broadcasts messages to all clients in a session
- **Heartbeat Monitoring**: Ping/pong system for connection health
- **Cleanup**: Automatic cleanup of inactive connections
- **Error Handling**: Robust error handling for WebSocket operations

## Configuration

### WebSocket URL Configuration

The sync hook automatically determines the WebSocket URL based on the current protocol:

```typescript
const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/rep-room-sync`;
```

### Default Configuration

```typescript
const DEFAULT_CONFIG = {
  reconnectInterval: 3000,        // 3 seconds between reconnection attempts
  maxReconnectAttempts: 10,       // Maximum number of reconnection attempts
  heartbeatInterval: 30000,       // 30 seconds between heartbeat pings
  messageQueueSize: 100,          // Maximum number of queued messages
};
```

## Usage Examples

### Basic Usage

```typescript
import { useRepRoomSync } from '../hooks/useRepRoomSync';
import { ConnectionStatusBar } from './ConnectionStatusBar';

const MyComponent = () => {
  const sync = useRepRoomSync(
    { sessionId: 'my-session' },
    {
      onMessageReceived: (message) => {
        console.log('New message:', message);
      },
      onParticipantJoined: (participant) => {
        console.log('Participant joined:', participant);
      }
    }
  );

  useEffect(() => {
    sync.connect();
    return () => sync.disconnect();
  }, []);

  return (
    <div>
      <ConnectionStatusBar
        connectionState={sync.connectionState}
        participantCount={sync.participantCount}
        lastSyncTimestamp={sync.lastSyncTimestamp}
        reconnectAttempts={sync.reconnectAttempts}
        error={sync.error}
        onRetry={sync.reconnect}
      />
      {/* Your component content */}
    </div>
  );
};
```

### Sending Messages

```typescript
const handleSendMessage = (content: string) => {
  const message = {
    id: `msg-${Date.now()}`,
    sender: 'User',
    content,
    timestamp: new Date(),
    type: 'human' as const,
    isTyping: false
  };

  // Send via sync if connected
  if (sync.isConnected) {
    sync.sendChatMessage(message);
  }
};
```

### Updating Agent Status

```typescript
const handleAgentStatusChange = (agentId: string, status: string) => {
  // Update local state
  updateLocalAgentStatus(agentId, status);
  
  // Sync with other clients
  if (sync.isConnected) {
    sync.updateAgentStatus(agentId, status);
  }
};
```

## Message Protocol

### Message Structure

```typescript
interface WebSocketMessage {
  type: 'participant_joined' | 'participant_left' | 'message' | 'state_update' | 'agent_status' | 'ping' | 'pong';
  payload: WebSocketPayload;
  timestamp: number;
  sessionId: string;
}
```

### Message Types

1. **participant_joined**: New participant joins the session
2. **participant_left**: Participant leaves the session
3. **message**: Chat message sent by a participant
4. **state_update**: Partial state update for the room
5. **agent_status**: Agent status change notification
6. **ping/pong**: Heartbeat messages for connection health

## Error Handling

### Connection Errors

The system handles various connection scenarios:

- **Network Disconnection**: Automatic reconnection with exponential backoff
- **Server Unavailable**: Queues messages for later delivery
- **Invalid Messages**: Logs errors and continues operation
- **Timeout**: Automatic cleanup of stale connections

### Error Recovery

1. **Automatic Reconnection**: Up to 10 attempts with 3-second intervals
2. **Message Queuing**: Stores up to 100 messages for offline delivery
3. **State Reconciliation**: Syncs state when reconnection is established
4. **Manual Recovery**: Retry button for user-initiated reconnection

## Security Considerations

### Current Implementation

- Session-based isolation (participants only see their session)
- Basic message validation
- Connection timeout handling

### Production Recommendations

1. **Authentication**: Implement JWT-based authentication
2. **Authorization**: Role-based access control for sessions
3. **Rate Limiting**: Prevent message spam and DoS attacks
4. **Input Validation**: Sanitize all incoming messages
5. **SSL/TLS**: Use secure WebSocket connections (wss://)
6. **CORS**: Configure proper CORS policies

## Performance Considerations

### Optimization Features

1. **Message Batching**: Groups multiple updates into single messages
2. **Connection Pooling**: Reuses connections when possible
3. **Heartbeat Optimization**: Minimal overhead ping/pong system
4. **Memory Management**: Automatic cleanup of old messages and connections

### Scalability

For production deployment, consider:

1. **Load Balancing**: Distribute WebSocket connections across multiple servers
2. **Redis Integration**: Use Redis for session state and message broadcasting
3. **Horizontal Scaling**: Support multiple server instances
4. **Database Integration**: Persist messages and session state

## Testing

### Manual Testing

1. Open multiple browser tabs with the same session ID
2. Send messages from one tab and verify they appear in others
3. Test connection loss by disabling network
4. Verify reconnection and message queue functionality

### Automated Testing

Consider implementing:

1. **Unit Tests**: Test individual hook functions
2. **Integration Tests**: Test WebSocket message flow
3. **E2E Tests**: Test full user scenarios
4. **Load Tests**: Test with multiple concurrent connections

## Future Enhancements

### Planned Features

1. **Voice Sync**: Synchronize voice call states
2. **Screen Sharing**: Real-time screen sharing coordination
3. **File Sharing**: Collaborative file sharing
4. **Presence Indicators**: Show typing indicators and user activity
5. **Message History**: Persistent message storage and retrieval

### Technical Improvements

1. **Binary Protocol**: Use binary messages for better performance
2. **Compression**: Compress large messages
3. **Encryption**: End-to-end encryption for sensitive data
4. **Offline Support**: Enhanced offline capabilities with local storage

## Troubleshooting

### Common Issues

1. **Connection Fails**: Check WebSocket server availability
2. **Messages Not Syncing**: Verify session ID consistency
3. **High Latency**: Check network conditions and server load
4. **Memory Leaks**: Ensure proper cleanup on component unmount

### Debug Tools

1. **Browser DevTools**: Monitor WebSocket connections in Network tab
2. **Console Logs**: Enable detailed logging for debugging
3. **Connection Status**: Use ConnectionStatusBar for real-time status
4. **Message Inspector**: Log all incoming/outgoing messages

## Conclusion

The Rep Room v3 real-time synchronization system provides a robust foundation for collaborative features. The modular design allows for easy extension and customization while maintaining performance and reliability.

The implementation follows React best practices and provides comprehensive error handling, making it suitable for production use with proper security and scalability considerations.