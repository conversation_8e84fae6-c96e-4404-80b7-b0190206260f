import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useLocalParticipant, useRemoteParticipants } from '@livekit/components-react';
import type { Participant, TrackPublication } from 'livekit-client';

export interface AudioVisualizerProps {
  className?: string;
  showSpeakerNames?: boolean;
  maxBars?: number;
  barWidth?: number;
  barSpacing?: number;
  height?: number;
  smoothingTimeConstant?: number;
  fftSize?: number;
}

interface ParticipantAudioData {
  participant: Participant;
  analyser: AnalyserNode | null;
  dataArray: Uint8Array | null;
  audioLevel: number;
  isActive: boolean;
  color: string;
}

export const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  className = '',
  showSpeakerNames = true,
  maxBars = 32,
  barWidth = 4,
  barSpacing = 2,
  height = 120,
  smoothingTimeConstant = 0.8,
  fftSize = 256
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const audioContextRef = useRef<AudioContext>();
  const participantDataRef = useRef<Map<string, ParticipantAudioData>>(new Map());
  
  // Use hooks with error handling
  let localParticipant = null;
  let remoteParticipants: Participant[] = [];
  let hasLiveKitContext = true;
  
  try {
    const localParticipantHook = useLocalParticipant();
    const remoteParticipantsHook = useRemoteParticipants();
    localParticipant = localParticipantHook.localParticipant;
    remoteParticipants = remoteParticipantsHook;
  } catch (error) {
    console.warn('[AudioVisualizer] LiveKit context not available, showing fallback UI');
    hasLiveKitContext = false;
  }
  
  const [activeSpeakers, setActiveSpeakers] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Color palette for different participants
  const colors = [
    '#3b82f6', // Blue
    '#10b981', // Green
    '#f59e0b', // Yellow
    '#ef4444', // Red
    '#8b5cf6', // Purple
    '#06b6d4', // Cyan
    '#f97316', // Orange
    '#84cc16', // Lime
  ];

  const getParticipantColor = useCallback((participantId: string): string => {
    const index = Array.from(participantDataRef.current.keys()).indexOf(participantId);
    return colors[index % colors.length];
  }, []);

  // Initialize audio context and analyzers for participants
  const initializeAudioAnalysis = useCallback(async (participant: Participant) => {
    try {
      console.log('🎵 AudioVisualizer: Initializing analysis for participant:', participant.identity);
      
      // Get audio track
      const audioTrackPubs = Array.from(participant.audioTrackPublications.values());
      const audioTrack = audioTrackPubs.find(pub => pub.track)?.track;
      
      if (!audioTrack || !audioTrack.mediaStreamTrack) {
        console.log('🎵 AudioVisualizer: No valid audio track for participant:', participant.identity);
        return null;
      }

      // Create or get audio context
      if (!audioContextRef.current) {
        const AudioContextClass = window.AudioContext || (window as typeof window & { webkitAudioContext: typeof AudioContext }).webkitAudioContext;
        audioContextRef.current = new AudioContextClass();
      }

      const audioContext = audioContextRef.current;
      const analyser = audioContext.createAnalyser();
      
      analyser.fftSize = fftSize;
      analyser.smoothingTimeConstant = smoothingTimeConstant;
      
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      // Connect audio track to analyser
      const mediaStream = new MediaStream([audioTrack.mediaStreamTrack]);
      const source = audioContext.createMediaStreamSource(mediaStream);
      source.connect(analyser);

      const participantData: ParticipantAudioData = {
        participant,
        analyser,
        dataArray,
        audioLevel: 0,
        isActive: false,
        color: getParticipantColor(participant.sid)
      };

      participantDataRef.current.set(participant.sid, participantData);
      
      console.log('✅ AudioVisualizer: Analysis initialized for participant:', participant.identity);
      return participantData;
    } catch (error) {
      console.error('❌ AudioVisualizer: Error initializing analysis for participant:', participant.identity, error);
      return null;
    }
  }, [fftSize, smoothingTimeConstant, getParticipantColor]);

  // Initialize all participants
  useEffect(() => {
    if (!hasLiveKitContext) return;
    
    const initializeAllParticipants = async () => {
      const allParticipants = [localParticipant, ...remoteParticipants].filter(Boolean);
      
      for (const participant of allParticipants) {
        if (!participantDataRef.current.has(participant.sid)) {
          await initializeAudioAnalysis(participant);
        }
      }
      
      setIsInitialized(true);
    };

    // Small delay to allow tracks to be ready
    const timeoutId = setTimeout(initializeAllParticipants, 1000);
    
    return () => clearTimeout(timeoutId);
  }, [localParticipant, remoteParticipants, initializeAudioAnalysis, hasLiveKitContext]);

  // Clean up removed participants
  useEffect(() => {
    if (!hasLiveKitContext) return;
    
    const currentParticipantIds = new Set([
      localParticipant?.sid,
      ...remoteParticipants.map(p => p.sid)
    ].filter(Boolean));

    Array.from(participantDataRef.current.entries()).forEach(([participantId, data]) => {
      if (!currentParticipantIds.has(participantId)) {
        console.log('🎵 AudioVisualizer: Cleaning up participant:', data.participant.identity);
        participantDataRef.current.delete(participantId);
      }
    });
  }, [localParticipant, remoteParticipants, hasLiveKitContext]);

  // Animation loop
  useEffect(() => {
    if (!hasLiveKitContext || !isInitialized) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const draw = () => {
      const width = canvas.width;
      const height = canvas.height;
      
      // Clear canvas with dark background
      ctx.fillStyle = '#1f2937';
      ctx.fillRect(0, 0, width, height);

      const participantData = Array.from(participantDataRef.current.values());
      if (participantData.length === 0) {
        animationRef.current = requestAnimationFrame(draw);
        return;
      }

      // Update audio levels and detect active speakers
      const currentActiveSpeakers: string[] = [];
      
      participantData.forEach(data => {
        if (!data.analyser || !data.dataArray) return;

        data.analyser.getByteFrequencyData(data.dataArray);
        
        // Calculate average audio level
        const sum = data.dataArray.reduce((acc, val) => acc + val, 0);
        const average = sum / data.dataArray.length;
        data.audioLevel = average;
        
        // Determine if participant is actively speaking (threshold: 20)
        data.isActive = average > 20;
        
        if (data.isActive) {
          currentActiveSpeakers.push(data.participant.sid);
        }
      });

      // Update active speakers state
      setActiveSpeakers(prev => {
        const newSpeakers = currentActiveSpeakers.sort();
        const prevSpeakers = prev.sort();
        if (JSON.stringify(newSpeakers) !== JSON.stringify(prevSpeakers)) {
          return currentActiveSpeakers;
        }
        return prev;
      });

      // Draw visualizations
      const totalWidth = width;
      const participantWidth = totalWidth / Math.max(participantData.length, 1);
      
      participantData.forEach((data, index) => {
        if (!data.analyser || !data.dataArray) return;

        const startX = index * participantWidth;
        const centerX = startX + participantWidth / 2;
        
        // Draw participant name if enabled
        if (showSpeakerNames) {
          ctx.fillStyle = data.isActive ? '#ffffff' : '#9ca3af';
          ctx.font = '12px Inter, sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(
            data.participant.identity || 'Unknown',
            centerX,
            height - 5
          );
        }

        // Calculate bars to display
        const barsToShow = Math.min(maxBars, Math.floor((participantWidth - 20) / (barWidth + barSpacing)));
        const startBarX = centerX - (barsToShow * (barWidth + barSpacing)) / 2;
        
        // Draw frequency bars
        const step = Math.floor(data.dataArray.length / barsToShow);
        
        for (let i = 0; i < barsToShow; i++) {
          const dataIndex = i * step;
          const value = data.dataArray[dataIndex] || 0;
          const barHeight = (value / 255) * (height - 30); // Leave space for names
          
          const x = startBarX + i * (barWidth + barSpacing);
          const y = height - barHeight - (showSpeakerNames ? 20 : 5);
          
          // Create gradient based on activity and participant color
          const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight);
          
          if (data.isActive) {
            gradient.addColorStop(0, data.color);
            gradient.addColorStop(0.5, data.color + '80');
            gradient.addColorStop(1, data.color + '40');
          } else {
            gradient.addColorStop(0, '#4b5563');
            gradient.addColorStop(1, '#374151');
          }
          
          ctx.fillStyle = gradient;
          ctx.fillRect(x, y, barWidth, barHeight);
          
          // Add glow effect for active speakers
          if (data.isActive && barHeight > 10) {
            ctx.shadowColor = data.color;
            ctx.shadowBlur = 8;
            ctx.fillRect(x, y, barWidth, barHeight);
            ctx.shadowBlur = 0;
          }
        }

        // Draw voice level indicator
        const levelHeight = 4;
        const levelWidth = participantWidth - 20;
        const levelX = startX + 10;
        const levelY = height - (showSpeakerNames ? 35 : 15);
        
        // Background
        ctx.fillStyle = '#374151';
        ctx.fillRect(levelX, levelY, levelWidth, levelHeight);
        
        // Level indicator
        const levelFill = (data.audioLevel / 255) * levelWidth;
        ctx.fillStyle = data.isActive ? data.color : '#6b7280';
        ctx.fillRect(levelX, levelY, levelFill, levelHeight);
      });

      animationRef.current = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isInitialized, showSpeakerNames, maxBars, barWidth, barSpacing, height, hasLiveKitContext]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // If no LiveKit context, show fallback UI
  if (!hasLiveKitContext) {
    return (
      <div className={`audio-visualizer bg-gray-800 rounded-lg p-4 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gray-500 rounded-full" />
            <span className="text-sm font-medium text-gray-400">
              Audio Activity (Disconnected)
            </span>
          </div>
        </div>

        {/* Canvas */}
        <div className="relative overflow-x-auto">
          <div
            className="bg-gray-900 rounded border border-gray-700 flex items-center justify-center"
            style={{ height: `${height}px`, minWidth: '100%' }}
          >
            <div className="text-center text-gray-500">
              <div className="text-sm mb-1">Voice not connected</div>
              <div className="text-xs">Connect to see audio visualization</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const canvasWidth = Math.max(400, (localParticipant ? 1 : 0) + remoteParticipants.length) * 150;

  return (
    <div className={`audio-visualizer bg-gray-800 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm font-medium text-gray-200">
            Audio Activity
          </span>
        </div>
        
        {activeSpeakers.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-400">
              {activeSpeakers.length} speaking
            </span>
            <div className="flex space-x-1">
              {activeSpeakers.slice(0, 3).map((speakerId, index) => {
                const data = participantDataRef.current.get(speakerId);
                return (
                  <div
                    key={speakerId}
                    className="w-2 h-2 rounded-full animate-pulse"
                    style={{ backgroundColor: data?.color || '#10b981' }}
                  />
                );
              })}
              {activeSpeakers.length > 3 && (
                <span className="text-xs text-gray-400">+{activeSpeakers.length - 3}</span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Canvas */}
      <div className="relative overflow-x-auto">
        <canvas
          ref={canvasRef}
          width={canvasWidth}
          height={height}
          className="bg-gray-900 rounded border border-gray-700"
          style={{ minWidth: '100%' }}
        />
        
        {!isInitialized && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75 rounded">
            <div className="text-center">
              <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2" />
              <div className="text-gray-400 text-sm">Initializing audio analysis...</div>
            </div>
          </div>
        )}
      </div>

      {/* Participant Legend */}
      {showSpeakerNames && participantDataRef.current.size > 0 && (
        <div className="mt-3 flex flex-wrap gap-2">
          {Array.from(participantDataRef.current.values()).map(data => (
            <div
              key={data.participant.sid}
              className={`flex items-center space-x-2 px-2 py-1 rounded text-xs ${
                data.isActive 
                  ? 'bg-gray-700 text-white' 
                  : 'bg-gray-800 text-gray-400'
              }`}
            >
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: data.color }}
              />
              <span>{data.participant.identity || 'Unknown'}</span>
              {data.isActive && (
                <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};