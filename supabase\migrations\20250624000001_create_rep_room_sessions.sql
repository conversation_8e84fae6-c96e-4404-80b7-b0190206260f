-- Create rep_room_sessions table and related tables
-- Migration: 20250624000001_create_rep_room_sessions.sql
-- Description: Creates the rep_room_sessions table for session management and tracking

-- Create rep_room_sessions table
CREATE TABLE IF NOT EXISTS public.rep_room_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    slug TEXT NOT NULL,
    session_id TEXT NOT NULL UNIQUE,
    room_name TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'ended', 'paused', 'error')),
    participant_count INTEGER DEFAULT 0,
    agent_joined BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create rep_room_participants table
CREATE TABLE IF NOT EXISTS public.rep_room_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id TEXT NOT NULL REFERENCES rep_room_sessions(session_id) ON DELETE CASCADE,
    participant_id TEXT NOT NULL,
    name TEXT,
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    UNIQUE(session_id, participant_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_rep_room_sessions_slug ON public.rep_room_sessions(slug);
CREATE INDEX IF NOT EXISTS idx_rep_room_sessions_session_id ON public.rep_room_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_rep_room_sessions_status ON public.rep_room_sessions(status);
CREATE INDEX IF NOT EXISTS idx_rep_room_sessions_created_at ON public.rep_room_sessions(created_at);

CREATE INDEX IF NOT EXISTS idx_rep_room_participants_session_id ON public.rep_room_participants(session_id);
CREATE INDEX IF NOT EXISTS idx_rep_room_participants_participant_id ON public.rep_room_participants(participant_id);
CREATE INDEX IF NOT EXISTS idx_rep_room_participants_is_active ON public.rep_room_participants(is_active);

-- Enable RLS on both tables
ALTER TABLE public.rep_room_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rep_room_participants ENABLE ROW LEVEL SECURITY;

-- RLS Policies for rep_room_sessions

-- Policy: Service role has full access to sessions
CREATE POLICY "Service role has full access to sessions"
ON public.rep_room_sessions
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- Policy: Anonymous users can read public sessions (for t1 slug)
CREATE POLICY "Anonymous users can read public sessions"
ON public.rep_room_sessions
FOR SELECT
TO anon
USING (slug = 't1' AND status = 'active');

-- Policy: Authenticated users can read sessions
CREATE POLICY "Authenticated users can read sessions"
ON public.rep_room_sessions
FOR SELECT
TO authenticated
USING (true);

-- Policy: Authenticated users can create sessions for public rep rooms
CREATE POLICY "Authenticated users can create public sessions"
ON public.rep_room_sessions
FOR INSERT
TO authenticated
WITH CHECK (slug = 't1');

-- Policy: Authenticated users can update sessions they participate in
CREATE POLICY "Authenticated users can update participating sessions"
ON public.rep_room_sessions
FOR UPDATE
TO authenticated
USING (
    session_id IN (
        SELECT session_id 
        FROM public.rep_room_participants 
        WHERE participant_id = auth.uid()::text
    )
)
WITH CHECK (
    session_id IN (
        SELECT session_id 
        FROM public.rep_room_participants 
        WHERE participant_id = auth.uid()::text
    )
);

-- RLS Policies for rep_room_participants

-- Policy: Service role has full access to participants
CREATE POLICY "Service role has full access to participants"
ON public.rep_room_participants
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- Policy: Anonymous users can read participants for public sessions
CREATE POLICY "Anonymous users can read public session participants"
ON public.rep_room_participants
FOR SELECT
TO anon
USING (
    session_id IN (
        SELECT session_id 
        FROM public.rep_room_sessions 
        WHERE slug = 't1' AND status = 'active'
    )
);

-- Policy: Authenticated users can read all participants
CREATE POLICY "Authenticated users can read participants"
ON public.rep_room_participants
FOR SELECT
TO authenticated
USING (true);

-- Policy: Authenticated users can create participant records
CREATE POLICY "Authenticated users can create participant records"
ON public.rep_room_participants
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Policy: Authenticated users can update their own participant records
CREATE POLICY "Authenticated users can update own participant records"
ON public.rep_room_participants
FOR UPDATE
TO authenticated
USING (participant_id = auth.uid()::text)
WITH CHECK (participant_id = auth.uid()::text);

-- Add table comments
COMMENT ON TABLE public.rep_room_sessions IS 'Tracks Rep Room sessions for session management and persistence';
COMMENT ON TABLE public.rep_room_participants IS 'Tracks participants in Rep Room sessions';

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_rep_room_sessions_updated_at 
    BEFORE UPDATE ON public.rep_room_sessions 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
