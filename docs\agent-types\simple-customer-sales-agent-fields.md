# Simple Customer-Facing Sales Agent Fields

A simplified customer-facing sales consultant agent type with copy-paste ready field values for database insertion. This agent leverages customer success stories, testimonials, reviews, and product specifications to build trust and drive sales.

## Agent Overview

This sales consultant agent is designed to:
- Build trust through customer success stories with visuals
- Share testimonial videos (transcribed content)
- Reference Google and Yelp reviews
- Provide detailed product/service specifications
- Access customer profile information via UUID from CRM
- Focus on core sales functions with simple configuration

## Copy-Paste Ready Database Fields

### Basic Agent Information

```json
{
  "id": "simpleCustomerSalesAgent",
  "name": "Customer Sales Consultant",
  "description": "AI sales consultant that builds trust through customer success stories, testimonials, and reviews while providing product expertise",
  "version": "1.0.0",
  "status": "published",
  "category": "sales_support",
  "is_public": false,
  "agent_type": "task_specific",
  "mastra_agent_id": "customer-sales-consultant-v1",
  "mastra_api_base_url": "https://mastra-runtime.example.com"
}
```

### Complete Configuration Object

```json
{
  "configuration_schema": {
    "type": "object",
    "properties": {
      "company_name": {
        "type": "string",
        "title": "Company Name",
        "description": "Your company name for personalized sales conversations",
        "default": ""
      },
      "industry": {
        "type": "string",
        "title": "Industry",
        "description": "Your company's industry sector",
        "enum": ["home_services", "technology", "healthcare", "finance", "retail", "real_estate", "automotive", "other"],
        "default": "home_services"
      },
      "primary_services": {
        "type": "array",
        "title": "Primary Services/Products",
        "description": "Main services or products you offer",
        "items": {
          "type": "string"
        },
        "maxItems": 10,
        "default": []
      },
      "service_area": {
        "type": "string",
        "title": "Service Area",
        "description": "Geographic area you serve (e.g., 'Greater Chicago Area', 'Nationwide')",
        "default": ""
      },
      "contact_phone": {
        "type": "string",
        "title": "Contact Phone",
        "description": "Primary phone number for customer contact",
        "pattern": "^[\\+]?[1-9]?[0-9]{7,15}$",
        "default": ""
      },
      "contact_email": {
        "type": "string",
        "title": "Contact Email",
        "description": "Primary email for customer inquiries",
        "format": "email",
        "default": ""
      },
      "business_hours": {
        "type": "string",
        "title": "Business Hours",
        "description": "Operating hours (e.g., 'Mon-Fri 8AM-6PM EST')",
        "default": "Mon-Fri 9AM-5PM"
      },
      "crm_integration": {
        "type": "object",
        "title": "CRM Integration",
        "description": "Customer relationship management system settings",
        "properties": {
          "enabled": {
            "type": "boolean",
            "title": "Enable CRM Integration",
            "default": true
          },
          "customer_lookup_endpoint": {
            "type": "string",
            "title": "Customer Lookup API Endpoint",
            "description": "API endpoint to fetch customer profile by UUID",
            "default": ""
          },
          "api_key_name": {
            "type": "string",
            "title": "API Key Header Name",
            "default": "X-API-Key"
          }
        },
        "required": ["enabled"]
      }
    },
    "required": ["company_name", "industry", "contact_phone", "contact_email"]
  },
  "runtime_context_schema": {
    "type": "object",
    "properties": {
      "customer_uuid": {
        "type": "string",
        "description": "UUID of the customer for CRM profile lookup",
        "format": "uuid"
      },
      "customer_profile": {
        "type": "object",
        "description": "Customer profile data from CRM",
        "properties": {
          "name": {"type": "string"},
          "email": {"type": "string"},
          "phone": {"type": "string"},
          "address": {"type": "string"},
          "previous_projects": {"type": "array", "items": {"type": "object"}},
          "preferences": {"type": "object"},
          "budget_range": {"type": "string"},
          "timeline": {"type": "string"}
        }
      },
      "session_context": {
        "type": "object",
        "properties": {
          "inquiry_type": {
            "type": "string",
            "enum": ["quote_request", "general_inquiry", "follow_up", "complaint", "referral"]
          },
          "lead_source": {
            "type": "string",
            "enum": ["website", "google", "referral", "social_media", "advertisement", "other"]
          },
          "urgency_level": {
            "type": "string",
            "enum": ["low", "medium", "high", "urgent"]
          }
        }
      }
    }
  },
  "knowledge_source_config_schema": {
    "type": "object",
    "properties": {
      "success_stories": {
        "type": "array",
        "title": "Customer Success Stories",
        "description": "Success stories with before/after photos and project details",
        "items": {
          "type": "object",
          "properties": {
            "title": {"type": "string"},
            "customer_name": {"type": "string"},
            "project_type": {"type": "string"},
            "description": {"type": "string"},
            "before_photo_url": {"type": "string", "format": "uri"},
            "after_photo_url": {"type": "string", "format": "uri"},
            "project_value": {"type": "string"},
            "completion_time": {"type": "string"},
            "customer_quote": {"type": "string"}
          }
        }
      },
      "testimonial_videos": {
        "type": "array",
        "title": "Testimonial Videos",
        "description": "Video testimonials with transcribed content",
        "items": {
          "type": "object",
          "properties": {
            "customer_name": {"type": "string"},
            "video_url": {"type": "string", "format": "uri"},
            "transcript": {"type": "string"},
            "project_type": {"type": "string"},
            "rating": {"type": "number", "minimum": 1, "maximum": 5},
            "date_recorded": {"type": "string", "format": "date"}
          }
        }
      },
      "google_reviews": {
        "type": "array",
        "title": "Google Reviews",
        "description": "Recent Google Business reviews",
        "items": {
          "type": "object",
          "properties": {
            "reviewer_name": {"type": "string"},
            "rating": {"type": "number", "minimum": 1, "maximum": 5},
            "review_text": {"type": "string"},
            "date": {"type": "string", "format": "date"},
            "project_type": {"type": "string"},
            "verified": {"type": "boolean"}
          }
        }
      },
      "yelp_reviews": {
        "type": "array",
        "title": "Yelp Reviews",
        "description": "Recent Yelp business reviews",
        "items": {
          "type": "object",
          "properties": {
            "reviewer_name": {"type": "string"},
            "rating": {"type": "number", "minimum": 1, "maximum": 5},
            "review_text": {"type": "string"},
            "date": {"type": "string", "format": "date"},
            "project_type": {"type": "string"},
            "photos": {"type": "array", "items": {"type": "string", "format": "uri"}}
          }
        }
      },
      "product_specifications": {
        "type": "array",
        "title": "Product/Service Specifications",
        "description": "Detailed specifications for products and services",
        "items": {
          "type": "object",
          "properties": {
            "name": {"type": "string"},
            "category": {"type": "string"},
            "description": {"type": "string"},
            "features": {"type": "array", "items": {"type": "string"}},
            "benefits": {"type": "array", "items": {"type": "string"}},
            "price_range": {"type": "string"},
            "warranty": {"type": "string"},
            "installation_time": {"type": "string"},
            "maintenance_requirements": {"type": "string"},
            "certifications": {"type": "array", "items": {"type": "string"}},
            "technical_specs": {"type": "object"}
          }
        }
      }
    }
  },
  "default_config_values": {
    "company_name": "",
    "industry": "home_services",
    "primary_services": [],
    "service_area": "",
    "contact_phone": "",
    "contact_email": "",
    "business_hours": "Mon-Fri 9AM-5PM",
    "crm_integration": {
      "enabled": true,
      "customer_lookup_endpoint": "",
      "api_key_name": "X-API-Key"
    }
  },
  "presentation_config": {
    "avatar": {
      "type": "professional",
      "style": "sales_consultant",
      "color_scheme": "trust_blue"
    },
    "personality": {
      "tone": "friendly_professional",
      "communication_style": "consultative",
      "expertise_level": "experienced_advisor"
    },
    "interface_elements": {
      "show_success_stories": true,
      "display_reviews": true,
      "include_product_specs": true,
      "show_contact_info": true
    }
  },
  "available_channels": ["chat", "voice", "rep_room"],
  "avatar_type": "sales_consultant",
  "capabilities": [
    "customer_consultation",
    "product_recommendation",
    "quote_generation",
    "appointment_scheduling",
    "objection_handling",
    "trust_building",
    "crm_integration"
  ],
  "agent_operational_mode": "interactive_user_facing"
}
```

### Voice Configuration

```json
{
  "voice_config": {
    "enabled": true,
    "voice_id": "professional_sales_consultant",
    "speech_rate": 1.0,
    "pitch": 0.0,
    "volume": 0.8,
    "ssml_enabled": true
  }
}
```

### Chat UI Settings

```json
{
  "chat_ui_settings": {
    "welcome_message": "Hello! I'm your sales consultant. I'd love to help you explore our services and share some amazing success stories from customers just like you. What project are you considering?",
    "placeholder_text": "Tell me about your project or ask about our services...",
    "suggested_prompts": [
      "Show me some recent customer success stories",
      "What services do you offer in my area?",
      "Can you share customer reviews for kitchen remodels?",
      "I'd like to see product specifications",
      "What's your pricing for bathroom renovations?"
    ]
  }
}
```

### Pricing Configuration

```json
{
  "pricing": {
    "credits_per_completion_token": 0.002,
    "credits_per_prompt_token": 0.001
  }
}
```

## Example Knowledge Source Data

### Sample Success Stories

```json
[
  {
    "title": "Complete Kitchen Transformation",
    "customer_name": "Sarah & Mike Johnson",
    "project_type": "Kitchen Remodel",
    "description": "Complete kitchen renovation including custom cabinets, granite countertops, and modern appliances",
    "before_photo_url": "https://example.com/photos/kitchen-before-1.jpg",
    "after_photo_url": "https://example.com/photos/kitchen-after-1.jpg",
    "project_value": "$45,000",
    "completion_time": "3 weeks",
    "customer_quote": "The team exceeded our expectations! Our kitchen is now the heart of our home."
  },
  {
    "title": "Luxury Bathroom Renovation",
    "customer_name": "Robert & Lisa Chen",
    "project_type": "Bathroom Remodel",
    "description": "Master bathroom renovation with walk-in shower, heated floors, and custom vanity",
    "before_photo_url": "https://example.com/photos/bathroom-before-1.jpg",
    "after_photo_url": "https://example.com/photos/bathroom-after-1.jpg",
    "project_value": "$28,000",
    "completion_time": "2 weeks",
    "customer_quote": "Professional work, on time, and within budget. Highly recommend!"
  }
]
```

### Sample Testimonial Videos

```json
[
  {
    "customer_name": "Jennifer Martinez",
    "video_url": "https://example.com/videos/testimonial-1.mp4",
    "transcript": "I was nervous about renovating my kitchen, but the team made the whole process so easy. They showed me examples of their previous work, and I knew I was in good hands. The result is absolutely beautiful - exactly what I dreamed of!",
    "project_type": "Kitchen Remodel",
    "rating": 5,
    "date_recorded": "2024-11-15"
  },
  {
    "customer_name": "David Thompson",
    "video_url": "https://example.com/videos/testimonial-2.mp4",
    "transcript": "From the initial consultation to the final walkthrough, everything was professional and high-quality. They finished ahead of schedule and the craftsmanship is outstanding. My bathroom looks like something from a luxury hotel!",
    "project_type": "Bathroom Remodel",
    "rating": 5,
    "date_recorded": "2024-12-02"
  }
]
```

### Sample Google Reviews

```json
[
  {
    "reviewer_name": "Amanda K.",
    "rating": 5,
    "review_text": "Exceptional work on our kitchen remodel! The team was professional, clean, and finished on time. The quality of work exceeded our expectations. Highly recommend for anyone considering a renovation.",
    "date": "2024-12-10",
    "project_type": "Kitchen Remodel",
    "verified": true
  },
  {
    "reviewer_name": "Mark S.",
    "rating": 5,
    "review_text": "Outstanding bathroom renovation! From design to completion, the process was smooth and stress-free. The attention to detail is remarkable. Our bathroom is now our favorite room in the house!",
    "date": "2024-12-05",
    "project_type": "Bathroom Remodel",
    "verified": true
  }
]
```

### Sample Product Specifications

```json
[
  {
    "name": "Premium Kitchen Cabinet Package",
    "category": "Kitchen Cabinets",
    "description": "Custom solid wood cabinets with soft-close hinges and full-extension drawers",
    "features": [
      "Solid hardwood construction",
      "Soft-close hinges and drawer slides",
      "Full-extension drawers",
      "Custom sizing available",
      "Multiple finish options"
    ],
    "benefits": [
      "Durable and long-lasting",
      "Quiet operation",
      "Maximum storage access",
      "Perfect fit for any space",
      "Matches any design style"
    ],
    "price_range": "$8,000 - $25,000",
    "warranty": "10-year limited warranty",
    "installation_time": "3-5 days",
    "maintenance_requirements": "Annual cleaning and hardware check",
    "certifications": ["KCMA Certified", "FSC Certified Wood"],
    "technical_specs": {
      "material": "Solid maple or oak",
      "finish": "Catalyzed conversion varnish",
      "hardware": "Blum soft-close mechanisms"
    }
  }
]
```

## Usage Examples

### How the Agent Uses Success Stories

```markdown
**Agent Response Example:**
"I'd love to show you a recent kitchen project we completed for Sarah & Mike Johnson. They had a similar space to yours, and we transformed their outdated kitchen into a modern, functional space they absolutely love. 

[Shows before/after photos]

Sarah told us: 'The team exceeded our expectations! Our kitchen is now the heart of our home.' The project took 3 weeks and included custom cabinets, granite countertops, and modern appliances for $45,000.

Would you like to see more examples or discuss what we could do for your kitchen?"
```

### How the Agent References Reviews

```markdown
**Agent Response Example:**
"Our customers consistently rate us 5 stars! Just last week, Amanda K. wrote: 'Exceptional work on our kitchen remodel! The team was professional, clean, and finished on time. The quality of work exceeded our expectations.'

We have over 150 five-star reviews on Google and Yelp. Would you like me to share reviews specifically about the type of project you're considering?"
```

### How the Agent Accesses Customer Profiles

```markdown
**Agent Response Example:**
"Hi John! I see from your profile that you've been considering a bathroom renovation and prefer modern designs. Based on your previous inquiries about walk-in showers, I think you'd love to see our recent luxury bathroom project for the Chen family.

[Shows relevant success story and specifications]

Given your timeline of starting in spring and your budget range, I can recommend some perfect options for you."
```

## Simple JSON Configuration for Database Insert

```sql
INSERT INTO agents (
  id,
  name,
  description,
  version,
  status,
  category,
  is_public,
  agent_type,
  mastra_agent_id,
  mastra_api_base_url,
  configuration,
  voice_config,
  chat_ui_settings,
  pricing,
  created_by_org_id
) VALUES (
  'simpleCustomerSalesAgent',
  'Customer Sales Consultant',
  'AI sales consultant that builds trust through customer success stories, testimonials, and reviews while providing product expertise',
  '1.0.0',
  'published',
  'sales_support',
  false,
  'task_specific',
  'customer-sales-consultant-v1',
  'https://mastra-runtime.example.com',
  '{"configuration_schema":{"type":"object","properties":{"company_name":{"type":"string","title":"Company Name","description":"Your company name for personalized sales conversations","default":""},"industry":{"type":"string","title":"Industry","description":"Your company''s industry sector","enum":["home_services","technology","healthcare","finance","retail","real_estate","automotive","other"],"default":"home_services"},"primary_services":{"type":"array","title":"Primary Services/Products","description":"Main services or products you offer","items":{"type":"string"},"maxItems":10,"default":[]},"service_area":{"type":"string","title":"Service Area","description":"Geographic area you serve","default":""},"contact_phone":{"type":"string","title":"Contact Phone","description":"Primary phone number for customer contact","pattern":"^[\\+]?[1-9]?[0-9]{7,15}$","default":""},"contact_email":{"type":"string","title":"Contact Email","description":"Primary email for customer inquiries","format":"email","default":""},"business_hours":{"type":"string","title":"Business Hours","description":"Operating hours","default":"Mon-Fri 9AM-5PM"},"crm_integration":{"type":"object","title":"CRM Integration","description":"Customer relationship management system settings","properties":{"enabled":{"type":"boolean","title":"Enable CRM Integration","default":true},"customer_lookup_endpoint":{"type":"string","title":"Customer Lookup API Endpoint","description":"API endpoint to fetch customer profile by UUID","default":""},"api_key_name":{"type":"string","title":"API Key Header Name","default":"X-API-Key"}},"required":["enabled"]}},"required":["company_name","industry","contact_phone","contact_email"]},"default_config_values":{"company_name":"","industry":"home_services","primary_services":[],"service_area":"","contact_phone":"","contact_email":"","business_hours":"Mon-Fri 9AM-5PM","crm_integration":{"enabled":true,"customer_lookup_endpoint":"","api_key_name":"X-API-Key"}},"available_channels":["chat","voice","rep_room"],"capabilities":["customer_consultation","product_recommendation","quote_generation","appointment_scheduling","objection_handling","trust_building","crm_integration"],"agent_operational_mode":"interactive_user_facing"}',
  '{"enabled":true,"voice_id":"professional_sales_consultant","speech_rate":1.0,"pitch":0.0,"volume":0.8,"ssml_enabled":true}',
  '{"welcome_message":"Hello! I''m your sales consultant. I''d love to help you explore our services and share some amazing success stories from customers just like you. What project are you considering?","placeholder_text":"Tell me about your project or ask about our services...","suggested_prompts":["Show me some recent customer success stories","What services do you offer in my area?","Can you share customer reviews for kitchen remodels?","I''d like to see product specifications","What''s your pricing for bathroom renovations?"]}',
  '{"credits_per_completion_token":0.002,"credits_per_prompt_token":0.001}',
  'house-org-uuid'
);
```

## Key Features

1. **Trust Building Content Types**:
   - Customer success stories with before/after photos
   - Transcribed testimonial videos
   - Google and Yelp reviews integration
   - Detailed product/service specifications

2. **CRM Integration**:
   - Customer profile lookup via UUID
   - Personalized conversations based on customer data
   - Previous project history access

3. **Simple Configuration**:
   - Essential fields only for quick setup
   - Industry-specific defaults
   - Clear validation rules

4. **Sales-Focused Capabilities**:
   - Consultative selling approach
   - Objection handling
   - Quote generation support
   - Appointment scheduling

This simplified agent type provides all the essential functionality for customer-facing sales while maintaining the flexibility to customize for specific business needs.