import React from 'react';
import { AgentStatus, HumanStatus, STATUS_COLORS, STATUS_ANIMATIONS } from '../../types/rep-room';

type AllStatusTypes = AgentStatus | HumanStatus | 'online' | 'offline';

interface StatusIndicatorProps {
  status: AllStatusTypes;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  className = '',
  showLabel = false
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const baseClasses = 'rounded-full flex-shrink-0';
  const colorClass = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || STATUS_COLORS.idle;
  const animationClass = STATUS_ANIMATIONS[status as keyof typeof STATUS_ANIMATIONS] || '';
  
  const getStatusLabel = (status: AllStatusTypes): string => {
    switch (status) {
      case 'speaking': return 'Speaking';
      case 'thinking': return 'Thinking';
      case 'working': return 'Working';
      case 'delegating': return 'Delegating';
      case 'ready': return 'Ready';
      case 'idle': return 'Idle';
      case 'talking': return 'Talking';
      case 'listening': return 'Listening';
      case 'hand-up': return 'Hand Up';
      case 'online': return 'Online';
      case 'offline': return 'Offline';
      default: return 'Unknown';
    }
  };
  
  if (showLabel) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div
          className={`${baseClasses} ${sizeClasses[size]} ${colorClass} ${animationClass}`}
          aria-label={`Status: ${status}`}
          role="status"
        />
        <span className="text-xs text-gray-600 capitalize">
          {getStatusLabel(status)}
        </span>
      </div>
    );
  }
  
  return (
    <div
      className={`${baseClasses} ${sizeClasses[size]} ${colorClass} ${animationClass} ${className}`}
      aria-label={`Status: ${status}`}
      role="status"
      title={getStatusLabel(status)}
    />
  );
};