import React, { useState, useEffect } from 'react';
import { Co<PERSON>lotKit } from '@copilotkit/react-core';
import { CopilotChat } from '@copilotkit/react-ui';
import '@copilotkit/react-ui/styles.css';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, X } from 'lucide-react';

interface CloneCopilotKitChatProps {
  cloneId: string;
  cloneName: string;
  agentType?: string;
  className?: string;
  onClose?: () => void;
}

/**
 * CopilotKit-based chat interface for user clones
 * Uses the same pattern as the working test implementation
 */
export function CloneCopilotKitChat({
  cloneId,
  cloneName,
  agentType = 'keywordResearchAgent',
  className = '',
  onClose
}: CloneCopilotKitChatProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState('⏳ Testing...');

  // Configuration based on the working test
  const CONFIG = {
    runtimeUrl: 'https://seo-expert.mastra.cloud/copilotkit',
    agent: agentType,
    agentName: cloneName,
    instructions: `You are ${cloneName}, an expert SEO keyword research assistant. Help users find the best keywords for their content, analyze search intent, suggest long-tail variations, and provide actionable SEO insights. Be friendly, knowledgeable, and always provide specific, actionable advice.`
  };

  useEffect(() => {
    // Test endpoint connectivity
    testConnection();
    
    // Initialize app
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  const testConnection = async () => {
    try {
      const response = await fetch(CONFIG.runtimeUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });
      
      console.log('Clone chat connection test result:', response.status);
      setConnectionStatus(response.ok ? '✅ Connected' : '❌ Connection Failed');
    } catch (error) {
      console.error('Clone chat connection test failed:', error);
      setConnectionStatus('❌ Connection Error');
    }
  };

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-red-600 mb-2">❌ CopilotKit Loading Error</h3>
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <p className="text-xs text-muted-foreground mb-4">
              Please check the browser console for detailed error information.
            </p>
            <div className="flex gap-2 justify-center">
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                size="sm"
              >
                Retry
              </Button>
              {onClose && (
                <Button onClick={onClose} variant="ghost" size="sm">
                  Close
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold mb-2">Loading CopilotKit...</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Initializing AI chat interface and loading CopilotKit modules.
            </p>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>Connecting to: {CONFIG.runtimeUrl}</div>
              <div>Agent: {CONFIG.agent}</div>
              <div>Status: {connectionStatus}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Close button */}
      {onClose && (
        <Button
          onClick={onClose}
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 z-10"
        >
          <X size={16} />
        </Button>
      )}

      <CopilotKit
        runtimeUrl={CONFIG.runtimeUrl}
        agent={CONFIG.agent}
        publicApiKey="disabled" // Disable branding
      >
        <div className="h-[500px] flex flex-col">
          {/* Header */}
          <div className="p-4 border-b bg-gradient-to-r from-blue-600 to-purple-700 text-white rounded-t-lg">
            <div className="flex items-center gap-3">
              <MessageSquare size={20} />
              <div>
                <h3 className="font-semibold">{CONFIG.agentName}</h3>
                <p className="text-sm opacity-90">AI-powered SEO keyword research assistant</p>
              </div>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="flex-1 flex flex-col">
            <CopilotChat
              instructions={CONFIG.instructions}
              labels={{
                title: CONFIG.agentName,
                initial: `Hi! I'm ${CONFIG.agentName}. What topic or niche would you like to research keywords for today?`
              }}
              className="h-full"
            />
          </div>
        </div>
      </CopilotKit>
    </div>
  );
}

export default CloneCopilotKitChat;