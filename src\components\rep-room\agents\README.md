# Multi-Agent Orchestration UI Components

This directory contains React components for implementing multi-agent orchestration features in Rep Room v3. These components provide visual interfaces for agent coordination, handoffs, and specialist agent management.

## Components Overview

### 1. AgentOrchestrationPanel
**File:** `AgentOrchestrationPanel.tsx`

A comprehensive dashboard showing the current state of agent orchestration including:
- Active agents with real-time status
- Agent handoff visualization with animated transitions
- Response coordination timeline
- Available specialist agents summary

**Props:**
```typescript
interface AgentOrchestrationPanelProps {
  orchestrationState: AgentOrchestrationState;
  agents: Agent[];
  className?: string;
  onAgentSelect?: (agentId: string) => void;
}
```

**Usage:**
```tsx
<AgentOrchestrationPanel
  orchestrationState={orchestrationState}
  agents={allAgents}
  onAgentSelect={handleAgentSelect}
  className="w-full max-w-md"
/>
```

### 2. AgentHandoffIndicator
**File:** `AgentHandoffIndicator.tsx`

Visual indicator for agent-to-agent handoffs with:
- Animated arrow connections between agents
- Handoff reason and duration display
- Success/failure status indicators
- Compact and detailed view modes

**Props:**
```typescript
interface AgentHandoffIndicatorProps {
  handoff: AgentHandoff;
  fromAgent?: Agent;
  toAgent?: Agent;
  showDetails?: boolean;
  className?: string;
  animated?: boolean;
}
```

**Usage:**
```tsx
// Compact view
<AgentHandoffIndicator
  handoff={handoffData}
  fromAgent={mainAgent}
  toAgent={specialistAgent}
  showDetails={false}
  animated={true}
/>

// Detailed view
<AgentHandoffIndicator
  handoff={handoffData}
  fromAgent={mainAgent}
  toAgent={specialistAgent}
  showDetails={true}
/>
```

### 3. SpecialistAgentTrigger
**File:** `SpecialistAgentTrigger.tsx`

Interface for manually triggering specialist agents with:
- Searchable list of available specialists
- Capability-based filtering
- Loading states during activation
- Integration with conversation context

**Props:**
```typescript
interface SpecialistAgentTriggerProps {
  orchestrationState: AgentOrchestrationState;
  conversationContext?: Record<string, unknown>;
  onTriggerSpecialist: (agentId: string, capability: string, context?: Record<string, unknown>) => Promise<void>;
  className?: string;
  disabled?: boolean;
}
```

**Usage:**
```tsx
<SpecialistAgentTrigger
  orchestrationState={orchestrationState}
  conversationContext={{ topic: 'SEO', userQuery: 'How to improve ranking?' }}
  onTriggerSpecialist={handleSpecialistTrigger}
  disabled={systemBusy}
/>
```

### 4. Enhanced AgentCard
**File:** `../AgentCard.tsx` (Updated)

The existing AgentCard component has been enhanced with orchestration features:
- Delegation indicators
- Active specialist agent display
- Handoff animations
- Coordination status for specialists

**New Props:**
```typescript
interface AgentCardProps {
  // ... existing props
  activeHandoff?: AgentHandoff;
  handoffTarget?: Agent;
  isDelegating?: boolean;
  specialistAgents?: Agent[];
  showOrchestration?: boolean;
}
```

## Type Definitions

### Core Types
```typescript
// Agent status now includes 'delegating'
export type AgentStatus = "speaking" | "thinking" | "ready" | "working" | "idle" | "delegating";

// Handoff status tracking
export type HandoffStatus = "pending" | "in-progress" | "completed" | "failed";

// Orchestration event types
export type OrchestrationEventType = "handoff" | "delegation" | "activation" | "completion";
```

### Key Interfaces
```typescript
interface AgentHandoff {
  id: string;
  fromAgentId: string;
  toAgentId: string;
  reason: string;
  status: HandoffStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  context?: Record<string, unknown>;
}

interface AgentOrchestrationState {
  activeHandoffs: AgentHandoff[];
  eventTimeline: OrchestrationEvent[];
  availableSpecialists: Agent[];
  specialistCapabilities: SpecialistAgentCapability[];
  coordinationStatus: "idle" | "coordinating" | "delegating" | "handoff-in-progress";
}

interface SpecialistAgentCapability {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  isAvailable: boolean;
  estimatedDuration?: number;
}
```

## Integration Guide

### 1. Basic Setup
```tsx
import { 
  AgentOrchestrationPanel, 
  SpecialistAgentTrigger,
  AgentHandoffIndicator 
} from './components/rep-room/agents';

// Enhanced AgentCard import
import { AgentCard } from './components/rep-room/AgentCard';
```

### 2. State Management
```tsx
const [orchestrationState, setOrchestrationState] = useState<AgentOrchestrationState>({
  activeHandoffs: [],
  eventTimeline: [],
  availableSpecialists: specialistAgents,
  specialistCapabilities: capabilities,
  coordinationStatus: 'idle'
});
```

### 3. Event Handlers
```tsx
const handleSpecialistTrigger = async (
  agentId: string, 
  capability: string, 
  context?: Record<string, unknown>
) => {
  // Update coordination status
  setOrchestrationState(prev => ({
    ...prev,
    coordinationStatus: 'delegating'
  }));

  try {
    // Trigger specialist agent
    await triggerSpecialistAgent(agentId, capability, context);
    
    // Create handoff record
    const handoff: AgentHandoff = {
      id: generateId(),
      fromAgentId: mainAgentId,
      toAgentId: agentId,
      reason: `Delegating ${capability} task`,
      status: 'in-progress',
      startTime: new Date(),
      context
    };

    // Update state with new handoff
    setOrchestrationState(prev => ({
      ...prev,
      activeHandoffs: [...prev.activeHandoffs, handoff],
      coordinationStatus: 'handoff-in-progress'
    }));
  } catch (error) {
    console.error('Failed to trigger specialist:', error);
    setOrchestrationState(prev => ({
      ...prev,
      coordinationStatus: 'idle'
    }));
  }
};
```

### 4. Layout Integration
```tsx
// Sidebar layout
<div className="flex">
  <div className="flex-1">
    {/* Main chat interface */}
  </div>
  <div className="w-80 border-l">
    <AgentOrchestrationPanel
      orchestrationState={orchestrationState}
      agents={allAgents}
      onAgentSelect={handleAgentSelect}
    />
  </div>
</div>

// Modal/overlay layout
<SpecialistAgentTrigger
  orchestrationState={orchestrationState}
  conversationContext={currentContext}
  onTriggerSpecialist={handleSpecialistTrigger}
  className="fixed bottom-4 right-4 w-96"
/>
```

## Styling and Theming

### Color Scheme
- **Delegation**: Purple (`purple-500`, `purple-100`)
- **Handoffs**: Orange (`orange-500`, `orange-100`)
- **Active Specialists**: Green (`green-500`, `green-100`)
- **Coordination**: Blue (`blue-500`, `blue-100`)

### Animations
- **Pulse**: For active states and speaking indicators
- **Spin**: For loading states
- **Ping**: For handoff transitions
- **Bounce**: For thinking states

### Responsive Design
All components are designed to be responsive and work well on:
- Desktop (full features)
- Tablet (compact layouts)
- Mobile (essential features only)

## Error Handling

### Common Error Scenarios
1. **Specialist Unavailable**: Show user-friendly message
2. **Network Failures**: Retry mechanism with exponential backoff
3. **Timeout Errors**: Clear loading states and show timeout message
4. **Invalid Context**: Validate context before triggering

### Error Recovery
```tsx
const handleError = (error: Error, context: string) => {
  console.error(`Orchestration error in ${context}:`, error);
  
  // Reset loading states
  setLoadingAgents(new Set());
  
  // Update coordination status
  setOrchestrationState(prev => ({
    ...prev,
    coordinationStatus: 'idle'
  }));
  
  // Show user notification
  showNotification({
    type: 'error',
    message: 'Failed to coordinate with specialist agent. Please try again.',
    duration: 5000
  });
};
```

## Performance Considerations

### Optimization Strategies
1. **Memoization**: Use `React.memo` for expensive components
2. **Virtual Scrolling**: For large event timelines
3. **Debounced Search**: In specialist trigger component
4. **Lazy Loading**: For specialist capabilities
5. **State Batching**: Batch orchestration state updates

### Memory Management
```tsx
// Limit event timeline size
const MAX_EVENTS = 50;
setOrchestrationState(prev => ({
  ...prev,
  eventTimeline: [newEvent, ...prev.eventTimeline].slice(0, MAX_EVENTS)
}));

// Clean up completed handoffs
useEffect(() => {
  const cleanup = setInterval(() => {
    setOrchestrationState(prev => ({
      ...prev,
      activeHandoffs: prev.activeHandoffs.filter(
        handoff => handoff.status !== 'completed' || 
        Date.now() - handoff.endTime!.getTime() < 30000 // Keep for 30 seconds
      )
    }));
  }, 10000); // Clean up every 10 seconds

  return () => clearInterval(cleanup);
}, []);
```

## Testing

### Unit Tests
```tsx
// Test specialist triggering
test('should trigger specialist agent correctly', async () => {
  const mockTrigger = jest.fn().mockResolvedValue(undefined);
  
  render(
    <SpecialistAgentTrigger
      orchestrationState={mockState}
      onTriggerSpecialist={mockTrigger}
    />
  );
  
  fireEvent.click(screen.getByText('SEO Analysis'));
  
  await waitFor(() => {
    expect(mockTrigger).toHaveBeenCalledWith(
      'seo-specialist',
      'seo-analysis',
      expect.any(Object)
    );
  });
});
```

### Integration Tests
```tsx
// Test full orchestration flow
test('should complete handoff flow', async () => {
  const { result } = renderHook(() => useOrchestration());
  
  act(() => {
    result.current.triggerSpecialist('seo-specialist', 'seo-analysis');
  });
  
  expect(result.current.orchestrationState.coordinationStatus).toBe('delegating');
  
  await waitFor(() => {
    expect(result.current.orchestrationState.activeHandoffs).toHaveLength(1);
  });
});
```

## Demo Component

See `OrchestrationDemo.tsx` for a complete working example showing all components in action with simulated data and interactions.

## Future Enhancements

### Planned Features
1. **Voice Integration**: Voice commands for specialist triggering
2. **Analytics Dashboard**: Metrics on agent performance and handoffs
3. **Custom Workflows**: User-defined orchestration patterns
4. **Real-time Collaboration**: Multiple users coordinating agents
5. **AI-Powered Suggestions**: Smart specialist recommendations

### API Integration Points
1. **WebSocket Events**: Real-time orchestration updates
2. **REST Endpoints**: CRUD operations for specialists and capabilities
3. **GraphQL Subscriptions**: Live event timeline updates
4. **Webhook Support**: External system integration