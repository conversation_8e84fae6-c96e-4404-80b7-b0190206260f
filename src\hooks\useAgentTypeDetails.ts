import { useState, useEffect } from "react";
import { useSupabase } from "@/contexts/supabase-context";
import { supabase } from "@/integrations/supabase/client";
import { J<PERSON> } from "@/integrations/supabase/types";
import { AgentType } from "./useAgentTypesList";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { isEqual } from "lodash";

// Define error types to categorize different errors
export enum AgentTypeErrorType {
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  NOT_FOUND = 'not_found',
  NETWORK = 'network',
  SERVER = 'server',
  AUTHENTICATION = 'authentication',
  UNKNOWN = 'unknown'
}

// Enhanced error interface with additional context
export interface AgentTypeError {
  type: AgentTypeErrorType;
  message: string;
  details?: string[] | Record<string, unknown>;
  originalError?: unknown;
}

// Extended AgentType interface to include configuration fields
export interface ExtendedAgentType extends Omit<AgentType, 'configuration'> {
  // Configuration object could be either a JSON string or an object
  configuration?: Json | Record<string, unknown>;
  // Fields that might be nested in configuration
  configuration_schema?: Json;
  runtime_context_schema?: Json;
  knowledge_source_config_schema?: Json;
  human_in_the_loop_schema?: Json;
  default_config_values?: Json;
  presentation_config?: Json;
  mastra_agent_id?: string;
  mastra_api_base_url?: string;
  capabilities?: string[];
  agent_operational_mode?: 'interactive_user_facing' | 'autonomous_background';
  trigger_events?: string[];
  customizable_parameters?: Json;
  voice_config?: Json;
  applicable_metrics?: Json;
  chat_ui_settings?: Json;
  phone_settings?: Json;
  static_overrides?: Json;
  presentation_overrides?: Json;
  // Add missing fields that are used in the form
  available_channels?: string[];
}

export interface AgentTypeOperationResult {
  success: boolean;
  message?: string;
  agent?: ExtendedAgentType;
  error?: AgentTypeError; // Add structured error information
}

interface AgentTypeResponse {
  agent: ExtendedAgentType;
}

// Helper function to format and categorize errors
function formatError(error: unknown): AgentTypeError {
  // Handle Error objects
  if (error instanceof Error) {
    const message = error.message;
    
    // Categorize based on error message content
    if (message.includes('validation') || message.includes('required') || message.includes('invalid')) {
      return {
        type: AgentTypeErrorType.VALIDATION,
        message: `Validation error: ${message}`,
        originalError: error
      };
    } else if (message.includes('permission') || message.includes('access denied') || message.includes('not authorized')) {
      return {
        type: AgentTypeErrorType.PERMISSION,
        message: 'You do not have permission to perform this operation',
        originalError: error
      };
    } else if (message.includes('not found') || message.includes('does not exist')) {
      return {
        type: AgentTypeErrorType.NOT_FOUND,
        message: 'The requested agent type could not be found',
        originalError: error
      };
    } else if (message.includes('network') || message.includes('connection')) {
      return {
        type: AgentTypeErrorType.NETWORK,
        message: 'Network error: Please check your internet connection',
        originalError: error
      };
    } else if (message.includes('authentication') || message.includes('not authenticated')) {
      return {
        type: AgentTypeErrorType.AUTHENTICATION,
        message: 'Authentication error: You need to be logged in',
        originalError: error
      };
    }
    
    // Default to unknown with the original message
    return {
      type: AgentTypeErrorType.UNKNOWN,
      message: message,
      originalError: error
    };
  }
  
  // Handle object-like errors from API responses
  if (error && typeof error === 'object') {
    // Use a more specific type to avoid eslint warning
    const errorObj = error as Record<string, unknown>;
    
    // Handle API error responses (common structure)
    if (errorObj.error || errorObj.message) {
      const message = errorObj.error || errorObj.message;
      const details = errorObj.details || errorObj.errors || errorObj.data;
      
      // Categorize API errors
      if (errorObj.status === 401 || errorObj.code === 'unauthorized') {
        return {
          type: AgentTypeErrorType.AUTHENTICATION,
          message: 'Authentication error: You need to be logged in',
          details: details as string[] | Record<string, unknown>,
          originalError: error
        };
      } else if (errorObj.status === 403) {
        return {
          type: AgentTypeErrorType.PERMISSION,
          message: 'Permission denied: You do not have access to perform this operation',
          details: details as string[] | Record<string, unknown>,
          originalError: error
        };
      } else if (errorObj.status === 404) {
        return {
          type: AgentTypeErrorType.NOT_FOUND,
          message: 'Resource not found',
          details: details as string[] | Record<string, unknown>,
          originalError: error
        };
      } else if (errorObj.status === 422 || (typeof message === 'string' && message.includes('validation'))) {
        return {
          type: AgentTypeErrorType.VALIDATION,
          message: 'Validation error',
          details: details as string[] | Record<string, unknown>,
          originalError: error
        };
      } else if (typeof errorObj.status === 'number' && errorObj.status >= 500) {
        return {
          type: AgentTypeErrorType.SERVER,
          message: 'Server error: The operation could not be completed',
          details: details as string[] | Record<string, unknown>,
          originalError: error
        };
      }
      
      // Default structured error
      return {
        type: AgentTypeErrorType.UNKNOWN,
        message: typeof message === 'string' ? message : 'An unknown error occurred',
        details: details as string[] | Record<string, unknown>,
        originalError: error
      };
    }
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return {
      type: AgentTypeErrorType.UNKNOWN,
      message: error
    };
  }
  
  // Default fallback
  return {
    type: AgentTypeErrorType.UNKNOWN,
    message: 'An unknown error occurred',
    originalError: error
  };
}

// Helper function to safely parse JSON fields with recursive parsing for nested JSON strings
// Added debug logging to trace field identity and prevent cross-contamination
function parseJsonField(field: Json | string | Record<string, unknown> | null | undefined, fieldName: string = 'unnamed'): Json {
  // First level parsing - handle string, null, or already parsed object
  if (typeof field === 'string' && field.trim() !== '') {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.debug(`[parseJsonField] ${fieldName}: Parsing string value (${field.substring(0, 20)}...)`);
      }
      
      const parsed = JSON.parse(field);
      // If successfully parsed into an object, recursively parse any nested JSON strings
      if (typeof parsed === 'object' && parsed !== null) {
        return parseNestedJsonStrings(parsed, fieldName) as Json;
      }
      return parsed as Json;
    } catch (e) {
      console.error(`[parseJsonField] ${fieldName}: Error parsing JSON field:`, e);
      // Return the original string if parsing fails
      return field as Json;
    }
  } else if (typeof field === 'object' && field !== null) {
    // If it's already an object, recursively parse any nested JSON strings
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[parseJsonField] ${fieldName}: Processing object value with keys: ${Object.keys(field).join(', ')}`);
    }
    return parseNestedJsonStrings(field, fieldName) as Json;
  } else if (field === null || (typeof field === 'string' && field.trim() === '')) {
    // Explicitly handle null or empty string as an empty object
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[parseJsonField] ${fieldName}: Handling null/empty value, returning empty object`);
    }
    return {} as Json;
  }
  
  // Return the field as is for other types
  if (process.env.NODE_ENV === 'development') {
    console.debug(`[parseJsonField] ${fieldName}: Returning field as-is (type: ${typeof field})`);
  }
  return field as Json;
}

// Helper function to recursively parse any JSON strings nested within an object
// Added field name tracking to preserve field identity
function parseNestedJsonStrings(obj: Record<string, unknown> | Array<unknown>, fieldName: string = 'unnamed'): Json {
  if (Array.isArray(obj)) {
    // If it's an array, recursively parse each item
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[parseNestedJsonStrings] ${fieldName}: Processing array with ${obj.length} items`);
    }
    
    const parsedArray = obj.map((item, index) => {
      const itemFieldName = `${fieldName}[${index}]`;
      
      if (typeof item === 'string' && item.trim() !== '') {
        try {
          const parsed = JSON.parse(item);
          if (typeof parsed === 'object' && parsed !== null) {
            return parseNestedJsonStrings(parsed, itemFieldName);
          }
          return parsed;
        } catch (e) {
          // If parsing fails, it's not a JSON string, so return as is
          return item;
        }
      } else if (typeof item === 'object' && item !== null) {
        return parseNestedJsonStrings(item as Record<string, unknown>, itemFieldName);
      }
      return item;
    });
    return parsedArray as Json;
  } else {
    // It's an object, recursively parse each value
    const result: Record<string, unknown> = {};
    
    Object.entries(obj).forEach(([key, value]) => {
      const nestedFieldName = `${fieldName}.${key}`;
      
      if (typeof value === 'string' && value.trim() !== '') {
        try {
          const parsed = JSON.parse(value);
          if (typeof parsed === 'object' && parsed !== null) {
            result[key] = parseNestedJsonStrings(parsed, nestedFieldName);
          } else {
            result[key] = parsed;
          }
        } catch (e) {
          // If parsing fails, it's not a JSON string, so keep original
          result[key] = value;
        }
      } else if (typeof value === 'object' && value !== null) {
        result[key] = parseNestedJsonStrings(value as Record<string, unknown>, nestedFieldName);
      } else {
        result[key] = value;
      }
    });
    
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[parseNestedJsonStrings] ${fieldName}: Returning object with keys: ${Object.keys(result).join(', ')}`);
    }
    
    return result as Json;
  }
}

// Helper to create user-friendly error messages from API errors
function createUserFriendlyErrorMessage(error: AgentTypeError): string {
  // Start with the base message
  let message = error.message;
  
  // Format details if they exist
  if (error.details) {
    if (Array.isArray(error.details)) {
      // Join array of details into a readable list
      if (error.details.length > 0) {
        message += `: ${error.details.join(', ')}`;
      }
    } else if (typeof error.details === 'object' && error.details !== null) {
      // Format object details
      const formattedDetails = Object.entries(error.details)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
      
      if (formattedDetails) {
        message += `: ${formattedDetails}`;
      }
    }
  }
  
  // Add specific guidance based on error type
  switch (error.type) {
    case AgentTypeErrorType.VALIDATION:
      message += '. Please check the form fields and try again.';
      break;
    case AgentTypeErrorType.PERMISSION:
      message += '. Please contact an administrator if you believe this is an error.';
      break;
    case AgentTypeErrorType.NETWORK:
      message += '. Please check your internet connection and try again.';
      break;
    case AgentTypeErrorType.SERVER:
      message += '. This is a server-side issue. Please try again later or contact support.';
      break;
    case AgentTypeErrorType.AUTHENTICATION:
      message += '. Please log in again to continue.';
      break;
  }
  
  return message;
}

/**
 * Utility function to ensure schema fields are properly stringified
 * This is called from both useEffect and getAgentType to guarantee consistent field handling
 */
function ensureSchemaFieldsAreStringified(
  normalizedData: ExtendedAgentType,
  config: Record<string, unknown>
) {
  // Define the schema fields we need to guarantee are set
  const criticalSchemaFields = [
    'configuration_schema',
    'runtime_context_schema',
    'knowledge_source_config_schema',
    'human_in_the_loop_schema'
  ];
    
  // ENHANCED SCHEMA EXTRACTION: Process each field completely independently
  criticalSchemaFields.forEach(fieldName => {
    try {
      // Track field processing
      if (process.env.NODE_ENV === 'development') {
        console.log(`[SCHEMA-EXTRACT] Processing ${fieldName}`);
      }
      
      // Get source value with clear isolation
      const sourceValue = config[fieldName];
      
      // Track what we're working with
      if (process.env.NODE_ENV === 'development') {
        console.log(`[SCHEMA-EXTRACT] ${fieldName} source:`, {
          exists: sourceValue !== undefined,
          type: typeof sourceValue,
          isNull: sourceValue === null
        });
      }
      
      // CASE 1: Source is an object - deep clone and stringify (most common case in sample)
      if (typeof sourceValue === 'object' && sourceValue !== null) {
        try {
          // Critical: Create completely isolated copy
          const clonedValue = structuredClone(sourceValue);
          normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
          if (process.env.NODE_ENV === 'development') {
            console.log(`[SCHEMA-EXTRACT] ${fieldName}: Successfully stringified object`);
          }
        } catch (e) {
          console.error(`[SCHEMA-EXTRACT] ${fieldName}: Failed to clone/stringify object:`, e);
          
          // Fallback: Try direct stringify without clone
          try {
            normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-EXTRACT] ${fieldName}: Successfully used direct stringify as fallback`);
            }
          } catch (e2) {
            console.error(`[SCHEMA-EXTRACT] ${fieldName}: Even direct stringify failed:`, e2);
            normalizedData[fieldName] = '{}' as Json;
          }
        }
      }
      // CASE 2: Source is a string - parse and re-stringify for consistent formatting
      else if (typeof sourceValue === 'string' && sourceValue.trim()) {
        try {
          const parsed = JSON.parse(sourceValue);
          normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
          if (process.env.NODE_ENV === 'development') {
            console.log(`[SCHEMA-EXTRACT] ${fieldName}: Successfully processed string to formatted JSON string`);
          }
        } catch (e) {
          console.warn(`[SCHEMA-EXTRACT] ${fieldName}: Failed to parse string as JSON, using as-is:`, e);
          normalizedData[fieldName] = sourceValue as Json;
        }
      }
      // CASE 3: Anything else (null, undefined, etc) - set default
      else {
        normalizedData[fieldName] = '{}' as Json;
        if (process.env.NODE_ENV === 'development') {
          console.log(`[SCHEMA-EXTRACT] ${fieldName}: Using default empty object for missing/invalid value`);
        }
      }
      
      // Verification: Ensure field was properly set with expected type
      if (process.env.NODE_ENV === 'development') {
        console.log(`[SCHEMA-EXTRACT] ${fieldName} result:`, {
          set: normalizedData[fieldName] !== undefined,
          type: typeof normalizedData[fieldName],
          isString: typeof normalizedData[fieldName] === 'string',
          isEmpty: normalizedData[fieldName] === '{}',
          preview: typeof normalizedData[fieldName] === 'string' ?
            (normalizedData[fieldName] as string).substring(0, 30) + '...' : 'not string'
        });
      }
      
      // Emergency validation: Ensure we have a string value for form display
      if (typeof normalizedData[fieldName] !== 'string') {
        console.warn(`[SCHEMA-EXTRACT] ${fieldName}: Result is not a string as expected. Converting...`);
        try {
          normalizedData[fieldName] = JSON.stringify(normalizedData[fieldName], null, 2) as Json;
        } catch {
          normalizedData[fieldName] = '{}' as Json;
        }
      }
      
    } catch (error) {
      // Complete isolation - if one field fails, others should still process
      console.error(`[SCHEMA-EXTRACT] Complete failure processing ${fieldName}:`, error);
      normalizedData[fieldName] = '{}' as Json;
    }
  });
  
  // Double-check what fields were set or missing
  if (process.env.NODE_ENV === 'development') {
    criticalSchemaFields.forEach(field => {
      console.log(`[DIRECT-FIX] ${field}: ${normalizedData[field] ? 'SET ✓' : 'MISSING ✗'} (type: ${typeof normalizedData[field]})`);
    });
  }
  
  // FINAL SAFETY CHECK: Ensure all schema fields are properly stringified
  // This is a last resort to guarantee that schema fields are always strings in the form
  const schemaFields = [
    'configuration_schema',
    'runtime_context_schema',
    'knowledge_source_config_schema',
    'human_in_the_loop_schema'
  ];
  
  schemaFields.forEach(field => {
    // If field is not a string or is undefined, set to default empty object string
    if (typeof normalizedData[field] !== 'string' || normalizedData[field] === undefined) {
      console.warn(`[FINAL-SAFETY] ${field} is not a string! Type: ${typeof normalizedData[field]}. Setting to default '{}'`);
      normalizedData[field] = '{}' as Json;
    } else if ((normalizedData[field] as string).trim() === '') {
      // Handle empty strings
      normalizedData[field] = '{}' as Json;
    }
  });
  
  return normalizedData;
}

/**
 * Hook for working with a single agent type (get, create, update, etc.)
 * Enhanced with improved error handling for better user feedback
 */
export function useAgentTypeDetails(id?: string) {
  const { user } = useSupabase();
  const queryClient = useQueryClient();
  const [agentType, setAgentType] = useState<ExtendedAgentType | null>(null);

  /**
   * Get a single agent type by ID using Edge Function
   */
  const fetchAgentType = async (agentId: string): Promise<ExtendedAgentType> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const { data, error } = await supabase.functions.invoke(`agent-types-get/${agentId}`, {
        method: 'GET'
      });
// Only log in development environment
if (process.env.NODE_ENV === 'development') {
  console.debug(`[fetchAgentType] Response for agent ${agentId} received`);
}


      if (error) {
        console.error(`[fetchAgentType] Error for agent ${agentId}:`, error);
        throw new Error(`Error calling agent-types-get: ${error.message}`);
      }

      if (!data) {
        console.error(`[fetchAgentType] No response data for agent ${agentId}`);
        throw new Error('No response data returned from agent-types-get');
      }
      
      // The agent-types-get endpoint returns the agent in data.data
      if (!data.data) {
        console.error(`[fetchAgentType] Unexpected response structure for agent ${agentId}:`, JSON.stringify(data));
        throw new Error(`No agent data found in response. Expected format: { data: { ... agent properties ... } }`);
      }

      return data.data as ExtendedAgentType;
    } catch (err) {
      console.error(`Error fetching agent type (${agentId}):`, err);
      throw err;
    }
  };

  // Use TanStack Query to manage data fetching, loading, and error states
  const query = useQuery<ExtendedAgentType, Error>({
    queryKey: ['agentType', id],
    queryFn: () => fetchAgentType(id!),
    enabled: !!id && !!user, // Only run if we have an ID and user
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    refetchOnWindowFocus: false, // Prevent unnecessary refetching
    refetchOnMount: true, // Only fetch once on mount
  });
  
  // Update the agentType state when query data changes
  // Update the agentType state when query data changes
  // Using proper deep equality comparison to prevent infinite updates
  useEffect(() => {
    // Only run the effect if we have valid data and the query isn't in a loading state
    if (query.data && !query.isLoading && !query.isFetching) {
      // Enhanced debug logging to help trace data transformations (only in development)
      if (process.env.NODE_ENV === 'development') {
        // Safe extraction of configuration data for logging
        const configAvailableChannels = (() => {
          if (query.data.configuration && typeof query.data.configuration === 'object' &&
              !Array.isArray(query.data.configuration)) {
            const config = query.data.configuration as Record<string, unknown>;
            if (Array.isArray(config.available_channels)) {
              return config.available_channels;
            }
          }
          return [];
        })();
        
        console.log('Query data received in useAgentTypeDetails:', {
          id: query.data.id,
          name: query.data.name,
          hasConfiguration: !!query.data.configuration,
          configKeys: query.data.configuration && typeof query.data.configuration === 'object' &&
                      !Array.isArray(query.data.configuration) ?
                      Object.keys(query.data.configuration as Record<string, unknown>) : [],
          available_channels: Array.isArray(query.data.available_channels) ?
                              query.data.available_channels : configAvailableChannels
        });
      }
      
      // Create a normalized version of the data with consistent structure
      const normalizedData = {
        ...query.data
      };
      
      // =========================================================================
      // COMPLETE REWRITE: Direct extraction of configuration fields to top level
      // =========================================================================
      
      if (query.data.configuration) {
        // Ensure we have a properly typed config object to work with
        const config = typeof query.data.configuration === 'object' && !Array.isArray(query.data.configuration)
          ? query.data.configuration as Record<string, unknown>
          : {} as Record<string, unknown>;
        
        // Log initial state so we know what we're working with
        if (process.env.NODE_ENV === 'development') {
          console.log('[REWRITE] Extracting fields from configuration object', {
            configType: typeof config,
            isArray: Array.isArray(config),
            hasValues: config && typeof config === 'object' ? Object.keys(config).length > 0 : false,
            keys: config && typeof config === 'object' ? Object.keys(config) : 'N/A'
          });
        }
        
        // =========================================================================
        // CRITICAL IMPROVEMENT: Process schema fields FIRST
        // This ensures schema fields are handled before any other field extraction
        // =========================================================================
        const schemaFields = [
          'configuration_schema',
          'runtime_context_schema',
          'knowledge_source_config_schema',
          'human_in_the_loop_schema'
        ];
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[SCHEMA-PRIORITY] Processing schema fields first...');
        }
        
        // Process all schema fields using our enhanced extraction logic
        // This must happen BEFORE any other fields are processed
        // to prevent potential cross-contamination
        schemaFields.forEach(fieldName => {
          try {
            // Track field processing
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-PRIORITY] Processing ${fieldName} first`);
            }
            
            // Get source value with clear isolation
            const sourceValue = config[fieldName];
            
            // Case 1: Source is a string - parse and re-stringify for consistent formatting
            if (typeof sourceValue === 'string' && sourceValue.trim()) {
              try {
                const parsed = JSON.parse(sourceValue);
                normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-PRIORITY] ${fieldName}: Successfully processed string to formatted JSON string`);
                }
              } catch (e) {
                console.warn(`[SCHEMA-PRIORITY] ${fieldName}: Failed to parse string as JSON, using as-is:`, e);
                normalizedData[fieldName] = sourceValue as Json;
              }
            }
            // Case 2: Source is an object - deep clone and stringify
            else if (typeof sourceValue === 'object' && sourceValue !== null) {
              try {
                // Critical: Create completely isolated copy
                const clonedValue = structuredClone(sourceValue);
                normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-PRIORITY] ${fieldName}: Successfully stringified object`);
                }
              } catch (e) {
                console.error(`[SCHEMA-PRIORITY] ${fieldName}: Failed to clone/stringify object:`, e);
                
                // Fallback: Try direct stringify without clone
                try {
                  normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
                } catch (e2) {
                  console.error(`[SCHEMA-PRIORITY] ${fieldName}: Even direct stringify failed:`, e2);
                  normalizedData[fieldName] = '{}' as Json;
                }
              }
            }
            // Case 3: Anything else (null, undefined, etc) - set default
            else {
              normalizedData[fieldName] = '{}' as Json;
              if (process.env.NODE_ENV === 'development') {
                console.log(`[SCHEMA-PRIORITY] ${fieldName}: Using default empty object for missing/invalid value`);
              }
            }
            
          } catch (error) {
            // Complete isolation - if one field fails, others should still process
            console.error(`[SCHEMA-PRIORITY] Complete failure processing ${fieldName}:`, error);
            normalizedData[fieldName] = '{}' as Json;
          }
        });
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[SCHEMA-PRIORITY] Schema fields processed, continuing with other fields...');
        }
        
        // Define fields to extract from configuration
        const extractFields = [
          'configuration_schema',
          'runtime_context_schema',
          'knowledge_source_config_schema',
          'human_in_the_loop_schema',
          'default_config_values',
          'presentation_config',
          'voice_config',
          'customizable_parameters',
          'applicable_metrics',
          'chat_ui_settings',
          'phone_settings',
          'static_overrides',
          'presentation_overrides',
          'available_channels',
          'mastra_api_base_url',
          'capabilities',
          'agent_operational_mode',
          'trigger_events'
        ];
        
        // DIRECT EXTRACTION FUNCTION
        // This simple function safely extracts a field from the config object to the top level
        const extractField = (fieldName: string): void => {
          // Skip if configuration is not an object
          if (!config || typeof config !== 'object') return;
          
          // Get the source field from configuration
          const sourceValue = (config as Record<string, unknown>)[fieldName];
          
          // Skip if the source field doesn't exist
          if (sourceValue === undefined) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[REWRITE] Field '${fieldName}' not found in configuration`);
            }
            // Initialize with appropriate default based on field type
            switch (fieldName) {
              case 'configuration_schema':
              case 'runtime_context_schema':
              case 'knowledge_source_config_schema':
              case 'human_in_the_loop_schema':
                // Cast as Json for schema fields
                normalizedData[fieldName] = {} as Json;
                break;
              case 'available_channels':
                // Type-safe assignment with explicit typing
                (normalizedData as ExtendedAgentType).available_channels = [];
                break;
              case 'capabilities':
                (normalizedData as ExtendedAgentType).capabilities = [];
                break;
              case 'trigger_events':
                (normalizedData as ExtendedAgentType).trigger_events = [];
                break;
              case 'mastra_api_base_url':
                // Handle string type
                (normalizedData as ExtendedAgentType).mastra_api_base_url = '';
                break;
              case 'agent_operational_mode':
                // Handle enum type
                (normalizedData as ExtendedAgentType).agent_operational_mode = 'interactive_user_facing';
                break;
              default:
                // For other fields, assign empty object as Json
                normalizedData[fieldName] = {} as Json;
            }
            return;
          }
          
          if (process.env.NODE_ENV === 'development') {
            console.log(`[REWRITE] Extracting '${fieldName}':`, {
              sourceType: typeof sourceValue,
              sourcePreview: typeof sourceValue === 'string' ?
                sourceValue.substring(0, 30) + '...' :
                sourceValue !== null && typeof sourceValue === 'object' ?
                  JSON.stringify(sourceValue).substring(0, 30) + '...' :
                  String(sourceValue)
            });
          }
          // ENHANCED SCHEMA PROCESSING: Complete rewrite of schema field handling
          // Schema fields are stored as objects in the configuration column but need to be
          // stringified JSON for the form editors
          if (fieldName.includes('schema')) {
            // Log what we're working with in development mode
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-HANDLER] Processing '${fieldName}':`, {
                sourceExists: sourceValue !== undefined,
                sourceType: typeof sourceValue,
                sourceIsNull: sourceValue === null,
                sourcePreview: typeof sourceValue === 'object' && sourceValue !== null ? 
                  JSON.stringify(sourceValue).substring(0, 50) + '...' : 
                  typeof sourceValue === 'string' ? sourceValue.substring(0, 50) + '...' : 
                  String(sourceValue)
              });
            }
            
            // CASE 1: Schema field is an object (this is the most common case based on the sample)
            if (typeof sourceValue === 'object' && sourceValue !== null) {
              try {
                // Deep clone and stringify the object
                const clonedValue = structuredClone(sourceValue);
                normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-HANDLER] '${fieldName}': Successfully stringified object to JSON string`);
                }
              } catch (e) {
                console.error(`[SCHEMA-HANDLER] '${fieldName}': Failed to clone/stringify object:`, e);
                
                // Fallback: Try direct stringify without clone
                try {
                  normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`[SCHEMA-HANDLER] '${fieldName}': Used direct stringify as fallback`);
                  }
                } catch (e2) {
                  console.error(`[SCHEMA-HANDLER] '${fieldName}': Even direct stringify failed:`, e2);
                  normalizedData[fieldName] = '{}' as Json;
                }
              }
            }
            // CASE 2: Schema field is already a string
            else if (typeof sourceValue === 'string' && sourceValue.trim()) {
              try {
                // Parse and re-stringify for consistent formatting
                const parsed = JSON.parse(sourceValue);
                normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-HANDLER] '${fieldName}': Successfully processed string to formatted JSON string`);
                }
              } catch (e) {
                console.warn(`[SCHEMA-HANDLER] '${fieldName}': Failed to parse string as JSON, using as-is:`, e);
                normalizedData[fieldName] = sourceValue as Json;
              }
            }
            // CASE 3: Default for missing/null/undefined values
            else {
              normalizedData[fieldName] = '{}' as Json;
              
              if (process.env.NODE_ENV === 'development') {
                console.log(`[SCHEMA-HANDLER] '${fieldName}': Using default empty object for missing/invalid value`);
              }
            }
            
            // Verify the field is now properly set
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-HANDLER] '${fieldName}': Final state:`, {
                set: normalizedData[fieldName] !== undefined,
                type: typeof normalizedData[fieldName],
                isEmpty: normalizedData[fieldName] === '{}'
              });
            }
            
            return; // Processing complete for schema fields
          }
          
          // For non-schema fields, process as before
          if (typeof sourceValue === 'string') {
            // Non-schema string fields - store as-is
            normalizedData[fieldName] = sourceValue as Json;
          }
          else if (sourceValue === null) {
            // Handle null values based on field type
            if (fieldName.includes('schema')) {
              // Empty string for schema fields
              normalizedData[fieldName] = '' as Json;
            } else if (['available_channels', 'capabilities', 'trigger_events'].includes(fieldName)) {
              // Empty array for array fields
              normalizedData[fieldName] = [] as unknown as Json;
            } else {
              // Empty object for object fields
              normalizedData[fieldName] = {} as Json;
            }
          }
          else if (Array.isArray(sourceValue)) {
            // Direct assignment for arrays with structuredClone to prevent reference issues
            normalizedData[fieldName] = structuredClone(sourceValue) as Json;
          }
          else if (typeof sourceValue === 'object') {
            // Enhanced handling of special configuration objects
            if (['presentation_config', 'presentation_overrides', 'customizable_parameters'].includes(fieldName)) {
              // Special handling for these important configuration objects
              try {
                // Always use structuredClone for complete isolation
                const clonedObject = structuredClone(sourceValue);
                normalizedData[fieldName] = clonedObject as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[REWRITE] Extracted important config object '${fieldName}':`, {
                    resultType: 'object',
                    keys: Object.keys(clonedObject).length > 0 ?
                      Object.keys(clonedObject) : 'empty object',
                    preview: JSON.stringify(clonedObject).substring(0, 50) + '...'
                  });
                }
              } catch (e) {
                // Fallback to an empty object if cloning fails
                console.error(`[REWRITE] Failed to clone '${fieldName}' object:`, e);
                normalizedData[fieldName] = {} as Json;
              }
            } else {
              // Standard handling for other object types
              normalizedData[fieldName] = structuredClone(sourceValue) as Json;
              
              if (process.env.NODE_ENV === 'development') {
                console.log(`[REWRITE] Extracted '${fieldName}' object:`, {
                  resultType: 'object',
                  keys: Object.keys(normalizedData[fieldName] as object).length > 0 ?
                    Object.keys(normalizedData[fieldName] as object) : 'empty object'
                });
              }
            }
          }
          else {
            // Direct assignment for primitive values
            normalizedData[fieldName] = sourceValue as Json;
          }
          
          // VALIDATION: Verify the extraction was successful
          if (process.env.NODE_ENV === 'development') {
            const extractedValue = normalizedData[fieldName];
            const extractionSuccessful = extractedValue !== undefined;
            const extractedType = typeof extractedValue;
            const sourceIsObject = typeof sourceValue === 'object' && sourceValue !== null && !Array.isArray(sourceValue);
            const extractedIsObject = typeof extractedValue === 'object' && extractedValue !== null && !Array.isArray(extractedValue);
            
            // Check if objects have the same keys (structural similarity)
            let hasSameKeys = true;
            if (sourceIsObject && extractedIsObject) {
              const sourceKeys = Object.keys(sourceValue as object).sort().join(',');
              const extractedKeys = Object.keys(extractedValue as object).sort().join(',');
              hasSameKeys = sourceKeys === extractedKeys;
            }
            
            console.log(`[REWRITE] Validation for '${fieldName}':`, {
              successful: extractionSuccessful,
              sourceType: typeof sourceValue,
              extractedType,
              structurallySame: sourceIsObject && extractedIsObject ? hasSameKeys : 'N/A',
              valuePreserved: extractionSuccessful
            });
            
            // Warning for potential issues
            if (sourceIsObject && extractedIsObject && !hasSameKeys) {
              console.warn(`[REWRITE] Warning: '${fieldName}' extraction may have lost data:`, {
                sourceKeys: Object.keys(sourceValue as object),
                extractedKeys: Object.keys(extractedValue as object)
              });
            }
          }
        };
        
        // Extract each field one by one
        extractFields.forEach(field => {
          extractField(field);
        });
        
        // VERIFICATION: Final check for critical fields to ensure proper extraction
        if (process.env.NODE_ENV === 'development') {
          console.log('[REWRITE] Verification of critical fields:');
          
          // SOURCE TRUTH - What fields actually exist in the configuration object?
          const configFields = typeof config === 'object' && config !== null ? Object.keys(config) : [];
          console.log('[REWRITE] Configuration fields in source:', configFields);
          
          // Check critical schema fields specifically
          const criticalFields = ['configuration_schema', 'runtime_context_schema'];
          for (const field of criticalFields) {
            // Does this field exist in the source configuration?
            const existsInSource = typeof config === 'object' && config !== null &&
              Object.prototype.hasOwnProperty.call(config, field);
            
            // Was this field extracted to the top level?
            const existsInNormalized = field in normalizedData;
            
            // Does this field actually have content?
            const hasContent = (() => {
              const value = normalizedData[field];
              if (value === undefined || value === null) return false;
              if (typeof value === 'string') return value.trim() !== '';
              if (typeof value === 'object') return Object.keys(value).length > 0;
              return true;
            })();
            
            console.log(`[REWRITE] Field '${field}':`, {
              existsInSource,
              existsInNormalized,
              hasContent,
              type: typeof normalizedData[field],
              preview: typeof normalizedData[field] === 'object' ?
                JSON.stringify(normalizedData[field]).substring(0, 50) + '...' :
                String(normalizedData[field]).substring(0, 50) + '...'
            });
            
            // Warning for fails
            if (existsInSource && (!existsInNormalized || !hasContent)) {
              console.warn(`[REWRITE] CRITICAL: '${field}' extraction failed!`);
            }
          }
          
          // FINAL VALIDATION: Comprehensive schema field verification
          console.log('[SCHEMA-DEBUG] Schema fields extracted:', {
            configuration_schema: normalizedData.configuration_schema ?
              typeof normalizedData.configuration_schema : 'undefined',
            runtime_context_schema: normalizedData.runtime_context_schema ?
              typeof normalizedData.runtime_context_schema : 'undefined',
            knowledge_source_config_schema: normalizedData.knowledge_source_config_schema ?
              typeof normalizedData.knowledge_source_config_schema : 'undefined',
            human_in_the_loop_schema: normalizedData.human_in_the_loop_schema ?
              typeof normalizedData.human_in_the_loop_schema : 'undefined'
          });
          
          // Check content preview for all schema fields
          const schemaFields = [
            'configuration_schema',
            'runtime_context_schema',
            'knowledge_source_config_schema',
            'human_in_the_loop_schema'
          ];
          
          console.log('[SCHEMA-DEBUG] Schema content previews:');
          schemaFields.forEach(field => {
            const value = normalizedData[field];
            console.log(`${field}:`, {
              exists: value !== undefined,
              type: typeof value,
              isString: typeof value === 'string',
              hasContent: typeof value === 'string' && value.trim() !== '',
              length: typeof value === 'string' ? value.length : 0,
              preview: value ?
                (typeof value === 'string' ?
                  value.substring(0, 50) + '...' :
                  JSON.stringify(value).substring(0, 50) + '...') :
                'empty'
            });
          });
  
          // VERIFICATION: Confirm schema fields are properly stringified
          // and will display correctly in the form
          const allSchemasValid = schemaFields.every(field => {
            const value = normalizedData[field];
            const isValidStringifiedSchema = typeof value === 'string' && value.trim() !== '';
            
            // If any schema field is not properly stringified, log a warning
            if (!isValidStringifiedSchema) {
              console.warn(`[SCHEMA-DEBUG] WARNING: Schema field '${field}' is not properly stringified:`, {
                value: value === undefined ? 'undefined' :
                      value === null ? 'null' :
                      typeof value === 'string' ? (value.length > 0 ? 'string with content' : 'empty string') :
                      `non-string type: ${typeof value}`
              });
              
              // Last-ditch attempt to fix this field if needed
              if (value === undefined || value === null) {
                normalizedData[field] = '{}' as Json;
                console.log(`[SCHEMA-DEBUG] Applied emergency fix to '${field}': set to empty object string`);
                return true; // Consider this fixed
              } else if (typeof value === 'object') {
                try {
                  normalizedData[field] = JSON.stringify(value, null, 2) as Json;
                  console.log(`[SCHEMA-DEBUG] Applied emergency fix to '${field}': converted object to string`);
                  return true; // Consider this fixed
                } catch (e) {
                  console.error(`[SCHEMA-DEBUG] Failed to apply emergency fix to '${field}'`, e);
                  return false;
                }
              }
              
              return false;
            }
            
            return true;
          });
          
          console.log(`[SCHEMA-DEBUG] Final validation: All schema fields properly stringified: ${allSchemasValid ? 'YES ✅' : 'NO ❌'}`);
        }
        
        // Safely extract available_channels with proper type handling
        const configAvailableChannels = (() => {
          if (Array.isArray(config.available_channels)) {
            return config.available_channels as string[];
          }
          return [];
        })();
        
        // Set available_channels with proper fallback
        normalizedData.available_channels = Array.isArray(query.data.available_channels) ?
          query.data.available_channels : configAvailableChannels;
        
        // CRITICAL FIX: Extract Mastra fields from dedicated columns FIRST, then fallback to configuration
        // This ensures the form displays values from the dedicated database columns
        
        // Extract mastra_agent_id from dedicated column (prioritize over configuration)
        if (query.data.mastra_agent_id) {
          normalizedData.mastra_agent_id = query.data.mastra_agent_id;
        } else if (typeof config.mastra_agent_id === 'string') {
          normalizedData.mastra_agent_id = config.mastra_agent_id;
        } else {
          normalizedData.mastra_agent_id = '';
        }
        
        // Extract mastra_api_base_url from dedicated column (prioritize over configuration)
        if (query.data.mastra_api_base_url) {
          normalizedData.mastra_api_base_url = query.data.mastra_api_base_url;
        } else if (typeof config.mastra_api_base_url === 'string') {
          normalizedData.mastra_api_base_url = config.mastra_api_base_url;
        } else {
          normalizedData.mastra_api_base_url = '';
        }

        // Extract remaining string/array fields
        if (Array.isArray(config.capabilities)) {
          normalizedData.capabilities = config.capabilities as string[];
        }
        
        if (Array.isArray(config.trigger_events)) {
          normalizedData.trigger_events = config.trigger_events as string[];
        }
        
        if (config.agent_operational_mode) {
          normalizedData.agent_operational_mode = config.agent_operational_mode as 'interactive_user_facing' | 'autonomous_background';
        }
      }
      // Use lodash's isEqual for proper deep comparison instead of string comparison
      // This prevents false change detections due to object property order in JSON.stringify
      // CRITICAL EMERGENCY FIX: Direct assignment of schema fields
      // This is a direct solution to ensure schema fields show up correctly in the form
      // Even if our extraction logic fails, we're going to set these fields directly from the source
      if (process.env.NODE_ENV === 'development') {
        console.log('[DIRECT-FIX:useEffect] Applying direct schema field assignment');
      }
      
      // First grab the configuration directly
      const config = (query.data.configuration && typeof query.data.configuration === 'object' && !Array.isArray(query.data.configuration))
        ? query.data.configuration as Record<string, unknown>
        : {} as Record<string, unknown>;
      
      // Define the schema fields we need to guarantee are set
      const criticalSchemaFields = [
        'configuration_schema',
        'runtime_context_schema',
        'knowledge_source_config_schema',
        'human_in_the_loop_schema'
      ];
      
      // For each critical field, ensure we have a proper string representation
      criticalSchemaFields.forEach(fieldName => {
        if (config[fieldName]) {
          try {
            // If it's a string, try to parse and re-stringify (for consistent formatting)
            if (typeof config[fieldName] === 'string') {
              try {
                const parsed = JSON.parse(config[fieldName] as string);
                normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
                console.log(`[DIRECT-FIX:useEffect] Field ${fieldName} parsed from string`);
              } catch (e) {
                // If it can't be parsed as JSON, use as is
                normalizedData[fieldName] = config[fieldName] as Json;
                console.log(`[DIRECT-FIX:useEffect] Field ${fieldName} used as-is (unparseable string)`);
              }
            }
            // If it's an object, stringify it
            else if (typeof config[fieldName] === 'object' && config[fieldName] !== null) {
              normalizedData[fieldName] = JSON.stringify(config[fieldName], null, 2) as Json;
              console.log(`[DIRECT-FIX:useEffect] Field ${fieldName} stringified from object`);
            }
          } catch (error) {
            console.error(`[DIRECT-FIX:useEffect] Error handling ${fieldName}`, error);
          }
        }
      });
      
      // Double-check what fields were set or missing
      if (process.env.NODE_ENV === 'development') {
        criticalSchemaFields.forEach(field => {
          console.log(`[DIRECT-FIX:useEffect] ${field}: ${normalizedData[field] ? 'SET ✓' : 'MISSING ✗'} (type: ${typeof normalizedData[field]})`);
        });
      }

      // FINAL SAFETY CHECK: Ensure all schema fields are properly stringified
      // This is a last resort to guarantee that schema fields are always strings in the form
      const schemaFields = [
        'configuration_schema',
        'runtime_context_schema',
        'knowledge_source_config_schema',
        'human_in_the_loop_schema'
      ];
      
      schemaFields.forEach(field => {
        // If field is not a string or is undefined, set to default empty object string
        if (typeof normalizedData[field] !== 'string' || normalizedData[field] === undefined) {
          console.warn(`[FINAL-SAFETY:useEffect] ${field} is not a string! Type: ${typeof normalizedData[field]}. Setting to default '{}'`);
          normalizedData[field] = '{}' as Json;
        } else if ((normalizedData[field] as string).trim() === '') {
          // Handle empty strings
          normalizedData[field] = '{}' as Json;
        }
      });

      // Only update state if the data actually changed
      if (!isEqual(normalizedData, agentType)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Setting agent type with normalized data - data has changed');
          
          // Debug what changed to help identify sources of unnecessary updates
          if (agentType) {
            // Find which properties changed (top level only for brevity)
            const changedProps = Object.keys(normalizedData).filter(key => {
              const k = key as keyof typeof normalizedData;
              return !isEqual(normalizedData[k], agentType[k]);
            });
            
            console.log('Changed properties:', changedProps);
            
            // If presentation_config changed, log more details
            if (changedProps.includes('presentation_config')) {
              console.log('presentation_config before:', agentType.presentation_config);
              console.log('presentation_config after:', normalizedData.presentation_config);
            }
          }
        }
        
        // Create a stable new object to prevent unnecessary re-renders
        const stableNormalizedData = {...normalizedData};
        
        // Use a functional state update to further prevent loops
        setAgentType(() => stableNormalizedData);
      } else if (process.env.NODE_ENV === 'development') {
        console.log('Data unchanged, skipping state update to prevent loop');
      }
    }
  }, [query.data, query.isLoading, query.isFetching]);
  // Get agent type with improved data normalization to prevent state update loops
  const getAgentType = async (agentId: string): Promise<ExtendedAgentType | null> => {
    try {
      // If we already have the data for this ID and query is not loading, return the cached data
      if (id === agentId && query.data && !query.isLoading) {
        return query.data;
      }
      
      // Otherwise fetch the data
      const result = await fetchAgentType(agentId);
      
      // Normalize the data just like in useEffect to ensure consistency
      const normalizedData = {
        ...result
      };
      
      // =========================================================================
      // REWRITTEN EXTRACTION: Using the same direct approach as in useEffect
      // =========================================================================
      
      if (result.configuration) {
        // Ensure we have a properly typed config object to work with
        const config = typeof result.configuration === 'object' && !Array.isArray(result.configuration)
          ? result.configuration as Record<string, unknown>
          : {} as Record<string, unknown>;
        
        // Log initial state in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log('[REWRITE:getAgentType] Processing configuration object with keys:',
            Object.keys(config).join(', '));
        }
        
        // =========================================================================
        // CRITICAL IMPROVEMENT: Process schema fields FIRST in getAgentType
        // This ensures schema fields are handled before any other field extraction
        // =========================================================================
        const schemaFields = [
          'configuration_schema',
          'runtime_context_schema',
          'knowledge_source_config_schema',
          'human_in_the_loop_schema'
        ];
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[SCHEMA-PRIORITY:getAgentType] Processing schema fields first...');
        }
        
        // Process all schema fields using our enhanced extraction logic
        // This must happen BEFORE any other fields are processed
        // to prevent potential cross-contamination
        schemaFields.forEach(fieldName => {
          try {
            // Track field processing
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-PRIORITY:getAgentType] Processing ${fieldName} first`);
            }
            
            // Get source value with clear isolation
            const sourceValue = config[fieldName];
            
            // Case 1: Source is a string - parse and re-stringify for consistent formatting
            // CASE 1: Schema field is an object (this is the most common case based on the sample)
            if (typeof sourceValue === 'object' && sourceValue !== null) {
              // Deep clone and stringify the object
              try {
                const clonedValue = structuredClone(sourceValue);
                normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-HANDLER] '${fieldName}': Successfully stringified object to JSON string`);
                }
              } catch (e) {
                console.error(`[SCHEMA-HANDLER] '${fieldName}': Failed to clone/stringify object:`, e);
                
                // Fallback: Try direct stringify without clone
                try {
                  normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`[SCHEMA-HANDLER] '${fieldName}': Used direct stringify as fallback`);
                  }
                } catch (e2) {
                  console.error(`[SCHEMA-HANDLER] '${fieldName}': Even direct stringify failed:`, e2);
                  normalizedData[fieldName] = '{}' as Json;
                }
              }
            }
            // CASE 2: Schema field is already a string
            else if (typeof sourceValue === 'string' && sourceValue.trim()) {
              try {
                const parsed = JSON.parse(sourceValue);
                normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-PRIORITY:getAgentType] ${fieldName}: Successfully processed string to formatted JSON string`);
                }
              } catch (e) {
                console.warn(`[SCHEMA-PRIORITY:getAgentType] ${fieldName}: Failed to parse string as JSON, using as-is:`, e);
                normalizedData[fieldName] = sourceValue as Json;
              }
            }
            // Case 2: Source is an object - deep clone and stringify
            else if (typeof sourceValue === 'object' && sourceValue !== null) {
              try {
                // Critical: Create completely isolated copy
                const clonedValue = structuredClone(sourceValue);
                normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-PRIORITY:getAgentType] ${fieldName}: Successfully stringified object`);
                }
              } catch (e) {
                console.error(`[SCHEMA-PRIORITY:getAgentType] ${fieldName}: Failed to clone/stringify object:`, e);
                
                // Fallback: Try direct stringify without clone
                try {
                  normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
                } catch (e2) {
                  console.error(`[SCHEMA-PRIORITY:getAgentType] ${fieldName}: Even direct stringify failed:`, e2);
                  normalizedData[fieldName] = '{}' as Json;
                }
              }
            }
            // Case 3: Anything else (null, undefined, etc) - set default
            else {
              normalizedData[fieldName] = '{}' as Json;
              if (process.env.NODE_ENV === 'development') {
                console.log(`[SCHEMA-PRIORITY:getAgentType] ${fieldName}: Using default empty object for missing/invalid value`);
              }
            }
            
          } catch (error) {
            // Complete isolation - if one field fails, others should still process
            console.error(`[SCHEMA-PRIORITY:getAgentType] Complete failure processing ${fieldName}:`, error);
            normalizedData[fieldName] = '{}' as Json;
          }
        });
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[SCHEMA-PRIORITY:getAgentType] Schema fields processed, continuing with other fields...');
        }
        
        // Define fields to extract from configuration - same as in useEffect
        const extractFields = [
          'configuration_schema',
          'runtime_context_schema',
          'knowledge_source_config_schema',
          'human_in_the_loop_schema',
          'default_config_values',
          'presentation_config',
          'voice_config',
          'customizable_parameters',
          'applicable_metrics',
          'chat_ui_settings',
          'phone_settings',
          'static_overrides',
          'presentation_overrides',
          'available_channels',
          'mastra_api_base_url',
          'capabilities',
          'agent_operational_mode',
          'trigger_events'
        ];
        
        // Use the same direct extraction function as in useEffect
        const extractField = (fieldName: string): void => {
          // Skip if configuration is not an object
          if (!config || typeof config !== 'object') return;
          
          // Get the source field from configuration
          const sourceValue = config[fieldName];
          
          // Skip if the source field doesn't exist
          if (sourceValue === undefined) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[REWRITE:getAgentType] Field '${fieldName}' not found in configuration`);
            }
            
            // Initialize with appropriate default based on field type
            switch (fieldName) {
              case 'configuration_schema':
              case 'runtime_context_schema':
              case 'knowledge_source_config_schema':
              case 'human_in_the_loop_schema':
                // Cast as Json for schema fields
                normalizedData[fieldName] = {} as Json;
                break;
              case 'available_channels':
                (normalizedData as ExtendedAgentType).available_channels = [];
                break;
              case 'capabilities':
                (normalizedData as ExtendedAgentType).capabilities = [];
                break;
              case 'trigger_events':
                (normalizedData as ExtendedAgentType).trigger_events = [];
                break;
              case 'mastra_api_base_url':
                (normalizedData as ExtendedAgentType).mastra_api_base_url = '';
                break;
              case 'agent_operational_mode':
                (normalizedData as ExtendedAgentType).agent_operational_mode = 'interactive_user_facing';
                break;
              default:
                normalizedData[fieldName] = {} as Json;
            }
            return;
          }
          
          if (process.env.NODE_ENV === 'development') {
            console.log(`[REWRITE:getAgentType] Processing '${fieldName}':`, {
              type: typeof sourceValue,
              preview: typeof sourceValue === 'string' ?
                sourceValue.substring(0, 30) + '...' :
                sourceValue !== null && typeof sourceValue === 'object' ?
                  JSON.stringify(sourceValue).substring(0, 30) + '...' :
                  String(sourceValue)
            });
          }
          
          // ENHANCED SCHEMA PROCESSING: Process schema fields directly even if the specialized handler ran
          if (fieldName.includes('schema')) {
            // For schema fields, verify first if they were already correctly processed by the specialized handler
            const alreadyProcessed = normalizedData[fieldName] !== undefined;
            const isCorrectType = typeof normalizedData[fieldName] === 'string';
            
            // Log the verification details if in development mode
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Verifying schema field state:`, {
                alreadyProcessed,
                correctType: isCorrectType,
                type: typeof normalizedData[fieldName],
                isEmpty: normalizedData[fieldName] === '{}'
              });
            }
            
            // FIXED: Even if the field appears to be processed, we should always process schema fields
            // to ensure they're consistently handled - removing the early return that caused the issue
            // We'll use sourceValue (the original value from config) for consistent processing
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Always processing schema fields for consistency, using sourceValue`);
            }
            
            // Process schema fields based on their type, always starting from sourceValue
            // This ensures consistent handling regardless of what might already be in normalizedData
            if (typeof sourceValue === 'string' && sourceValue.trim()) {
              try {
                // Parse and re-stringify for consistent formatting
                const parsed = JSON.parse(sourceValue);
                normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Successfully processed string value`);
                }
              } catch (e) {
                console.warn(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Failed to parse string as JSON, using as-is:`, e);
                normalizedData[fieldName] = sourceValue as Json;
              }
            }
            else if (typeof sourceValue === 'object' && sourceValue !== null) {
              try {
                // Deep clone for safety
                const clonedValue = structuredClone(sourceValue);
                normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Successfully stringified object value`);
                }
              } catch (e) {
                console.error(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Failed to stringify object:`, e);
                
                // Fallback: Try direct stringify without clone
                try {
                  normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
                } catch (e2) {
                  console.error(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Even direct stringify failed:`, e2);
                  normalizedData[fieldName] = '{}' as Json;
                }
              }
            }
            else {
              // Default for missing/null/undefined values
              normalizedData[fieldName] = '{}' as Json;
              
              if (process.env.NODE_ENV === 'development') {
                console.log(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Using default empty object for missing/invalid value`);
              }
            }
            
            // Verify the field is now properly set
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-FLOW:getAgentType] '${fieldName}' - Final state:`, {
                set: normalizedData[fieldName] !== undefined,
                type: typeof normalizedData[fieldName],
                isEmpty: normalizedData[fieldName] === '{}'
              });
            }
            
            return; // Processing complete
          }
          // For non-schema fields, process as before
          else if (typeof sourceValue === 'string') {
            // Handle special string fields directly
            switch(fieldName) {
              case 'mastra_api_base_url':
                (normalizedData as ExtendedAgentType).mastra_api_base_url = sourceValue;
                break;
              case 'agent_operational_mode':
                (normalizedData as ExtendedAgentType).agent_operational_mode =
                  sourceValue as 'interactive_user_facing' | 'autonomous_background';
                break;
              default:
                normalizedData[fieldName] = sourceValue as Json;
            }
          }
          else if (Array.isArray(sourceValue)) {
            // Handle array fields directly with appropriate typing
            switch(fieldName) {
              case 'available_channels':
                (normalizedData as ExtendedAgentType).available_channels =
                  structuredClone(sourceValue) as string[];
                break;
              case 'capabilities':
                (normalizedData as ExtendedAgentType).capabilities =
                  structuredClone(sourceValue) as string[];
                break;
              case 'trigger_events':
                (normalizedData as ExtendedAgentType).trigger_events =
                  structuredClone(sourceValue) as string[];
                break;
              default:
                normalizedData[fieldName] = structuredClone(sourceValue) as Json;
            }
          }
          else if (sourceValue === null) {
            // Handle nulls with appropriate defaults based on field type
            switch (fieldName) {
              case 'configuration_schema':
              case 'runtime_context_schema':
              case 'knowledge_source_config_schema':
              case 'human_in_the_loop_schema':
                normalizedData[fieldName] = {} as Json;
                break;
              case 'available_channels':
                (normalizedData as ExtendedAgentType).available_channels = [];
                break;
              case 'capabilities':
                (normalizedData as ExtendedAgentType).capabilities = [];
                break;
              case 'trigger_events':
                (normalizedData as ExtendedAgentType).trigger_events = [];
                break;
              case 'mastra_api_base_url':
                (normalizedData as ExtendedAgentType).mastra_api_base_url = '';
                break;
              case 'agent_operational_mode':
                (normalizedData as ExtendedAgentType).agent_operational_mode = 'interactive_user_facing';
                break;
              default:
                normalizedData[fieldName] = {} as Json;
            }
          }
          else if (typeof sourceValue === 'object') {
            // Enhanced handling of special configuration objects
            if (['presentation_config', 'presentation_overrides', 'customizable_parameters'].includes(fieldName)) {
              // Special handling for these important configuration objects
              try {
                // Always use structuredClone for complete isolation
                const clonedObject = structuredClone(sourceValue);
                normalizedData[fieldName] = clonedObject as Json;
                
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[REWRITE:getAgentType] Extracted important config object '${fieldName}':`, {
                    resultType: 'object',
                    keys: Object.keys(clonedObject).length > 0 ?
                      Object.keys(clonedObject) : 'empty object',
                    preview: JSON.stringify(clonedObject).substring(0, 50) + '...'
                  });
                }
              } catch (e) {
                // Fallback to an empty object if cloning fails
                console.error(`[REWRITE:getAgentType] Failed to clone '${fieldName}' object:`, e);
                normalizedData[fieldName] = {} as Json;
              }
            } else {
              // Standard handling for other object types
              normalizedData[fieldName] = structuredClone(sourceValue) as Json;
            }
          }
          else {
            // Direct assignment for primitive values
            normalizedData[fieldName] = sourceValue as Json;
          }
          
          // Log extraction result in development mode
          if (process.env.NODE_ENV === 'development') {
            const extractedValue = normalizedData[fieldName];
            console.log(`[REWRITE:getAgentType] '${fieldName}' extraction result:`, {
              successful: extractedValue !== undefined,
              type: typeof extractedValue,
              isEmpty: typeof extractedValue === 'object' && extractedValue !== null ?
                Object.keys(extractedValue).length === 0 : false
            });
          }
        };
        
        // Extract each field using our direct extraction function
        extractFields.forEach(fieldName => extractField(fieldName));
        
        // CRITICAL FIX: Extract Mastra fields from dedicated columns FIRST, then fallback to configuration
        // This ensures the form displays values from the dedicated database columns
        
        // Extract mastra_agent_id from dedicated column (prioritize over configuration)
        if (result.mastra_agent_id) {
          normalizedData.mastra_agent_id = result.mastra_agent_id;
        } else if (typeof config.mastra_agent_id === 'string') {
          normalizedData.mastra_agent_id = config.mastra_agent_id;
        } else {
          normalizedData.mastra_agent_id = '';
        }
        
        // Extract mastra_api_base_url from dedicated column (prioritize over configuration)
        if (result.mastra_api_base_url) {
          normalizedData.mastra_api_base_url = result.mastra_api_base_url;
        } else if (typeof config.mastra_api_base_url === 'string') {
          normalizedData.mastra_api_base_url = config.mastra_api_base_url;
        } else {
          normalizedData.mastra_api_base_url = '';
        }
        
        // Final verification in development mode
        if (process.env.NODE_ENV === 'development') {
          // Log which fields were processed
          console.log('[REWRITE:getAgentType] Processed fields:',
            extractFields.filter(f => normalizedData[f] !== undefined).join(', '));
          
          // Check for cross-contamination between critical fields
          const criticalFields = ['configuration_schema', 'runtime_context_schema'];
          for (let i = 0; i < criticalFields.length; i++) {
            for (let j = i+1; j < criticalFields.length; j++) {
              const field1 = criticalFields[i];
              const field2 = criticalFields[j];
              
              if (
                normalizedData[field1] &&
                normalizedData[field2] &&
                normalizedData[field1] === normalizedData[field2] &&
                typeof normalizedData[field1] === 'object' &&
                Object.keys(normalizedData[field1] as Record<string, unknown>).length > 0
              ) {
                console.warn(`[REWRITE:getAgentType] Cross-contamination between ${field1} and ${field2}!`);
                // Force separation to fix contamination
                normalizedData[field2] = structuredClone(normalizedData[field2]);
              }
            }
          }
          
          // FINAL VALIDATION: Comprehensive schema field verification
          console.log('[SCHEMA-DEBUG:getAgentType] Schema fields extracted:', {
            configuration_schema: normalizedData.configuration_schema ?
              typeof normalizedData.configuration_schema : 'undefined',
            runtime_context_schema: normalizedData.runtime_context_schema ?
              typeof normalizedData.runtime_context_schema : 'undefined',
            knowledge_source_config_schema: normalizedData.knowledge_source_config_schema ?
              typeof normalizedData.knowledge_source_config_schema : 'undefined',
            human_in_the_loop_schema: normalizedData.human_in_the_loop_schema ?
              typeof normalizedData.human_in_the_loop_schema : 'undefined'
          });
          
          // Check content preview for all schema fields
          const schemaFields = [
            'configuration_schema',
            'runtime_context_schema',
            'knowledge_source_config_schema',
            'human_in_the_loop_schema'
          ];
          
          console.log('[SCHEMA-DEBUG:getAgentType] Schema content previews:');
          schemaFields.forEach(field => {
            const value = normalizedData[field];
            console.log(`${field}:`, {
              exists: value !== undefined,
              type: typeof value,
              isString: typeof value === 'string',
              hasContent: typeof value === 'string' && value.trim() !== '',
              length: typeof value === 'string' ? value.length : 0,
              preview: value ?
                (typeof value === 'string' ?
                  value.substring(0, 50) + '...' :
                  JSON.stringify(value).substring(0, 50) + '...') :
                'empty'
            });
          });
          
          // VERIFICATION: Confirm schema fields are properly stringified
          // and will display correctly in the form
          const allSchemasValid = schemaFields.every(field => {
            const value = normalizedData[field];
            const isValidStringifiedSchema = typeof value === 'string' && value.trim() !== '';
            
            // If any schema field is not properly stringified, log a warning
            if (!isValidStringifiedSchema) {
              console.warn(`[SCHEMA-DEBUG:getAgentType] WARNING: Schema field '${field}' is not properly stringified:`, {
                value: value === undefined ? 'undefined' :
                      value === null ? 'null' :
                      typeof value === 'string' ? (value.length > 0 ? 'string with content' : 'empty string') :
                      `non-string type: ${typeof value}`
              });
              
              // Last-ditch attempt to fix this field if needed
              if (value === undefined || value === null) {
                normalizedData[field] = '{}' as Json;
                console.log(`[SCHEMA-DEBUG:getAgentType] Applied emergency fix to '${field}': set to empty object string`);
                return true; // Consider this fixed
              } else if (typeof value === 'object') {
                try {
                  normalizedData[field] = JSON.stringify(value, null, 2) as Json;
                  console.log(`[SCHEMA-DEBUG:getAgentType] Applied emergency fix to '${field}': converted object to string`);
                  return true; // Consider this fixed
                } catch (e) {
                  console.error(`[SCHEMA-DEBUG:getAgentType] Failed to apply emergency fix to '${field}'`, e);
                  return false;
                }
              }
              
              return false;
            }
            
            return true;
          });
          
          console.log(`[SCHEMA-DEBUG:getAgentType] Final validation: All schema fields properly stringified: ${allSchemasValid ? 'YES ✅' : 'NO ❌'}`);
        }
      }
      
      // CRITICAL EMERGENCY FIX: Direct assignment of schema fields
      // This is a direct solution to ensure schema fields show up correctly in the form
      // Even if our extraction logic fails, we're going to set these fields directly from the source
      if (process.env.NODE_ENV === 'development') {
        console.log('[DIRECT-FIX] Applying direct schema field assignment');
      }
      
      // First grab the configuration directly
      const config = (result.configuration && typeof result.configuration === 'object' && !Array.isArray(result.configuration))
        ? result.configuration as Record<string, unknown>
        : {} as Record<string, unknown>;
      
      // Define the schema fields we need to guarantee are set
      const criticalSchemaFields = [
        'configuration_schema',
        'runtime_context_schema',
        'knowledge_source_config_schema',
        'human_in_the_loop_schema'
      ];
      
      // ENHANCED SCHEMA EXTRACTION: Process each field completely independently
      criticalSchemaFields.forEach(fieldName => {
        try {
          // Track field processing
          if (process.env.NODE_ENV === 'development') {
            console.log(`[SCHEMA-EXTRACT:getAgentType] Processing ${fieldName}`);
          }
          
          // Get source value with clear isolation
          const sourceValue = config[fieldName];
          
          // Track what we're working with
          if (process.env.NODE_ENV === 'development') {
            console.log(`[SCHEMA-EXTRACT:getAgentType] ${fieldName} source:`, {
              exists: sourceValue !== undefined,
              type: typeof sourceValue,
              isNull: sourceValue === null
            });
          }
          
          // Case 1: Source is a string - parse and re-stringify for consistent formatting
          if (typeof sourceValue === 'string' && sourceValue.trim()) {
            try {
              const parsed = JSON.parse(sourceValue);
              normalizedData[fieldName] = JSON.stringify(parsed, null, 2) as Json;
              if (process.env.NODE_ENV === 'development') {
                console.log(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Successfully processed string to formatted JSON string`);
              }
            } catch (e) {
              console.warn(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Failed to parse string as JSON, using as-is:`, e);
              normalizedData[fieldName] = sourceValue as Json;
            }
          }
          // Case 2: Source is an object - deep clone and stringify
          else if (typeof sourceValue === 'object' && sourceValue !== null) {
            try {
              // Critical: Create completely isolated copy
              const clonedValue = structuredClone(sourceValue);
              normalizedData[fieldName] = JSON.stringify(clonedValue, null, 2) as Json;
              if (process.env.NODE_ENV === 'development') {
                console.log(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Successfully stringified object`);
              }
            } catch (e) {
              console.error(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Failed to clone/stringify object:`, e);
              
              // Fallback: Try direct stringify without clone
              try {
                normalizedData[fieldName] = JSON.stringify(sourceValue, null, 2) as Json;
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Successfully used direct stringify as fallback`);
                }
              } catch (e2) {
                console.error(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Even direct stringify failed:`, e2);
                normalizedData[fieldName] = '{}' as Json;
              }
            }
          }
          // Case 3: Anything else (null, undefined, etc) - set default
          else {
            normalizedData[fieldName] = '{}' as Json;
            if (process.env.NODE_ENV === 'development') {
              console.log(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Using default empty object for missing/invalid value`);
            }
          }
          
          // Verification: Ensure field was properly set with expected type
          if (process.env.NODE_ENV === 'development') {
            console.log(`[SCHEMA-EXTRACT:getAgentType] ${fieldName} result:`, {
              set: normalizedData[fieldName] !== undefined,
              type: typeof normalizedData[fieldName],
              isString: typeof normalizedData[fieldName] === 'string',
              isEmpty: normalizedData[fieldName] === '{}',
              preview: typeof normalizedData[fieldName] === 'string' ?
                (normalizedData[fieldName] as string).substring(0, 30) + '...' : 'not string'
            });
          }
          
          // Emergency validation: Ensure we have a string value for form display
          if (typeof normalizedData[fieldName] !== 'string') {
            console.warn(`[SCHEMA-EXTRACT:getAgentType] ${fieldName}: Result is not a string as expected. Converting...`);
            try {
              normalizedData[fieldName] = JSON.stringify(normalizedData[fieldName], null, 2) as Json;
            } catch {
              normalizedData[fieldName] = '{}' as Json;
            }
          }
          
        } catch (error) {
          // Complete isolation - if one field fails, others should still process
          console.error(`[SCHEMA-EXTRACT:getAgentType] Complete failure processing ${fieldName}:`, error);
          normalizedData[fieldName] = '{}' as Json;
        }
      });
      
      // Double-check what fields were set or missing
      if (process.env.NODE_ENV === 'development') {
        criticalSchemaFields.forEach(field => {
          console.log(`[DIRECT-FIX] ${field}: ${normalizedData[field] ? 'SET ✓' : 'MISSING ✗'} (type: ${typeof normalizedData[field]})`);
        });
      }
      
      // Only update state if the data actually changed
      if (!isEqual(normalizedData, agentType)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Setting agent type with normalized data from getAgentType');
        }
        setAgentType(normalizedData);
      }
      
      return normalizedData;
    } catch (err) {
      console.error(`Error in getAgentType for ${agentId}:`, err);
      return null;
    }
  };
  
  /**
   * Create a new agent type using Edge Function
   */
  const createAgentType = async (agent: Omit<ExtendedAgentType, 'created_at' | 'updated_at'> & { id: string }): Promise<AgentTypeOperationResult> => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    // Client-side validation for required fields
    const validationErrors: string[] = [];
    
    if (!agent.id) {
      validationErrors.push('Agent ID is required');
    } else if (typeof agent.id !== 'string') {
      validationErrors.push('Agent ID must be a string');
    } else if (agent.id.length > 100) {
      validationErrors.push('Agent ID cannot exceed 100 characters');
    }
    
    if (!agent.name) {
      validationErrors.push('Agent name is required');
    } else if (typeof agent.name !== 'string') {
      validationErrors.push('Agent name must be a string');
    } else if (agent.name.length > 255) {
      validationErrors.push('Agent name cannot exceed 255 characters');
    }
    
    // Return validation errors if any
    if (validationErrors.length > 0) {
      return {
        success: false,
        message: `Validation failed: ${validationErrors.join(', ')}`,
        error: {
          type: AgentTypeErrorType.VALIDATION,
          message: 'Validation failed',
          details: validationErrors
        }
      };
    }

    try {
      // Transform data to match backend expectations by restructuring configuration fields
      const {
        // Extract fields that need to be nested in configuration
        configuration_schema,
        runtime_context_schema,
        knowledge_source_config_schema,
        human_in_the_loop_schema,
        default_config_values,
        presentation_config,
        mastra_api_base_url,
        capabilities,
        agent_operational_mode,
        trigger_events,
        customizable_parameters,
        voice_config,
        applicable_metrics,
        chat_ui_settings,
        phone_settings,
        static_overrides,
        presentation_overrides,
        // Leave other fields at top level
        ...topLevelFields
      } = agent;

      // Prepare payload with the structure expected by the backend
      const payload = {
        ...topLevelFields,
        // Ensure ID is included in the payload
        id: agent.id,
        // Nest configuration fields as expected by backend
        configuration: {
          // More robust parsing of schema fields that handles empty strings
          configuration_schema: parseJsonField(configuration_schema),
          runtime_context_schema: parseJsonField(runtime_context_schema),
          knowledge_source_config_schema: parseJsonField(knowledge_source_config_schema),
          human_in_the_loop_schema: parseJsonField(human_in_the_loop_schema),
          default_config_values: default_config_values !== undefined ? default_config_values : {},
          presentation_config: presentation_config !== undefined ? presentation_config : {},
          mastra_api_base_url: mastra_api_base_url,
          capabilities: capabilities,
          agent_operational_mode: agent_operational_mode || 'interactive_user_facing',
          trigger_events: trigger_events,
          customizable_parameters: customizable_parameters,
          voice_config: voice_config,
          applicable_metrics: applicable_metrics,
          chat_ui_settings: chat_ui_settings,
          phone_settings: phone_settings,
          static_overrides: static_overrides,
          presentation_overrides: presentation_overrides,
          // Ensure available_channels is included in the payload
          available_channels: agent.available_channels || []
        }
      };

      // Log request payload in development for debugging
      if (process.env.NODE_ENV === 'development') {
        console.debug('Sending transformed payload to agent-types-create:', JSON.stringify(payload, null, 2));
      }

      // Capture timing for performance monitoring in development
      const startTime = performance.now();
      
      // Use Edge Function to create the agent type
      const { data, error } = await supabase.functions.invoke('agent-types-create', {
        method: 'POST',
        body: payload
      });
      
      // Log response time in development
      if (process.env.NODE_ENV === 'development') {
        const duration = performance.now() - startTime;
        console.debug(`agent-types-create API call completed in ${duration.toFixed(2)}ms`);
      }

      if (error) {
        console.error('Error from agent-types-create:', error);
        throw { 
          type: AgentTypeErrorType.SERVER, 
          message: `Error creating agent type: ${error.message}`,
          details: error
        };
      }

      if (!data || !data.data) {
        console.error('No response data from agent-types-create:', data);
        throw { 
          type: AgentTypeErrorType.SERVER, 
          message: 'Failed to create agent type - no response data' 
        };
      }

      // Process the created agent data
      const createdAgent = data.data as ExtendedAgentType;
      
      // Transform the returned data to match frontend structure
      let transformedAgent: ExtendedAgentType;
      
      if (createdAgent.configuration && typeof createdAgent.configuration === 'object') {
        // Extract configuration fields to top level for frontend use
        const configObj = createdAgent.configuration as Record<string, unknown> || {};
        
        // Convert configuration_schema to string if it's an object
        let configSchemaValue: Json = null;
        if (configObj.configuration_schema) {
          configSchemaValue = typeof configObj.configuration_schema === 'object'
            ? JSON.stringify(configObj.configuration_schema, null, 2) as Json
            : configObj.configuration_schema as Json;
        }
        
        // Enhanced extraction and processing of presentation_config using recursive parsing
        // Use our enhanced parseJsonField function to handle nested JSON strings
        const presentationConfig = configObj.presentation_config ?
          parseJsonField(configObj.presentation_config as Json | string | Record<string, unknown>) :
          ({} as Json);
        
        if (process.env.NODE_ENV === 'development') {
          console.log('Processed presentation_config with recursive parsing in createAgentType:', presentationConfig);
        }
  
        transformedAgent = {
          ...createdAgent,
          // Ensure all configuration fields have proper fallback values
          configuration_schema: configSchemaValue || '' as Json,
          runtime_context_schema: (configObj.runtime_context_schema as Json) || '' as Json,
          knowledge_source_config_schema: (configObj.knowledge_source_config_schema as Json) || '' as Json,
          human_in_the_loop_schema: (configObj.human_in_the_loop_schema as Json) || '' as Json,
          default_config_values: (configObj.default_config_values as Json) || {} as Json,
          presentation_config: presentationConfig, // Use the properly extracted and processed presentation_config
          // CRITICAL FIX: Extract Mastra fields from dedicated columns FIRST, then fallback to configuration
          mastra_agent_id: transformedAgent.mastra_agent_id || (configObj.mastra_agent_id as string) || '',
          mastra_api_base_url: transformedAgent.mastra_api_base_url || (configObj.mastra_api_base_url as string) || '',
          capabilities: Array.isArray(configObj.capabilities) ? configObj.capabilities as string[] : [],
          agent_operational_mode: (configObj.agent_operational_mode as 'interactive_user_facing' | 'autonomous_background') || 'interactive_user_facing',
          trigger_events: Array.isArray(configObj.trigger_events) ? configObj.trigger_events as string[] : [],
          customizable_parameters: (configObj.customizable_parameters as Json) || {} as Json,
          voice_config: (configObj.voice_config as Json) || {} as Json,
          applicable_metrics: (configObj.applicable_metrics as Json) || {} as Json,
          chat_ui_settings: (configObj.chat_ui_settings as Json) || {} as Json,
          phone_settings: (configObj.phone_settings as Json) || {} as Json,
          static_overrides: (configObj.static_overrides as Json) || {} as Json,
          presentation_overrides: (configObj.presentation_overrides as Json) || {} as Json,
          available_channels: Array.isArray(configObj.available_channels) ? configObj.available_channels as string[] : []
        };
      } else {
        // Even if no configuration object is found, ensure the presentation_config exists
        transformedAgent = {
          ...createdAgent,
          presentation_config: {} as Json
        };
      }
      
      setAgentType(transformedAgent);
      
      // Invalidate queries to refetch lists
      queryClient.invalidateQueries({ queryKey: ['agentTypes'] });
      
      // Log success in development environment
      if (process.env.NODE_ENV === 'development') {
        console.debug(`Successfully created agent type "${transformedAgent.id}" via Edge Function`);
      }
      
      return {
        success: true,
        message: data.message || `Successfully created agent type "${transformedAgent.id}"`,
        agent: transformedAgent
      };
    } catch (err) {
      console.error('Error creating agent type via Edge Function:', err);
      
      // Format the error properly
      const formattedError = err as AgentTypeError;
      if (!formattedError.type) {
        // If it's not already a formatted error, format it
        const structuredError = formatError(err);
        
        // Log the structured error for debugging
        console.error('Structured error:', structuredError);
        
        // Create a user-friendly message
        const userMessage = createUserFriendlyErrorMessage(structuredError);
        
        return {
          success: false,
          message: userMessage,
          error: structuredError
        };
      }
      
      // If it's already a formatted error, just create the user message
      const userMessage = createUserFriendlyErrorMessage(formattedError);
      
      return {
        success: false,
        message: userMessage,
        error: formattedError
      };
    }
  };

  /**
   * Update an existing agent type
   */
  const updateAgentType = async (agentId: string, updates: Partial<ExtendedAgentType>): Promise<AgentTypeOperationResult> => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    try {
      // Transform data to match backend expectations by restructuring configuration fields
      const {
        // Extract fields that need to be nested in configuration
        configuration_schema,
        runtime_context_schema,
        knowledge_source_config_schema,
        human_in_the_loop_schema,
        default_config_values,
        presentation_config,
        mastra_agent_id,
        mastra_api_base_url,
        capabilities,
        agent_operational_mode,
        trigger_events,
        customizable_parameters,
        voice_config,
        applicable_metrics,
        chat_ui_settings,
        phone_settings,
        static_overrides,
        presentation_overrides,
        // Leave other fields at top level
        ...topLevelFields
      } = updates;
      
      // Create the transformed update payload
      const payload: Record<string, unknown> = {
        ...topLevelFields,
        id: agentId,
      };
      
      // Always include configuration to ensure all fields are properly updated
      // This allows setting fields to empty values explicitly
      payload.configuration = {
          // Use parseJsonField helper for all schema fields
          configuration_schema: parseJsonField(configuration_schema),
          runtime_context_schema: parseJsonField(runtime_context_schema),
          knowledge_source_config_schema: parseJsonField(knowledge_source_config_schema),
          human_in_the_loop_schema: parseJsonField(human_in_the_loop_schema),
          default_config_values: default_config_values !== undefined ? default_config_values : {},
          presentation_config: presentation_config !== undefined ? presentation_config : {},
          mastra_agent_id,
          mastra_api_base_url,
          capabilities,
          agent_operational_mode,
          trigger_events,
          customizable_parameters,
          voice_config,
          applicable_metrics,
          chat_ui_settings,
          phone_settings,
          static_overrides,
          presentation_overrides,
          // Ensure available_channels is included in the payload
          available_channels: updates.available_channels || []
        }
      
      // Log request payload in development for debugging
      if (process.env.NODE_ENV === 'development') {
        console.debug('Sending transformed update payload to agent-types-update:', JSON.stringify(payload, null, 2));
      }
      
      // Capture timing for performance monitoring in development
      const startTime = performance.now();
      
      // Handle potential network errors with catch
      const { data, error } = await supabase.functions.invoke('agent-types-update', {
        method: 'PUT',
        body: payload
      }).catch(networkError => {
        console.error(`Network error when updating agent type (${agentId}):`, networkError);
        throw {
          type: AgentTypeErrorType.NETWORK,
          message: 'Network error while updating the agent type',
          details: { originalError: networkError },
          originalError: networkError
        };
      });
      
      // Log response time in development
      if (process.env.NODE_ENV === 'development') {
        const duration = performance.now() - startTime;
        console.debug(`agent-types-update API call completed in ${duration.toFixed(2)}ms`);
      }

      // Handle Supabase function invocation error
      if (error) {
        console.error(`API error from agent-types-update for agent ${agentId}:`, error);
        
        // Extract detailed error information if available
        const errorDetails = {
          status: error.status,
          statusText: error.message,
          details: error.details || 'No additional details available'
        };
        
        // Format a user-friendly error based on status code
        let formattedError: AgentTypeError;
        if (error.status === 401 || error.status === 403) {
          formattedError = {
            type: AgentTypeErrorType.PERMISSION,
            message: 'You do not have permission to update this agent type',
            details: errorDetails
          };
        } else if (error.status === 404) {
          formattedError = {
            type: AgentTypeErrorType.NOT_FOUND,
            message: 'The agent type you are trying to update does not exist',
            details: errorDetails
          };
        } else if (error.status === 422) {
          formattedError = {
            type: AgentTypeErrorType.VALIDATION,
            message: 'The agent type update data is invalid',
            details: errorDetails
          };
        } else if (error.status >= 500) {
          formattedError = {
            type: AgentTypeErrorType.SERVER,
            message: 'Server error while updating agent type',
            details: errorDetails
          };
        } else {
          formattedError = {
            type: AgentTypeErrorType.UNKNOWN,
            message: `Error updating agent type: ${error.message}`,
            details: errorDetails
          };
        }
        
        throw formattedError;
      }

      // Handle missing response data
      if (!data) {
        console.error(`No response data when updating agent type (${agentId})`);
        throw {
          type: AgentTypeErrorType.SERVER,
          message: 'Failed to update agent type - no response data received'
        };
      }
      
      // Check for error response structure in the data itself
      if (data.error) {
        console.error(`Error response from agent-types-update for agent ${agentId}:`, data.error, data.details);
        
        // Build a structured error object with all available information
        const formattedDetails = Array.isArray(data.details)
          ? data.details
          : data.details ? [data.details] : [];
          
        // Create structured error based on error content
        const errorType = data.error.toLowerCase().includes('validation')
          ? AgentTypeErrorType.VALIDATION
          : data.error.toLowerCase().includes('permission')
            ? AgentTypeErrorType.PERMISSION
            : data.error.toLowerCase().includes('not found')
              ? AgentTypeErrorType.NOT_FOUND
              : AgentTypeErrorType.SERVER;
        
        throw {
          type: errorType,
          message: data.error || 'Failed to update agent type',
          details: formattedDetails
        };
      }

      // The server returns the updated agent in data.data
      const updatedAgent = data.data as ExtendedAgentType;
      
      // Transform the returned data to match frontend structure
      let transformedAgent: ExtendedAgentType = {
        ...updatedAgent
      };
      
      // Extract nested configuration fields and convert objects to JSON strings for form editing
      if (updatedAgent.configuration && typeof updatedAgent.configuration === 'object') {
        const configObj = updatedAgent.configuration as Record<string, unknown>;
        
        // Convert configuration_schema to string if it's an object
        let configSchemaValue: Json = null;
        if (configObj.configuration_schema) {
          configSchemaValue = typeof configObj.configuration_schema === 'object'
            ? JSON.stringify(configObj.configuration_schema, null, 2) as Json
            : configObj.configuration_schema as Json;
        }
        
        // Enhanced extraction and processing of presentation_config using recursive parsing
        if (process.env.NODE_ENV === 'development') {
          console.log('Processing presentation_config from API response:', {
            hasConfig: !!configObj.presentation_config,
            configType: typeof configObj.presentation_config
          });
        }
        
        // Use our enhanced parseJsonField function to handle nested JSON strings
        const presentationConfig = configObj.presentation_config ?
          parseJsonField(configObj.presentation_config as Json | string | Record<string, unknown>) :
          ({} as Json);
          
        if (process.env.NODE_ENV === 'development') {
          console.log('Processed presentation_config with recursive parsing in updateAgentType:', presentationConfig);
        }
        
        // Ensure available_channels is properly extracted and has a fallback
        let availableChannels: string[] = [];
        if (Array.isArray(configObj.available_channels)) {
          availableChannels = configObj.available_channels as string[];
          console.log('extracted available_channels as array:', availableChannels);
        } else if (typeof configObj.available_channels === 'string') {
          try {
            const parsed = JSON.parse(configObj.available_channels);
            if (Array.isArray(parsed)) {
              availableChannels = parsed;
              console.log('parsed available_channels from string:', availableChannels);
            }
          } catch (e) {
            console.warn('Failed to parse available_channels:', e);
          }
        }
        
        // Debug log for available_channels
        console.log('Final available_channels value:', availableChannels);
        
        transformedAgent = {
          ...transformedAgent,
          // Set configuration_schema as a string for the form with proper fallbacks
          configuration_schema: configSchemaValue || '' as Json,
          // Add other configuration fields at the top level for frontend use with proper fallbacks
          runtime_context_schema: (configObj.runtime_context_schema as Json) || '' as Json,
          knowledge_source_config_schema: (configObj.knowledge_source_config_schema as Json) || '' as Json,
          human_in_the_loop_schema: (configObj.human_in_the_loop_schema as Json) || '' as Json,
          default_config_values: (configObj.default_config_values as Json) || {} as Json,
          presentation_config: presentationConfig,
          mastra_api_base_url: (configObj.mastra_api_base_url as string) || '',
          capabilities: Array.isArray(configObj.capabilities) ? configObj.capabilities as string[] : [],
          agent_operational_mode: (configObj.agent_operational_mode as 'interactive_user_facing' | 'autonomous_background') || 'interactive_user_facing',
          trigger_events: Array.isArray(configObj.trigger_events) ? configObj.trigger_events as string[] : [],
          customizable_parameters: (configObj.customizable_parameters as Json) || {} as Json,
          voice_config: (configObj.voice_config as Json) || {} as Json,
          applicable_metrics: (configObj.applicable_metrics as Json) || {} as Json,
          chat_ui_settings: (configObj.chat_ui_settings as Json) || {} as Json,
          phone_settings: (configObj.phone_settings as Json) || {} as Json,
          static_overrides: (configObj.static_overrides as Json) || {} as Json,
          presentation_overrides: (configObj.presentation_overrides as Json) || {} as Json,
          available_channels: availableChannels
        };
      } else {
        // Ensure default values even when configuration is missing
        transformedAgent = {
          ...transformedAgent,
          configuration_schema: '',
          runtime_context_schema: '',
          knowledge_source_config_schema: '',
          human_in_the_loop_schema: '',
          default_config_values: {},
          presentation_config: {},
          mastra_agent_id: '',
          mastra_api_base_url: '',
          capabilities: [],
          agent_operational_mode: 'interactive_user_facing',
          trigger_events: [],
          customizable_parameters: {},
          voice_config: {},
          applicable_metrics: {},
          chat_ui_settings: {},
          phone_settings: {},
          static_overrides: {},
          presentation_overrides: {},
          available_channels: []
        };
      }
      
      setAgentType(transformedAgent);
      
      // Don't invalidate the current query as we already have the updated data
      // Only invalidate the list query to reflect changes in lists
      queryClient.invalidateQueries({ queryKey: ['agentTypes'] });
      
      // Construct a proper response object with success field
      return {
        success: true,
        message: data.message || `Successfully updated agent type "${agentId}"`,
        agent: transformedAgent
      };
    } catch (err) {
      console.error(`Error updating agent type (${agentId}):`, err);
      
      // Format the error properly
      const formattedError = err as AgentTypeError;
      if (!formattedError.type) {
        // If it's not already a formatted error, format it
        const structuredError = formatError(err);
        
        // Create a user-friendly message
        const userMessage = createUserFriendlyErrorMessage(structuredError);
        
        return {
          success: false,
          message: userMessage,
          error: structuredError
        };
      }
      
      // If it's already a formatted error, just create the user message
      const userMessage = createUserFriendlyErrorMessage(formattedError);
      
      return {
        success: false,
        message: userMessage,
        error: formattedError
      };
    }
  };

  /**
   * Delete an agent type
   */
  const deleteAgentType = async (agentId: string): Promise<AgentTypeOperationResult> => {
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    try {
      // Capture timing for performance monitoring
      const startTime = performance.now();
      
      // Handle potential network errors with catch
      const { data, error } = await supabase.functions.invoke(`agent-types-delete/${agentId}`, {
        method: 'DELETE'
      }).catch(networkError => {
        console.error(`Network error when deleting agent type (${agentId}):`, networkError);
        throw {
          type: AgentTypeErrorType.NETWORK,
          message: 'Network error while deleting the agent type',
          details: { originalError: networkError },
          originalError: networkError
        };
      });
      
      // Log response time in development
      if (process.env.NODE_ENV === 'development') {
        const duration = performance.now() - startTime;
        console.debug(`agent-types-delete API call completed in ${duration.toFixed(2)}ms`);
      }

      // Handle Supabase function invocation error
      if (error) {
        console.error(`API error from agent-types-delete for agent ${agentId}:`, error);
        
        // Extract detailed error information
        const errorDetails = {
          status: error.status,
          statusText: error.message,
          details: error.details || 'No additional details available'
        };
        
        // Format a user-friendly error based on status code
        let formattedError: AgentTypeError;
        if (error.status === 401 || error.status === 403) {
          formattedError = {
            type: AgentTypeErrorType.PERMISSION,
            message: 'You do not have permission to delete this agent type',
            details: errorDetails
          };
        } else if (error.status === 404) {
          formattedError = {
            type: AgentTypeErrorType.NOT_FOUND,
            message: 'The agent type you are trying to delete does not exist',
            details: errorDetails
          };
        } else if (error.status >= 500) {
          formattedError = {
            type: AgentTypeErrorType.SERVER,
            message: 'Server error while deleting agent type',
            details: errorDetails
          };
        } else {
          formattedError = {
            type: AgentTypeErrorType.UNKNOWN,
            message: `Error deleting agent type: ${error.message}`,
            details: errorDetails
          };
        }
        
        throw formattedError;
      }

      // Handle missing or unsuccessful response data
      if (!data || !data.success) {
        console.error(`Unsuccessful response when deleting agent type (${agentId}):`, data);
        throw {
          type: AgentTypeErrorType.SERVER,
          message: data?.message || 'Failed to delete agent type - operation unsuccessful',
          details: data || {}
        };
      }

      setAgentType(null);
      
      // Invalidate queries to refetch lists
      queryClient.invalidateQueries({ queryKey: ['agentTypes'] });
      
      return data as AgentTypeOperationResult;
    } catch (err) {
      console.error(`Error deleting agent type (${agentId}):`, err);
      
      // Format the error properly
      const formattedError = err as AgentTypeError;
      if (!formattedError.type) {
        // If it's not already a formatted error, format it
        const structuredError = formatError(err);
        
        // Create a user-friendly message
        const userMessage = createUserFriendlyErrorMessage(structuredError);
        
        return {
          success: false,
          message: userMessage,
          error: structuredError
        };
      }
      
      // If it's already a formatted error, just create the user message
      const userMessage = createUserFriendlyErrorMessage(formattedError);
      
      return {
        success: false,
        message: userMessage,
        error: formattedError
      };
    }
  };

  /**
   * Change the status of an agent type
   */
  const changeAgentTypeStatus = async (
    agentId: string, 
    status: 'draft' | 'pending' | 'published' | 'deprecated' | 'archived'
  ): Promise<AgentTypeOperationResult> => {
    return updateAgentType(agentId, { status });
  };

  return {
    agentType,
    loading: query.isLoading || query.isFetching,
    error: query.error,
    getAgentType,
    createAgentType,
    updateAgentType,
    deleteAgentType,
    changeAgentTypeStatus,
  };
}