# REP-106 Phase 3: Fly.io Agent Coordination Implementation

**Implementation Date:** June 24, 2025  
**Status:** ✅ Complete  
**Phase:** 3 of 8 (Agent Coordination)

## Overview

This document details the implementation of Phase 3 of the REP-106 implementation plan: Fly.io Agent Coordination. This phase enhances the voice agent to automatically discover and join Rep Room sessions using the new `rrs-*` room naming pattern and session-specific configuration.

## Implementation Summary

### Task 3.1: Agent Room Discovery with rrs-* Pattern Matching ✅

**Implemented Components:**
- `RepRoomSessionManager` class for session discovery and management
- Room name parsing with regex pattern `^rrs-([^-]+)-(.+)$`
- Automatic session registration and tracking
- Session-specific metadata extraction

**Key Features:**
- **Pattern Matching:** Detects rooms with format `rrs-{slug}-{sessionId}`
- **Session Parsing:** Extracts slug and session ID from room names
- **Active Session Tracking:** Maintains registry of active sessions
- **Metadata Integration:** Merges room metadata with session context

### Task 3.2: Session-Specific Agent Configuration ✅

**Enhanced Configuration Loading:**
- Session-aware metadata parsing from room metadata
- Enhanced context extraction with session parameters
- Session-specific thread ID generation
- Improved parameter validation for session contexts

**Session Context Parameters:**
```python
{
    'sessionId': session_info['session_id'],
    'repRoomSlug': session_info['slug'],
    'roomName': session_info['room_name'],
    'sessionType': 'persistent_voice',
    'threadId': f"session-{session_info['session_id']}"
}
```

### Task 3.3: Webhook-Based Agent Coordination ✅

**Webhook Handler Implementation:**
- `/webhook/livekit` endpoint for LiveKit event processing
- Support for `room_started`, `participant_joined`, and `room_finished` events
- Session state management and participant tracking
- Automatic session cleanup on room finish

**Webhook Event Processing:**
- **room_started:** Registers new session with metadata
- **participant_joined:** Updates session with participant information
- **room_finished:** Cleans up session state

## Technical Implementation Details

### 1. RepRoomSessionManager Class

```python
class RepRoomSessionManager:
    """Manages Rep Room session discovery and coordination for REP-106 Phase 3."""
    
    def __init__(self):
        self.active_sessions = {}  # session_id -> session_info
        self.room_pattern = re.compile(r'^rrs-([^-]+)-(.+)$')
        
    def parse_room_name(self, room_name: str) -> Optional[Dict[str, str]]:
        """Parse room name to extract slug and session ID."""
        
    def should_join_room(self, room_name: str) -> bool:
        """Determine if agent should join this room."""
        
    def register_session(self, session_info: Dict[str, Any]):
        """Register an active session."""
```

### 2. Enhanced Agent Entrypoint

**Session Detection:**
```python
# REP-106 Phase 3: Check if this is a rep room session
session_info = session_manager.parse_room_name(ctx.room.name)
if session_info:
    logger.info(f"[REP-106] Detected Rep Room session: slug='{session_info['slug']}', session_id='{session_info['session_id']}'")
    session_manager.register_session(session_info)
```

**Enhanced Context Merging:**
```python
# REP-106 Phase 3: If we have session info from room name parsing, merge it
if session_info:
    room_context.update({
        'repRoomSlug': session_info['slug'],
        'sessionId': session_info['session_id'],
        'roomName': session_info['room_name']
    })
```

### 3. Session-Aware Data Channel Communication

**Enhanced Event Structure:**
```python
# REP-106 Phase 3: Enhanced events with session information
start_event = {
    "type": "TEXT_MESSAGE_START",
    "messageId": message_id,
    "role": role,
    "timestamp": base_timestamp
}
if session_id:
    start_event["sessionId"] = session_id
```

### 4. Webhook Server Integration

**Health Check Enhancement:**
```python
@app.route('/health')
def health_check():
    return jsonify({
        "status": "healthy",
        "service": "stable-voice-agent",
        "version": "v7.1-stable-rep106",
        "active_sessions": len(session_manager.active_sessions)
    })
```

**LiveKit Webhook Handler:**
```python
@app.route('/webhook/livekit', methods=['POST'])
def livekit_webhook_handler():
    """Handle LiveKit webhook events for REP-106 Phase 3 coordination."""
```

## Integration Points

### 1. Room Name Pattern Matching
- **Pattern:** `rrs-{slug}-{sessionId}`
- **Example:** `rrs-t1-3d43005b-936c-472f-9565-c260eebcca9b`
- **Extraction:** Slug = `t1`, Session ID = `3d43005b-936c-472f-9565-c260eebcca9b`

### 2. Session State Management
- **Registration:** When agent joins room
- **Updates:** On participant events
- **Cleanup:** On room finish or agent disconnect

### 3. Enhanced Logging
- **Session Context:** All logs include `[REP-106]` prefix
- **Session Tracking:** Periodic status reports with participant counts
- **Error Handling:** Session-aware error reporting

## Configuration Requirements

### Environment Variables
- `DATABASE_URL`: For session metadata queries
- `SUPABASE_URL`: For edge function integration
- `SUPABASE_ANON_KEY`: For authentication

### LiveKit Configuration
- Room naming pattern: `rrs-{slug}-{sessionId}`
- Webhook endpoint: `https://agent-domain/webhook/livekit`
- Metadata format: JSON with session context

## Testing and Validation

### 1. Room Discovery Testing
```bash
# Test room name parsing
python -c "
from server.agent_stable import session_manager
result = session_manager.parse_room_name('rrs-t1-session-123')
print(result)
"
```

### 2. Session Management Testing
- Create session with `rrs-*` pattern
- Verify agent joins automatically
- Check session registration in logs
- Validate participant tracking

### 3. Webhook Testing
```bash
# Test webhook endpoint
curl -X POST http://localhost:8080/webhook/livekit \
  -H "Content-Type: application/json" \
  -d '{
    "event": "room_started",
    "room": {"name": "rrs-t1-session-123", "metadata": {}}
  }'
```

## Performance Considerations

### 1. Session Registry
- **Memory Usage:** Minimal overhead per session
- **Cleanup:** Automatic on room finish
- **Scalability:** Supports multiple concurrent sessions

### 2. Pattern Matching
- **Regex Performance:** Compiled pattern for efficiency
- **Early Exit:** Non-matching rooms ignored quickly
- **Logging:** Minimal overhead for non-rep-rooms

### 3. Webhook Processing
- **Async Handling:** Non-blocking event processing
- **Error Recovery:** Graceful handling of malformed events
- **Rate Limiting:** Built-in Flask rate limiting

## Security Considerations

### 1. Webhook Validation
- **Content-Type:** JSON validation
- **Payload Size:** Reasonable limits
- **Error Handling:** No sensitive data in error responses

### 2. Session Access
- **Room Metadata:** Validated session context
- **Participant Identity:** Tracked but not exposed
- **Cleanup:** Automatic session data removal

## Deployment Notes

### 1. Fly.io Configuration
- **Health Check:** Enhanced with session count
- **Port Mapping:** 8080 for health and webhooks
- **Environment:** Production-ready logging

### 2. LiveKit Integration
- **Webhook URL:** Configure in LiveKit dashboard
- **Event Types:** Enable room and participant events
- **Retry Logic:** Built-in webhook retry handling

## Next Steps (Phase 4)

1. **Real-time Features:** Enhanced participant management
2. **Data Channels:** Structured message format improvements
3. **Chat Integration:** Session-aware chat persistence
4. **UI Updates:** Real-time session status display

## Troubleshooting

### Common Issues

1. **Agent Not Joining Sessions**
   - Check room name pattern: `rrs-{slug}-{sessionId}`
   - Verify agent logs for pattern matching
   - Ensure session metadata is present

2. **Session Not Registered**
   - Check webhook configuration
   - Verify LiveKit event delivery
   - Review session manager logs

3. **Missing Session Context**
   - Validate room metadata format
   - Check context merging logic
   - Verify parameter extraction

### Debug Commands

```bash
# Check active sessions
curl http://localhost:8080/health

# View agent logs
docker logs agent-container | grep REP-106

# Test room pattern
python -c "import re; print(re.match(r'^rrs-([^-]+)-(.+)$', 'rrs-t1-session-123').groups())"
```

## Conclusion

Phase 3 implementation successfully enables automatic agent coordination for Rep Room sessions with:

- ✅ **Room Discovery:** Automatic detection of `rrs-*` pattern rooms
- ✅ **Session Management:** Complete session lifecycle tracking
- ✅ **Webhook Integration:** Real-time event processing
- ✅ **Enhanced Configuration:** Session-specific agent setup
- ✅ **Data Channel Enhancement:** Session-aware communication

The agent now automatically joins Rep Room sessions and maintains session-specific context throughout the conversation, providing the foundation for the persistent session experience defined in the REP-106 implementation plan.