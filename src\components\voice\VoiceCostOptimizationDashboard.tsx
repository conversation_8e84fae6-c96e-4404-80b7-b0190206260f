import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { 
  Activity, 
  DollarSign, 
  Mic, 
  MicOff, 
  TrendingDown, 
  TrendingUp,
  Clock,
  Zap,
  BarChart3,
  RefreshCw
} from 'lucide-react';

interface VoiceCostStats {
  sessionDuration: number;
  totalSttTime: number;
  sttActivations: number;
  sttEfficiency: number;
  estimatedCostSavings: number;
  vadState: string;
  audioLevel: number;
}

interface VoiceCostOptimizationDashboardProps {
  costStats: VoiceCostStats;
  vadOptimization: {
    enabled: boolean;
    state: string;
    audioLevel: number;
    speechDetected: boolean;
    sttSessionActive: boolean;
  };
  onResetStats?: () => void;
  onToggleOptimization?: () => void;
  className?: string;
}

export function VoiceCostOptimizationDashboard({
  costStats,
  vadOptimization,
  onResetStats,
  onToggleOptimization,
  className = ''
}: VoiceCostOptimizationDashboardProps) {
  const [realtimeStats, setRealtimeStats] = useState(costStats);
  const [previousStats, setPreviousStats] = useState(costStats);

  // Update stats and track changes
  useEffect(() => {
    setPreviousStats(realtimeStats);
    setRealtimeStats(costStats);
  }, [costStats, realtimeStats]);

  // Calculate cost savings in dollars (estimated)
  const estimatedMonthlySavings = (costStats.estimatedCostSavings / 100) * 50; // Assuming $50/month baseline
  const estimatedDailySavings = estimatedMonthlySavings / 30;

  // Format time duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Get VAD state color and icon
  const getVADStateInfo = (state: string) => {
    switch (state.toLowerCase()) {
      case 'idle':
        return { color: 'bg-gray-500', icon: MicOff, label: 'Idle' };
      case 'speech_detected':
        return { color: 'bg-yellow-500', icon: Activity, label: 'Speech Detected' };
      case 'processing':
        return { color: 'bg-green-500', icon: Mic, label: 'Processing' };
      case 'cooldown':
        return { color: 'bg-blue-500', icon: Clock, label: 'Cooldown' };
      default:
        return { color: 'bg-gray-500', icon: MicOff, label: 'Unknown' };
    }
  };

  const vadStateInfo = getVADStateInfo(vadOptimization.state);
  const VADIcon = vadStateInfo.icon;

  // Calculate efficiency trend
  const efficiencyTrend = realtimeStats.sttEfficiency - previousStats.sttEfficiency;
  const TrendIcon = efficiencyTrend >= 0 ? TrendingUp : TrendingDown;
  const trendColor = efficiencyTrend >= 0 ? 'text-red-500' : 'text-green-500';

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Zap className="h-5 w-5 text-yellow-500" />
          <h3 className="text-lg font-semibold">Voice Cost Optimization</h3>
          <Badge variant={vadOptimization.enabled ? 'default' : 'secondary'}>
            {vadOptimization.enabled ? 'Enabled' : 'Disabled'}
          </Badge>
        </div>
        <div className="flex space-x-2">
          {onToggleOptimization && (
            <Button
              variant="outline"
              size="sm"
              onClick={onToggleOptimization}
              className="flex items-center space-x-1"
            >
              <Zap className="h-4 w-4" />
              <span>{vadOptimization.enabled ? 'Disable' : 'Enable'} VAD</span>
            </Button>
          )}
          {onResetStats && (
            <Button
              variant="outline"
              size="sm"
              onClick={onResetStats}
              className="flex items-center space-x-1"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Reset</span>
            </Button>
          )}
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Cost Savings */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {costStats.estimatedCostSavings.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              ~${estimatedDailySavings.toFixed(2)}/day saved
            </p>
          </CardContent>
        </Card>

        {/* STT Efficiency */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">STT Efficiency</CardTitle>
            <div className="flex items-center space-x-1">
              <TrendIcon className={`h-4 w-4 ${trendColor}`} />
              <BarChart3 className="h-4 w-4 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {costStats.sttEfficiency.toFixed(1)}%
            </div>
            <Progress value={costStats.sttEfficiency} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {costStats.sttActivations} activations
            </p>
          </CardContent>
        </Card>

        {/* Session Duration */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Session Time</CardTitle>
            <Clock className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(costStats.sessionDuration)}
            </div>
            <p className="text-xs text-muted-foreground">
              STT: {formatDuration(costStats.totalSttTime)}
            </p>
          </CardContent>
        </Card>

        {/* VAD State */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VAD State</CardTitle>
            <VADIcon className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${vadStateInfo.color}`} />
              <span className="text-sm font-medium">{vadStateInfo.label}</span>
            </div>
            <div className="mt-2">
              <div className="text-xs text-muted-foreground">Audio Level</div>
              <Progress 
                value={vadOptimization.audioLevel * 100} 
                className="mt-1 h-2"
              />
            </div>
            {vadOptimization.sttSessionActive && (
              <Badge variant="outline" className="mt-2 text-xs">
                STT Active
              </Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Optimization Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-muted-foreground">Total STT Time</div>
              <div className="text-lg font-semibold">
                {formatDuration(costStats.totalSttTime)}
              </div>
            </div>
            <div>
              <div className="font-medium text-muted-foreground">STT Activations</div>
              <div className="text-lg font-semibold">
                {costStats.sttActivations}
              </div>
            </div>
            <div>
              <div className="font-medium text-muted-foreground">Avg Session Length</div>
              <div className="text-lg font-semibold">
                {costStats.sttActivations > 0 
                  ? formatDuration(costStats.totalSttTime / costStats.sttActivations)
                  : '0s'
                }
              </div>
            </div>
            <div>
              <div className="font-medium text-muted-foreground">Est. Monthly Savings</div>
              <div className="text-lg font-semibold text-green-600">
                ${estimatedMonthlySavings.toFixed(2)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Audio Visualization */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Real-time Audio Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Audio Level Meter */}
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Audio Level</span>
                <span>{(vadOptimization.audioLevel * 100).toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 relative overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-green-400 via-yellow-400 to-red-500 transition-all duration-100"
                  style={{ width: `${vadOptimization.audioLevel * 100}%` }}
                />
                {/* Speech threshold indicator */}
                <div 
                  className="absolute top-0 h-full w-0.5 bg-red-600"
                  style={{ left: '50%' }}
                  title="Speech Threshold"
                />
              </div>
            </div>

            {/* Status Indicators */}
            <div className="flex space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${vadOptimization.speechDetected ? 'bg-green-500' : 'bg-gray-300'}`} />
                <span>Speech Detected</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${vadOptimization.sttSessionActive ? 'bg-blue-500' : 'bg-gray-300'}`} />
                <span>STT Session</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${vadOptimization.enabled ? 'bg-yellow-500' : 'bg-gray-300'}`} />
                <span>VAD Optimization</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cost Optimization Tips */}
      {costStats.sttEfficiency > 80 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-4">
            <div className="flex items-start space-x-2">
              <TrendingUp className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <div className="font-medium text-yellow-800">High STT Usage Detected</div>
                <div className="text-sm text-yellow-700 mt-1">
                  Your STT efficiency is {costStats.sttEfficiency.toFixed(1)}%. Consider adjusting VAD sensitivity 
                  or checking for background noise to optimize costs further.
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {costStats.estimatedCostSavings > 50 && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-4">
            <div className="flex items-start space-x-2">
              <DollarSign className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <div className="font-medium text-green-800">Excellent Cost Optimization!</div>
                <div className="text-sm text-green-700 mt-1">
                  You're saving {costStats.estimatedCostSavings.toFixed(1)}% on voice processing costs. 
                  Keep up the great work with VAD optimization!
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default VoiceCostOptimizationDashboard;