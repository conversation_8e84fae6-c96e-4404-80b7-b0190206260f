import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '../contexts/supabase-context';

export interface SessionValidationResult {
  isValid: boolean;
  exists: boolean;
  hasAccess: boolean;
  isExpired: boolean;
  requiresPassword: boolean;
  error: string | null;
  sessionData?: {
    id: string;
    slug: string;
    status: string;
    created_at: string;
    expires_at?: string;
    max_participants?: number;
    current_participants: number;
    metadata?: Record<string, unknown>;
  };
}

export interface SessionValidationConfig {
  sessionId: string;
  slug: string;
  password?: string;
  userId?: string;
  onValidationChange?: (result: SessionValidationResult) => void;
  onError?: (error: string) => void;
}

/**
 * useSessionValidation Hook - Phase 6: Error Handling & Edge Cases
 * 
 * Features:
 * - Session existence validation
 * - Access control and permissions
 * - Session expiration checking
 * - Password protection support
 * - Participant limit enforcement
 * - Rate limiting protection
 * - Real-time session status monitoring
 */
export function useSessionValidation(config: SessionValidationConfig) {
  const {
    sessionId,
    slug,
    password,
    userId,
    onValidationChange,
    onError
  } = config;

  const [validationResult, setValidationResult] = useState<SessionValidationResult>({
    isValid: false,
    exists: false,
    hasAccess: false,
    isExpired: false,
    requiresPassword: false,
    error: null
  });

  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] = useState<number>(0);

  // Get Supabase client from context
  const supabase = useSupabaseClient();

  // Validate session ID format
  const isValidSessionIdFormat = useCallback((sessionId: string): boolean => {
    // Session ID should be 12 characters, alphanumeric + URL-safe characters
    return /^[A-Za-z0-9_-]{12}$/.test(sessionId);
  }, []);

  // Check if session is expired
  const isSessionExpired = useCallback((expiresAt?: string): boolean => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  }, []);

  // Validate session with comprehensive checks
  const validateSession = useCallback(async (): Promise<SessionValidationResult> => {
    if (!sessionId || !slug) {
      return {
        isValid: false,
        exists: false,
        hasAccess: false,
        isExpired: false,
        requiresPassword: false,
        error: 'Missing session ID or slug'
      };
    }

    // Validate session ID format
    if (!isValidSessionIdFormat(sessionId)) {
      return {
        isValid: false,
        exists: false,
        hasAccess: false,
        isExpired: false,
        requiresPassword: false,
        error: 'Invalid session ID format'
      };
    }

    try {
      // Check if session exists and get session data
      const { data: sessionData, error: sessionError } = await supabase
        .from('rep_room_sessions')
        .select(`
          session_id,
          slug,
          status,
          created_at,
          expires_at,
          max_participants,
          password_hash,
          metadata,
          rep_room_participants!inner(count)
        `)
        .eq('session_id', sessionId)
        .eq('slug', slug)
        .single();

      if (sessionError || !sessionData) {
        return {
          isValid: false,
          exists: false,
          hasAccess: false,
          isExpired: false,
          requiresPassword: false,
          error: 'Session not found'
        };
      }

      // Check if session is expired
      const expired = isSessionExpired(sessionData.expires_at);
      if (expired) {
        return {
          isValid: false,
          exists: true,
          hasAccess: false,
          isExpired: true,
          requiresPassword: false,
          error: 'Session has expired',
          sessionData: {
            id: sessionData.session_id,
            slug: sessionData.slug,
            status: sessionData.status,
            created_at: sessionData.created_at,
            expires_at: sessionData.expires_at,
            max_participants: sessionData.max_participants,
            current_participants: sessionData.rep_room_participants?.length || 0,
            metadata: sessionData.metadata
          }
        };
      }

      // Check if session is active
      if (sessionData.status !== 'active') {
        return {
          isValid: false,
          exists: true,
          hasAccess: false,
          isExpired: false,
          requiresPassword: false,
          error: `Session is ${sessionData.status}`,
          sessionData: {
            id: sessionData.session_id,
            slug: sessionData.slug,
            status: sessionData.status,
            created_at: sessionData.created_at,
            expires_at: sessionData.expires_at,
            max_participants: sessionData.max_participants,
            current_participants: sessionData.rep_room_participants?.length || 0,
            metadata: sessionData.metadata
          }
        };
      }

      // Check participant limit
      const currentParticipants = sessionData.rep_room_participants?.length || 0;
      if (sessionData.max_participants && currentParticipants >= sessionData.max_participants) {
        // Check if user is already a participant
        if (userId) {
          const { data: existingParticipant } = await supabase
            .from('rep_room_participants')
            .select('participant_id')
            .eq('session_id', sessionId)
            .eq('participant_id', userId)
            .single();

          if (!existingParticipant) {
            return {
              isValid: false,
              exists: true,
              hasAccess: false,
              isExpired: false,
              requiresPassword: false,
              error: 'Session is full',
              sessionData: {
                id: sessionData.session_id,
                slug: sessionData.slug,
                status: sessionData.status,
                created_at: sessionData.created_at,
                expires_at: sessionData.expires_at,
                max_participants: sessionData.max_participants,
                current_participants: currentParticipants,
                metadata: sessionData.metadata
              }
            };
          }
        }
      }

      // Check password protection
      const requiresPassword = !!sessionData.password_hash;
      if (requiresPassword && !password) {
        return {
          isValid: false,
          exists: true,
          hasAccess: false,
          isExpired: false,
          requiresPassword: true,
          error: 'Session requires password',
          sessionData: {
            id: sessionData.session_id,
            slug: sessionData.slug,
            status: sessionData.status,
            created_at: sessionData.created_at,
            expires_at: sessionData.expires_at,
            max_participants: sessionData.max_participants,
            current_participants: currentParticipants,
            metadata: sessionData.metadata
          }
        };
      }

      // Validate password if provided
      if (requiresPassword && password) {
        // In a real implementation, you would hash the password and compare
        // For now, we'll use a simple comparison (this should be done server-side)
        const { data: passwordValid, error: passwordError } = await supabase.rpc(
          'validate_session_password',
          {
            session_id: sessionId,
            password_input: password
          }
        );

        if (passwordError || !passwordValid) {
          return {
            isValid: false,
            exists: true,
            hasAccess: false,
            isExpired: false,
            requiresPassword: true,
            error: 'Invalid password',
            sessionData: {
              id: sessionData.session_id,
              slug: sessionData.slug,
              status: sessionData.status,
              created_at: sessionData.created_at,
              expires_at: sessionData.expires_at,
              max_participants: sessionData.max_participants,
              current_participants: currentParticipants,
              metadata: sessionData.metadata
            }
          };
        }
      }

      // All checks passed
      return {
        isValid: true,
        exists: true,
        hasAccess: true,
        isExpired: false,
        requiresPassword,
        error: null,
        sessionData: {
          id: sessionData.session_id,
          slug: sessionData.slug,
          status: sessionData.status,
          created_at: sessionData.created_at,
          expires_at: sessionData.expires_at,
          max_participants: sessionData.max_participants,
          current_participants: currentParticipants,
          metadata: sessionData.metadata
        }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation failed';
      return {
        isValid: false,
        exists: false,
        hasAccess: false,
        isExpired: false,
        requiresPassword: false,
        error: errorMessage
      };
    }
  }, [sessionId, slug, password, userId, isValidSessionIdFormat, isSessionExpired, supabase]);

  // Perform validation with rate limiting
  const performValidation = useCallback(async () => {
    const now = Date.now();
    const MIN_VALIDATION_INTERVAL = 1000; // 1 second

    // Rate limiting: prevent too frequent validations
    if (now - lastValidation < MIN_VALIDATION_INTERVAL) {
      console.log('[SessionValidation] Rate limited, skipping validation');
      return;
    }

    setIsValidating(true);
    setLastValidation(now);

    try {
      const result = await validateSession();
      setValidationResult(result);
      onValidationChange?.(result);

      if (result.error) {
        onError?.(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation failed';
      const errorResult: SessionValidationResult = {
        isValid: false,
        exists: false,
        hasAccess: false,
        isExpired: false,
        requiresPassword: false,
        error: errorMessage
      };

      setValidationResult(errorResult);
      onValidationChange?.(errorResult);
      onError?.(errorMessage);
    } finally {
      setIsValidating(false);
    }
  }, [validateSession, lastValidation, onValidationChange, onError]);

  // Initial validation
  useEffect(() => {
    if (sessionId && slug) {
      performValidation();
    }
  }, [sessionId, slug, password, performValidation]);

  // Re-validate when dependencies change
  const revalidate = useCallback(() => {
    performValidation();
  }, [performValidation]);

  // Check if user can join session
  const canJoinSession = useCallback((): boolean => {
    return validationResult.isValid && 
           validationResult.hasAccess && 
           !validationResult.isExpired &&
           (!validationResult.requiresPassword || !!password);
  }, [validationResult, password]);

  // Get user-friendly error message
  const getErrorMessage = useCallback((): string | null => {
    if (!validationResult.error) return null;

    switch (validationResult.error) {
      case 'Session not found':
        return 'This session does not exist or has been deleted.';
      case 'Session has expired':
        return 'This session has expired and is no longer available.';
      case 'Session is full':
        return 'This session has reached its maximum number of participants.';
      case 'Session requires password':
        return 'This session is password protected. Please enter the password to join.';
      case 'Invalid password':
        return 'The password you entered is incorrect. Please try again.';
      case 'Invalid session ID format':
        return 'The session link appears to be invalid. Please check the URL and try again.';
      default:
        return validationResult.error;
    }
  }, [validationResult.error]);

  return {
    validationResult,
    isValidating,
    revalidate,
    canJoinSession: canJoinSession(),
    errorMessage: getErrorMessage(),
    isValid: validationResult.isValid,
    exists: validationResult.exists,
    hasAccess: validationResult.hasAccess,
    isExpired: validationResult.isExpired,
    requiresPassword: validationResult.requiresPassword,
    sessionData: validationResult.sessionData
  };
}

export default useSessionValidation;