import { useState, useEffect, useCallback, useRef } from 'react';

export type ConnectionStatus = 
  | 'disconnected' 
  | 'connecting' 
  | 'connected' 
  | 'reconnecting' 
  | 'failed'
  | 'offline';

export interface ConnectionState {
  status: ConnectionStatus;
  isOnline: boolean;
  lastConnected: number | null;
  reconnectAttempts: number;
  error: string | null;
  latency: number | null;
}

export interface ConnectionConfig {
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  exponentialBackoff?: boolean;
  maxReconnectInterval?: number;
  pingInterval?: number;
  pingTimeout?: number;
  onStatusChange?: (status: ConnectionStatus) => void;
  onError?: (error: string) => void;
}

/**
 * useConnectionState Hook - Phase 6: Error Handling & Edge Cases
 * 
 * Features:
 * - Network connectivity monitoring
 * - Automatic reconnection with exponential backoff
 * - Connection state management
 * - Latency monitoring
 * - Offline/online detection
 * - Configurable retry logic
 */
export function useConnectionState(config: ConnectionConfig = {}) {
  const {
    maxReconnectAttempts = 5,
    reconnectInterval = 2000,
    exponentialBackoff = true,
    maxReconnectInterval = 30000,
    pingInterval = 30000,
    pingTimeout = 5000,
    onStatusChange,
    onError
  } = config;

  const [connectionState, setConnectionState] = useState<ConnectionState>({
    status: 'disconnected',
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : false,
    lastConnected: null,
    reconnectAttempts: 0,
    error: null,
    latency: null
  });

  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const connectionStartTimeRef = useRef<number | null>(null);

  // Calculate reconnect delay with exponential backoff
  const getReconnectDelay = useCallback((attempt: number): number => {
    if (!exponentialBackoff) {
      return reconnectInterval;
    }

    const delay = Math.min(
      reconnectInterval * Math.pow(2, attempt),
      maxReconnectInterval
    );

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.3 * delay;
    return delay + jitter;
  }, [reconnectInterval, exponentialBackoff, maxReconnectInterval]);

  // Update connection status
  const updateStatus = useCallback((status: ConnectionStatus, error?: string) => {
    setConnectionState(prev => {
      const newState = {
        ...prev,
        status,
        error: error || null,
        lastConnected: status === 'connected' ? Date.now() : prev.lastConnected
      };

      // Reset reconnect attempts on successful connection
      if (status === 'connected') {
        newState.reconnectAttempts = 0;
      }

      return newState;
    });

    onStatusChange?.(status);
    if (error) {
      onError?.(error);
    }
  }, [onStatusChange, onError]);

  // Ping function to test connectivity
  const ping = useCallback(async (): Promise<number> => {
    const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), pingTimeout);

      const response = await fetch('/api/health', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Ping failed: ${response.status}`);
      }

      const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
      const latency = Math.round(endTime - startTime);
      
      setConnectionState(prev => ({
        ...prev,
        latency
      }));

      return latency;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Ping timeout');
      }
      
      throw error;
    }
  }, [pingTimeout]);

  // Start periodic ping monitoring
  const startPingMonitoring = useCallback(() => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
    }

    pingIntervalRef.current = setInterval(async () => {
      if (connectionState.status === 'connected') {
        try {
          await ping();
        } catch (error) {
          console.warn('[ConnectionState] Ping failed:', error);
          updateStatus('reconnecting', error instanceof Error ? error.message : 'Ping failed');
        }
      }
    }, pingInterval);
  }, [connectionState.status, ping, pingInterval, updateStatus]);

  // Stop ping monitoring
  const stopPingMonitoring = useCallback(() => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
  }, []);

  // Connect function
  const connect = useCallback(async (connectFn: () => Promise<void>): Promise<void> => {
    // Use functional state update to get current state
    let shouldConnect = false;
    setConnectionState(prev => {
      if (prev.status === 'connecting' || prev.status === 'connected') {
        shouldConnect = false;
        return prev;
      }
      shouldConnect = true;
      return prev;
    });

    if (!shouldConnect) {
      return;
    }

    updateStatus('connecting');
    connectionStartTimeRef.current = Date.now();

    try {
      await connectFn();
      updateStatus('connected');
      startPingMonitoring();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';
      updateStatus('failed', errorMessage);
      throw error;
    }
  }, [updateStatus, startPingMonitoring]);

  // Disconnect function
  const disconnect = useCallback(async (disconnectFn?: () => Promise<void>): Promise<void> => {
    // Clear any pending reconnection
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    stopPingMonitoring();

    try {
      if (disconnectFn) {
        await disconnectFn();
      }
    } catch (error) {
      console.warn('[ConnectionState] Disconnect error:', error);
    } finally {
      updateStatus('disconnected');
      setConnectionState(prev => ({
        ...prev,
        reconnectAttempts: 0,
        latency: null
      }));
    }
  }, [stopPingMonitoring, updateStatus]);

  // Reconnect function with retry logic
  const reconnect = useCallback(async (connectFn: () => Promise<void>): Promise<void> => {
    if (connectionState.reconnectAttempts >= maxReconnectAttempts) {
      updateStatus('failed', `Max reconnection attempts (${maxReconnectAttempts}) exceeded`);
      return;
    }

    const attempt = connectionState.reconnectAttempts + 1;
    const delay = getReconnectDelay(attempt - 1);

    console.log(`[ConnectionState] Reconnection attempt ${attempt}/${maxReconnectAttempts} in ${Math.round(delay)}ms`);

    setConnectionState(prev => ({
      ...prev,
      reconnectAttempts: attempt
    }));

    updateStatus('reconnecting');

    reconnectTimeoutRef.current = setTimeout(async () => {
      try {
        await connectFn();
        updateStatus('connected');
        startPingMonitoring();
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Reconnection failed';
        console.error(`[ConnectionState] Reconnection attempt ${attempt} failed:`, errorMessage);
        
        if (attempt < maxReconnectAttempts) {
          // Schedule next reconnection attempt
          reconnect(connectFn);
        } else {
          updateStatus('failed', `All reconnection attempts failed. Last error: ${errorMessage}`);
        }
      }
    }, delay);
  }, [
    connectionState.reconnectAttempts,
    maxReconnectAttempts,
    getReconnectDelay,
    updateStatus,
    startPingMonitoring
  ]);

  // Force reconnection (resets attempt counter)
  const forceReconnect = useCallback(async (connectFn: () => Promise<void>): Promise<void> => {
    setConnectionState(prev => ({
      ...prev,
      reconnectAttempts: 0
    }));
    
    await reconnect(connectFn);
  }, [reconnect]);

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      console.log('[ConnectionState] Browser came online');
      setConnectionState(prev => ({
        ...prev,
        isOnline: true
      }));
    };

    const handleOffline = () => {
      console.log('[ConnectionState] Browser went offline');
      setConnectionState(prev => ({
        ...prev,
        isOnline: false
      }));
      updateStatus('offline');
      stopPingMonitoring();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, [updateStatus, stopPingMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      stopPingMonitoring();
    };
  }, [stopPingMonitoring]);

  return {
    connectionState,
    connect,
    disconnect,
    reconnect,
    forceReconnect,
    ping,
    isConnected: connectionState.status === 'connected',
    isConnecting: connectionState.status === 'connecting',
    isReconnecting: connectionState.status === 'reconnecting',
    isFailed: connectionState.status === 'failed',
    isOffline: connectionState.status === 'offline' || !connectionState.isOnline,
    canReconnect: connectionState.reconnectAttempts < maxReconnectAttempts && connectionState.isOnline
  };
}

export default useConnectionState;