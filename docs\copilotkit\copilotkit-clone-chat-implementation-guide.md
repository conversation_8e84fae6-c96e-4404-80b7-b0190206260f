# CopilotKit Clone Chat Implementation Guide

## Overview

This guide provides a comprehensive walkthrough for implementing CopilotKit UI integration for clone chat functionality, specifically focusing on the clone agent chat interface observed in the application. This implementation combines CopilotKit's chat components with Mastra agent backend integration via the AG-UI protocol.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Prerequisites](#prerequisites)
3. [Core Components](#core-components)
4. [Implementation Steps](#implementation-steps)
5. [CopilotKit Configuration](#copilotkit-configuration)
6. [Backend Integration](#backend-integration)
7. [Styling and UI](#styling-and-ui)
8. [Advanced Features](#advanced-features)
9. [Testing and Debugging](#testing-and-debugging)
10. [Troubleshooting](#troubleshooting)

## Architecture Overview

The clone chat implementation follows this architecture:

```
Frontend (React + CopilotKit)
├── CopilotKit Provider
├── CopilotChat Component
├── Custom Conversation Flow
└── Voice Integration (Optional)

Backend (Mastra + AG-UI Protocol)
├── Mastra Agent Runtime
├── AG-UI Event Streaming
├── Clone Agent Logic
└── Authentication/Authorization
```

### Key Technologies

- **Frontend**: React, CopilotKit (`@copilotkit/react-core`, `@copilotkit/react-ui`)
- **Backend**: Mastra agents with AG-UI protocol
- **Communication**: Server-Sent Events (SSE), WebSocket (optional)
- **Authentication**: Organization-based access control
- **Styling**: Tailwind CSS with custom clone branding

## Prerequisites

### Dependencies

Install the required CopilotKit packages:

```bash
npm install @copilotkit/react-core @copilotkit/react-ui
# or
pnpm add @copilotkit/react-core @copilotkit/react-ui
```

### Backend Requirements

- Mastra agent runtime configured with AG-UI protocol
- Clone agent definitions and configurations
- Authentication system with organization/tenant support
- API endpoints for clone chat functionality

## Core Components

### 1. CopilotKit Provider Setup

The foundation of any CopilotKit integration is the provider configuration:

```tsx
import { CopilotKit } from '@copilotkit/react-core';

function App() {
  return (
    <CopilotKit runtimeUrl="/api/copilotkit">
      {/* Your app components */}
    </CopilotKit>
  );
}
```

### 2. Enhanced Conversation Flow Component

This is the main component that handles the clone chat interface:

```tsx
import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core';

interface CopilotKitConversationFlowProps {
  messages: ChatMessage[];
  isVoiceActive: boolean;
  isVoiceConnecting?: boolean;
  onToggleVoice?: () => void;
  onSendMessage?: (message: string) => void;
  className?: string;
  agentName?: string;
  sessionId?: string;
}

export const CopilotKitConversationFlow: React.FC<CopilotKitConversationFlowProps> = ({
  messages,
  isVoiceActive,
  isVoiceConnecting = false,
  onToggleVoice,
  onSendMessage,
  className = '',
  agentName = 'AI Assistant',
  sessionId
}) => {
  const [showCopilotChat, setShowCopilotChat] = React.useState(true);

  // Make session context available to CopilotKit
  useCopilotReadable({
    description: "Current clone chat session information",
    value: {
      sessionId,
      agentName,
      isVoiceActive,
      messageCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content || null
    }
  });

  // Register CopilotKit action for sending messages
  const sendMessageHandler = useCallback(async ({ message }: { message: string }) => {
    if (onSendMessage) {
      onSendMessage(message);
    }
    return `Message sent: ${message}`;
  }, [onSendMessage]);

  useCopilotAction({
    name: "sendMessage",
    description: "Send a message in the clone chat conversation",
    parameters: [
      {
        name: "message",
        type: "string",
        description: "The message content to send",
        required: true
      }
    ],
    handler: sendMessageHandler
  }, [sendMessageHandler]);

  return (
    <div className={`bg-white border-r border-gray-200 flex flex-col h-full ${className}`}>
      {/* Header with clone branding */}
      <div className="p-4 border-b border-gray-200 bg-purple-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">
                {agentName.charAt(0)}
              </span>
            </div>
            <h2 className="font-semibold text-gray-900">{agentName}</h2>
          </div>
          
          {/* Voice toggle button */}
          {onToggleVoice && (
            <button
              onClick={onToggleVoice}
              className={`p-2 rounded-full transition-colors ${
                isVoiceActive ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
              }`}
            >
              {/* Voice icon */}
            </button>
          )}
        </div>
      </div>

      {/* CopilotKit Chat Interface */}
      <div className="flex-1 overflow-y-auto">
        {showCopilotChat && (
          <CopilotChat
            instructions={`You are ${agentName}, a specialized AI clone. 
              Help the user with their specific needs related to your expertise.
              Current session: ${sessionId || 'demo'}`}
            labels={{
              title: agentName,
              initial: `Hi! I'm ${agentName}. How can I help you today?`,
            }}
          />
        )}
      </div>
    </div>
  );
};
```

### 3. Main Interface Component

The enhanced interface component that wraps everything together:

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotKitConversationFlow } from './CopilotKitConversationFlow';

interface RepRoomInterfaceEnhancedProps {
  agentName?: string;
  sessionId?: string;
  copilotKitRuntimeUrl?: string;
}

export const RepRoomInterfaceEnhanced: React.FC<RepRoomInterfaceEnhancedProps> = ({
  agentName = 'AI Assistant',
  sessionId = 'demo-session',
  copilotKitRuntimeUrl = '/api/copilotkit'
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isVoiceActive, setIsVoiceActive] = useState(false);

  const handleSendMessage = useCallback((content: string) => {
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      sender: 'You',
      content,
      timestamp: new Date(),
      type: 'human',
      isTyping: false
    };

    setMessages(prev => [...prev, newMessage]);

    // Here you would integrate with your backend
    // to send the message to the Mastra agent
  }, []);

  const handleToggleVoice = useCallback(() => {
    setIsVoiceActive(prev => !prev);
    // Implement voice toggle logic
  }, []);

  return (
    <CopilotKit runtimeUrl={copilotKitRuntimeUrl}>
      <div className="h-screen bg-gray-100 flex flex-col">
        <CopilotKitConversationFlow
          messages={messages}
          isVoiceActive={isVoiceActive}
          onToggleVoice={handleToggleVoice}
          onSendMessage={handleSendMessage}
          agentName={agentName}
          sessionId={sessionId}
        />
      </div>
    </CopilotKit>
  );
};
```

## Implementation Steps

### Step 1: Set Up CopilotKit Provider

1. **Install Dependencies**:
   ```bash
   npm install @copilotkit/react-core @copilotkit/react-ui
   ```

2. **Configure the Provider**:
   ```tsx
   import { CopilotKit } from '@copilotkit/react-core';
   
   function App() {
     return (
       <CopilotKit runtimeUrl="/api/copilotkit">
         <YourCloneChatComponent />
       </CopilotKit>
     );
   }
   ```

### Step 2: Create the Chat Interface

1. **Basic Chat Component**:
   ```tsx
   import { CopilotChat } from '@copilotkit/react-ui';
   
   function CloneChatInterface({ cloneName, cloneId }) {
     return (
       <div className="h-full">
         <CopilotChat
           instructions={`You are ${cloneName}, a specialized AI assistant.`}
           labels={{
             title: cloneName,
             initial: `Hi! I'm ${cloneName}. What would you like to know?`,
           }}
         />
       </div>
     );
   }
   ```

### Step 3: Add Context and Actions

1. **Provide Context to CopilotKit**:
   ```tsx
   import { useCopilotReadable } from '@copilotkit/react-core';
   
   useCopilotReadable({
     description: "Clone chat session context",
     value: {
       cloneId,
       cloneName,
       sessionId,
       userContext: currentUser
     }
   });
   ```

2. **Register Custom Actions**:
   ```tsx
   import { useCopilotAction } from '@copilotkit/react-core';
   
   useCopilotAction({
     name: "sendToClone",
     description: "Send a message to the clone agent",
     parameters: [
       {
         name: "message",
         type: "string",
         description: "Message to send to the clone",
         required: true
       }
     ],
     handler: async ({ message }) => {
       // Send to your backend/Mastra agent
       const response = await sendToCloneAgent(cloneId, message);
       return response;
     }
   });
   ```

### Step 4: Integrate with Backend

1. **API Endpoint for CopilotKit**:
   ```typescript
   // /api/copilotkit/route.ts
   import { CopilotRuntime, OpenAIAdapter } from '@copilotkit/runtime';
   
   export async function POST(req: Request) {
     const copilotKit = new CopilotRuntime();
     
     return copilotKit.response(req, new OpenAIAdapter({
       // Your OpenAI or custom LLM configuration
     }));
   }
   ```

2. **Clone Agent Integration**:
   ```typescript
   // Integrate with Mastra agent
   async function sendToCloneAgent(cloneId: string, message: string) {
     const response = await fetch(`/api/clones/${cloneId}/chat`, {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify({ message })
     });
     
     return response.json();
   }
   ```

## CopilotKit Configuration

### Runtime Configuration

```typescript
// copilotkit.config.ts
export const copilotKitConfig = {
  runtimeUrl: process.env.COPILOTKIT_RUNTIME_URL || '/api/copilotkit',
  publicApiKey: process.env.COPILOTKIT_PUBLIC_API_KEY,
  // Additional configuration options
};
```

### Chat Customization

```tsx
<CopilotChat
  instructions={`You are ${cloneName}, a specialized AI clone with expertise in ${cloneSpecialization}.
    Respond in character and provide helpful, accurate information.
    Current user context: ${userContext}
    Session ID: ${sessionId}`}
  labels={{
    title: cloneName,
    initial: `Hi! I'm ${cloneName}. ${cloneGreeting}`,
    placeholder: "Ask me anything...",
    submitButton: "Send"
  }}
  className="clone-chat-interface"
/>
```

## Backend Integration

### AG-UI Protocol Integration

The backend uses the AG-UI protocol for real-time communication:

```typescript
// AG-UI Event Types
interface AGUIEvent {
  type: 'agent_message_start' | 'agent_message_content' | 'agent_message_end';
  data: {
    id: string;
    content?: string;
    delta?: string;
    role: 'user' | 'assistant';
  };
}

// Event streaming
function streamAGUIEvents(cloneId: string, message: string) {
  const eventSource = new EventSource(`/api/clones/${cloneId}/stream`);
  
  eventSource.onmessage = (event) => {
    const agUIEvent: AGUIEvent = JSON.parse(event.data);
    handleAGUIEvent(agUIEvent);
  };
}
```

### Mastra Agent Configuration

```typescript
// mastra.config.ts
export const mastraConfig = {
  agents: {
    [cloneId]: {
      name: cloneName,
      instructions: cloneInstructions,
      tools: cloneTools,
      model: 'gpt-4',
      // Additional agent configuration
    }
  }
};
```

## Styling and UI

### Clone-Specific Branding

```css
/* Clone chat styling */
.clone-chat-interface {
  --clone-primary-color: #8b5cf6;
  --clone-secondary-color: #f3f4f6;
  --clone-accent-color: #10b981;
}

.clone-header {
  background: linear-gradient(135deg, var(--clone-primary-color), #a855f7);
  color: white;
  padding: 1rem;
  border-radius: 0.5rem 0.5rem 0 0;
}

.clone-avatar {
  width: 2rem;
  height: 2rem;
  background: var(--clone-primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}
```

### Responsive Design

```tsx
// Mobile-responsive chat interface
const CloneChatMobile = () => {
  return (
    <div className="h-screen flex flex-col">
      {/* Mobile header */}
      <div className="bg-purple-600 text-white p-4 flex items-center space-x-3">
        <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
          <span className="text-sm font-bold">{cloneName.charAt(0)}</span>
        </div>
        <h1 className="font-semibold">{cloneName}</h1>
      </div>
      
      {/* Chat area */}
      <div className="flex-1 overflow-hidden">
        <CopilotChat
          instructions={cloneInstructions}
          labels={{
            title: cloneName,
            initial: cloneGreeting
          }}
        />
      </div>
    </div>
  );
};
```

## Advanced Features

### Voice Integration

```tsx
// Voice-enabled clone chat
const VoiceEnabledCloneChat = () => {
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  
  const handleVoiceToggle = useCallback(async () => {
    if (isVoiceActive) {
      await voiceControls.disconnect();
    } else {
      await voiceControls.connect();
    }
    setIsVoiceActive(!isVoiceActive);
  }, [isVoiceActive]);

  return (
    <div className="clone-chat-with-voice">
      {/* Voice controls */}
      <div className="voice-controls">
        <button
          onClick={handleVoiceToggle}
          className={`voice-toggle ${isVoiceActive ? 'active' : ''}`}
        >
          {isVoiceActive ? <MicIcon /> : <MicOffIcon />}
        </button>
      </div>
      
      {/* Chat interface */}
      <CopilotChat
        instructions={`${cloneInstructions} Voice mode is ${isVoiceActive ? 'enabled' : 'disabled'}.`}
        // ... other props
      />
    </div>
  );
};
```

### Real-time Streaming

```tsx
// Real-time message streaming
const StreamingCloneChat = () => {
  const [streamingMessage, setStreamingMessage] = useState('');
  
  useEffect(() => {
    const eventSource = new EventSource(`/api/clones/${cloneId}/stream`);
    
    eventSource.addEventListener('message_start', (event) => {
      setStreamingMessage('');
    });
    
    eventSource.addEventListener('message_content', (event) => {
      const { delta } = JSON.parse(event.data);
      setStreamingMessage(prev => prev + delta);
    });
    
    eventSource.addEventListener('message_end', (event) => {
      // Message complete
      setStreamingMessage('');
    });
    
    return () => eventSource.close();
  }, [cloneId]);

  return (
    <div className="streaming-chat">
      {/* Show streaming indicator */}
      {streamingMessage && (
        <div className="streaming-message">
          <div className="typing-indicator">
            <span>{cloneName} is typing...</span>
          </div>
          <div className="streaming-content">
            {streamingMessage}
            <span className="cursor">|</span>
          </div>
        </div>
      )}
      
      <CopilotChat
        // ... chat configuration
      />
    </div>
  );
};
```

## Testing and Debugging

### Debug Configuration

```tsx
// Enable debug mode for development
const DebugCloneChat = () => {
  const [debugMode, setDebugMode] = useState(process.env.NODE_ENV === 'development');
  
  return (
    <div className="debug-clone-chat">
      {debugMode && (
        <div className="debug-panel">
          <h3>Debug Information</h3>
          <pre>{JSON.stringify({ cloneId, sessionId, messageCount }, null, 2)}</pre>
        </div>
      )}
      
      <CopilotChat
        // ... chat configuration
      />
    </div>
  );
};
```

### Testing Utilities

```typescript
// Test utilities for clone chat
export const cloneChatTestUtils = {
  mockCloneResponse: (message: string) => ({
    id: `test-${Date.now()}`,
    content: `Mock response to: ${message}`,
    role: 'assistant' as const,
    timestamp: Date.now()
  }),
  
  simulateTyping: (callback: (delta: string) => void, message: string) => {
    const words = message.split(' ');
    words.forEach((word, index) => {
      setTimeout(() => {
        callback(word + (index < words.length - 1 ? ' ' : ''));
      }, index * 100);
    });
  }
};
```

## Troubleshooting

### Common Issues

1. **CopilotKit Not Connecting**:
   ```typescript
   // Check runtime URL configuration
   const runtimeUrl = process.env.COPILOTKIT_RUNTIME_URL || '/api/copilotkit';
   console.log('CopilotKit Runtime URL:', runtimeUrl);
   
   // Verify API endpoint is accessible
   fetch(runtimeUrl)
     .then(response => console.log('Runtime accessible:', response.ok))
     .catch(error => console.error('Runtime error:', error));
   ```

2. **Messages Not Appearing**:
   ```typescript
   // Debug message flow
   useCopilotAction({
     name: "debugMessage",
     description: "Debug message sending",
     parameters: [{ name: "message", type: "string", required: true }],
     handler: async ({ message }) => {
       console.log('Message received by CopilotKit:', message);
       // Add your debugging logic here
       return `Debug: ${message}`;
     }
   });
   ```

3. **Authentication Issues**:
   ```typescript
   // Check authentication context
   const authContext = {
     userId: currentUser?.id,
     organizationId: currentOrganization?.id,
     permissions: userPermissions
   };
   
   useCopilotReadable({
     description: "User authentication context",
     value: authContext
   });
   ```

### Performance Optimization

```typescript
// Optimize re-renders
const MemoizedCloneChat = React.memo(({ cloneId, sessionId }) => {
  const memoizedInstructions = useMemo(() => 
    `You are ${cloneName}. Session: ${sessionId}`,
    [cloneName, sessionId]
  );
  
  const stableHandler = useCallback(async ({ message }) => {
    return await sendToClone(cloneId, message);
  }, [cloneId]);
  
  return (
    <CopilotChat
      instructions={memoizedInstructions}
      // ... other props
    />
  );
});
```

## Conclusion

This guide provides a comprehensive foundation for implementing CopilotKit UI integration for clone chat functionality. The key aspects include:

1. **Proper CopilotKit setup** with provider configuration
2. **Custom conversation flow components** that handle clone-specific logic
3. **Backend integration** with Mastra agents via AG-UI protocol
4. **Real-time streaming** for responsive user experience
5. **Voice integration** for enhanced interaction
6. **Responsive design** for mobile and desktop
7. **Debug and testing utilities** for development

By following this guide, junior developers should be able to reproduce and customize the clone chat functionality observed in the application, creating their own CopilotKit-powered chat interfaces with backend agent integration.

### Next Steps

1. Implement the basic chat interface following the core components
2. Add backend integration with your Mastra agent setup
3. Customize styling and branding for your specific clones
4. Add advanced features like voice and real-time streaming
5. Test thoroughly and optimize for performance

For additional support, refer to the [CopilotKit documentation](https://docs.copilotkit.ai/) and the [Mastra documentation](https://mastra.ai/docs) for more detailed information about specific features and configurations.