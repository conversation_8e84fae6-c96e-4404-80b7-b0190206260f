import React, { useState, useMemo } from 'react';
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { TrendingUp, TrendingDown, Minus, Eye, BarChart3, Target, Globe, Users, DollarSign, Award, AlertTriangle } from 'lucide-react';

interface CompetitorData {
  id: string;
  name: string;
  domain: string;
  description: string;
  logo?: string;
  metrics: {
    traffic: number;
    domainAuthority: number;
    marketShare: number;
    socialFollowing: number;
    contentScore: number;
    brandStrength: number;
  };
  strengths: string[];
  weaknesses: string[];
  threatLevel: 'low' | 'medium' | 'high';
  trend: 'up' | 'down' | 'stable';
  positioning: string;
  keyFeatures: string[];
  pricing?: {
    model: string;
    range: string;
  };
}

interface CompetitorAnalysisViewProps {
  competitors: CompetitorData[];
  onCompetitorSelect?: (competitor: CompetitorData) => void;
  className?: string;
}

const THREAT_COLORS = {
  low: '#10B981',    // Green
  medium: '#F59E0B', // Yellow
  high: '#EF4444'    // Red
};

const THREAT_LABELS = {
  low: 'Low Threat',
  medium: 'Medium Threat',
  high: 'High Threat'
};

export const CompetitorAnalysisView: React.FC<CompetitorAnalysisViewProps> = ({
  competitors,
  onCompetitorSelect,
  className = ''
}) => {
  const [selectedCompetitor, setSelectedCompetitor] = useState<CompetitorData | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'comparison' | 'radar'>('overview');
  const [sortBy, setSortBy] = useState<'traffic' | 'domainAuthority' | 'threatLevel'>('traffic');

  // Sort competitors
  const sortedCompetitors = useMemo(() => {
    return [...competitors].sort((a, b) => {
      switch (sortBy) {
        case 'traffic':
          return b.metrics.traffic - a.metrics.traffic;
        case 'domainAuthority':
          return b.metrics.domainAuthority - a.metrics.domainAuthority;
        case 'threatLevel': {
          const threatOrder = { high: 3, medium: 2, low: 1 };
          return threatOrder[b.threatLevel] - threatOrder[a.threatLevel];
        }
        default:
          return 0;
      }
    });
  }, [competitors, sortBy]);

  // Prepare radar chart data
  const radarData = useMemo(() => {
    if (!selectedCompetitor) return [];
    
    return [
      { metric: 'Traffic', value: selectedCompetitor.metrics.traffic / 1000000, fullMark: 10 },
      { metric: 'Domain Authority', value: selectedCompetitor.metrics.domainAuthority, fullMark: 100 },
      { metric: 'Market Share', value: selectedCompetitor.metrics.marketShare, fullMark: 100 },
      { metric: 'Social Following', value: selectedCompetitor.metrics.socialFollowing / 100000, fullMark: 10 },
      { metric: 'Content Score', value: selectedCompetitor.metrics.contentScore, fullMark: 100 },
      { metric: 'Brand Strength', value: selectedCompetitor.metrics.brandStrength, fullMark: 100 }
    ];
  }, [selectedCompetitor]);

  // Prepare comparison chart data
  const comparisonData = useMemo(() => {
    return sortedCompetitors.slice(0, 5).map(competitor => ({
      name: competitor.name.length > 10 ? `${competitor.name.slice(0, 10)}...` : competitor.name,
      traffic: competitor.metrics.traffic / 1000000,
      domainAuthority: competitor.metrics.domainAuthority,
      marketShare: competitor.metrics.marketShare,
      fullName: competitor.name
    }));
  }, [sortedCompetitors]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const handleCompetitorClick = (competitor: CompetitorData) => {
    setSelectedCompetitor(competitor);
    onCompetitorSelect?.(competitor);
  };

  // Overview Mode
  const OverviewMode = () => (
    <div className="space-y-4">
      {/* Sort Controls */}
      <div className="flex items-center space-x-4 mb-4">
        <span className="text-sm text-gray-600">Sort by:</span>
        {[
          { key: 'traffic', label: 'Traffic' },
          { key: 'domainAuthority', label: 'Domain Authority' },
          { key: 'threatLevel', label: 'Threat Level' }
        ].map(option => (
          <button
            key={option.key}
            onClick={() => setSortBy(option.key as typeof sortBy)}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              sortBy === option.key
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>

      {/* Competitors Grid */}
      <div className="grid gap-4">
        {sortedCompetitors.map((competitor) => (
          <div
            key={competitor.id}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleCompetitorClick(competitor)}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                {competitor.logo ? (
                  <img src={competitor.logo} alt={competitor.name} className="w-8 h-8 rounded" />
                ) : (
                  <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                    <Globe className="w-4 h-4 text-gray-500" />
                  </div>
                )}
                <div>
                  <h4 className="font-semibold text-gray-900">{competitor.name}</h4>
                  <p className="text-sm text-gray-500">{competitor.domain}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {getTrendIcon(competitor.trend)}
                <span
                  className="px-2 py-1 rounded-full text-xs font-medium text-white"
                  style={{ backgroundColor: THREAT_COLORS[competitor.threatLevel] }}
                >
                  {THREAT_LABELS[competitor.threatLevel]}
                </span>
              </div>
            </div>

            <p className="text-sm text-gray-600 mb-3">{competitor.description}</p>

            {/* Metrics */}
            <div className="grid grid-cols-3 gap-4 mb-3">
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {(competitor.metrics.traffic / 1000000).toFixed(1)}M
                </div>
                <div className="text-xs text-gray-500">Monthly Traffic</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {competitor.metrics.domainAuthority}
                </div>
                <div className="text-xs text-gray-500">Domain Authority</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {competitor.metrics.marketShare}%
                </div>
                <div className="text-xs text-gray-500">Market Share</div>
              </div>
            </div>

            {/* Key Features */}
            <div className="flex flex-wrap gap-1">
              {competitor.keyFeatures.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                >
                  {feature}
                </span>
              ))}
              {competitor.keyFeatures.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  +{competitor.keyFeatures.length - 3} more
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Comparison Mode
  const ComparisonMode = () => (
    <div className="space-y-6">
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={comparisonData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip 
              formatter={(value, name) => [
                name === 'traffic' ? `${value}M` : value,
                name === 'traffic' ? 'Traffic (M)' : 
                name === 'domainAuthority' ? 'Domain Authority' : 'Market Share (%)'
              ]}
            />
            <Legend />
            <Bar yAxisId="left" dataKey="traffic" fill="#3B82F6" name="Traffic (M)" />
            <Bar yAxisId="right" dataKey="domainAuthority" fill="#10B981" name="Domain Authority" />
            <Bar yAxisId="right" dataKey="marketShare" fill="#F59E0B" name="Market Share (%)" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Detailed Comparison Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Competitor
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Traffic
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                DA
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Market Share
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Threat Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trend
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedCompetitors.slice(0, 10).map((competitor) => (
              <tr key={competitor.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      {competitor.logo ? (
                        <img src={competitor.logo} alt={competitor.name} className="h-8 w-8 rounded" />
                      ) : (
                        <div className="h-8 w-8 bg-gray-200 rounded flex items-center justify-center">
                          <Globe className="h-4 w-4 text-gray-500" />
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{competitor.name}</div>
                      <div className="text-sm text-gray-500">{competitor.domain}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {(competitor.metrics.traffic / 1000000).toFixed(1)}M
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {competitor.metrics.domainAuthority}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {competitor.metrics.marketShare}%
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white"
                    style={{ backgroundColor: THREAT_COLORS[competitor.threatLevel] }}
                  >
                    {THREAT_LABELS[competitor.threatLevel]}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getTrendIcon(competitor.trend)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Radar Mode
  const RadarMode = () => (
    <div className="space-y-6">
      {!selectedCompetitor ? (
        <div className="text-center py-8">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Select a competitor to view detailed radar analysis</p>
        </div>
      ) : (
        <>
          <div className="text-center">
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              {selectedCompetitor.name} - Performance Radar
            </h4>
            <p className="text-gray-600">{selectedCompetitor.positioning}</p>
          </div>

          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="metric" />
                <PolarRadiusAxis angle={90} domain={[0, 'dataMax']} />
                <Radar
                  name={selectedCompetitor.name}
                  dataKey="value"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
              </RadarChart>
            </ResponsiveContainer>
          </div>

          {/* Strengths and Weaknesses */}
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h5 className="font-semibold text-green-700 mb-3 flex items-center">
                <Award className="w-4 h-4 mr-2" />
                Strengths
              </h5>
              <ul className="space-y-2">
                {selectedCompetitor.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h5 className="font-semibold text-red-700 mb-3 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Weaknesses
              </h5>
              <ul className="space-y-2">
                {selectedCompetitor.weaknesses.map((weakness, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{weakness}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Pricing Info */}
          {selectedCompetitor.pricing && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="font-semibold text-gray-900 mb-2 flex items-center">
                <DollarSign className="w-4 h-4 mr-2" />
                Pricing Model
              </h5>
              <p className="text-sm text-gray-700">
                <span className="font-medium">{selectedCompetitor.pricing.model}:</span> {selectedCompetitor.pricing.range}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Competitor Analysis</h3>
          </div>
          
          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            {[
              { key: 'overview', label: 'Overview', icon: Eye },
              { key: 'comparison', label: 'Compare', icon: BarChart3 },
              { key: 'radar', label: 'Radar', icon: Target }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setViewMode(key as typeof viewMode)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors flex items-center space-x-1 ${
                  viewMode === key
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{competitors.length}</div>
            <div className="text-sm text-gray-600">Competitors</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {competitors.filter(c => c.threatLevel === 'high').length}
            </div>
            <div className="text-sm text-gray-600">High Threat</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {competitors.filter(c => c.trend === 'up').length}
            </div>
            <div className="text-sm text-gray-600">Growing</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(competitors.reduce((sum, c) => sum + c.metrics.domainAuthority, 0) / competitors.length)}
            </div>
            <div className="text-sm text-gray-600">Avg DA</div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {viewMode === 'overview' && <OverviewMode />}
        {viewMode === 'comparison' && <ComparisonMode />}
        {viewMode === 'radar' && <RadarMode />}
      </div>
    </div>
  );
};