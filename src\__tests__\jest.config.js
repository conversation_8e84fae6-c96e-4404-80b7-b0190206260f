module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/setup.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: '<rootDir>/../tsconfig.json',
    }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(nanoid|@copilotkit|@supabase)/)'
  ],
  testMatch: [
    '<rootDir>/**/*.test.(ts|tsx)'
  ],
  collectCoverageFrom: [
    '../components/**/*.{ts,tsx}',
    '../pages/**/*.{ts,tsx}',
    '../hooks/**/*.{ts,tsx}',
    '../utils/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  globals: {
    'ts-jest': {
      isolatedModules: true,
    },
  },
  testPathIgnorePatterns: [
    '/node_modules/',
    '/docs/copilotkit/'
  ],
};