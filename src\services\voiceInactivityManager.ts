/**
 * Voice Inactivity Manager
 * Handles 90-second inactivity timeout for voice sessions
 * Tracks user activity and automatically disconnects inactive sessions
 */

export interface VoiceActivity {
  lastSpeechTime: number;
  lastInteractionTime: number;
  isActive: boolean;
  sessionStartTime: number;
}

export interface InactivityTimeoutConfig {
  timeoutSeconds: number;
  warningSeconds: number;
  checkIntervalMs: number;
}

export class VoiceInactivityManager {
  private activity: VoiceActivity;
  private config: InactivityTimeoutConfig;
  private timeoutId: NodeJS.Timeout | null = null;
  private warningTimeoutId: NodeJS.Timeout | null = null;
  private checkIntervalId: NodeJS.Timeout | null = null;
  private onWarning?: () => void;
  private onTimeout?: () => void;
  private onActivityUpdate?: (activity: VoiceActivity) => void;

  constructor(
    config: Partial<InactivityTimeoutConfig> = {},
    callbacks: {
      onWarning?: () => void;
      onTimeout?: () => void;
      onActivityUpdate?: (activity: VoiceActivity) => void;
    } = {}
  ) {
    this.config = {
      timeoutSeconds: 90, // 90 seconds default
      warningSeconds: 75, // 15 seconds before timeout
      checkIntervalMs: 5000, // Check every 5 seconds
      ...config
    };

    this.activity = {
      lastSpeechTime: Date.now(),
      lastInteractionTime: Date.now(),
      isActive: true,
      sessionStartTime: Date.now()
    };

    this.onWarning = callbacks.onWarning;
    this.onTimeout = callbacks.onTimeout;
    this.onActivityUpdate = callbacks.onActivityUpdate;

    console.log('[VoiceInactivityManager] Initialized with config:', this.config);
    this.startMonitoring();
  }

  /**
   * Record user speech activity
   */
  recordSpeechActivity(): void {
    const now = Date.now();
    this.activity.lastSpeechTime = now;
    this.activity.lastInteractionTime = now;
    this.activity.isActive = true;

    console.log('[VoiceInactivityManager] Speech activity recorded at:', new Date(now).toISOString());
    this.resetTimeouts();
    this.onActivityUpdate?.(this.activity);
  }

  /**
   * Record user interaction activity (button clicks, etc.)
   */
  recordInteractionActivity(): void {
    const now = Date.now();
    this.activity.lastInteractionTime = now;
    this.activity.isActive = true;

    console.log('[VoiceInactivityManager] Interaction activity recorded at:', new Date(now).toISOString());
    this.resetTimeouts();
    this.onActivityUpdate?.(this.activity);
  }

  /**
   * Get current activity status
   */
  getActivity(): VoiceActivity {
    return { ...this.activity };
  }

  /**
   * Get time since last activity in seconds
   */
  getTimeSinceLastActivity(): number {
    return Math.floor((Date.now() - this.activity.lastInteractionTime) / 1000);
  }

  /**
   * Get remaining time before timeout in seconds
   */
  getRemainingTime(): number {
    const timeSinceActivity = this.getTimeSinceLastActivity();
    return Math.max(0, this.config.timeoutSeconds - timeSinceActivity);
  }

  /**
   * Check if session should timeout
   */
  private shouldTimeout(): boolean {
    const timeSinceActivity = this.getTimeSinceLastActivity();
    return timeSinceActivity >= this.config.timeoutSeconds;
  }

  /**
   * Check if warning should be shown
   */
  private shouldShowWarning(): boolean {
    const timeSinceActivity = this.getTimeSinceLastActivity();
    return timeSinceActivity >= this.config.warningSeconds;
  }

  /**
   * Start monitoring for inactivity
   */
  private startMonitoring(): void {
    console.log('[VoiceInactivityManager] Starting inactivity monitoring');
    
    this.checkIntervalId = setInterval(() => {
      this.checkInactivity();
    }, this.config.checkIntervalMs);
  }

  /**
   * Check for inactivity and trigger appropriate actions
   */
  private checkInactivity(): void {
    if (!this.activity.isActive) {
      return; // Session already marked as inactive
    }

    const timeSinceActivity = this.getTimeSinceLastActivity();
    const remainingTime = this.getRemainingTime();

    console.log('[VoiceInactivityManager] Checking inactivity:', {
      timeSinceActivity,
      remainingTime,
      timeoutThreshold: this.config.timeoutSeconds,
      warningThreshold: this.config.warningSeconds
    });

    // Check for timeout
    if (this.shouldTimeout()) {
      console.log('[VoiceInactivityManager] Inactivity timeout reached, triggering disconnect');
      this.triggerTimeout();
      return;
    }

    // Check for warning
    if (this.shouldShowWarning() && !this.warningTimeoutId) {
      console.log('[VoiceInactivityManager] Inactivity warning threshold reached');
      this.triggerWarning();
    }
  }

  /**
   * Trigger warning callback
   */
  private triggerWarning(): void {
    console.log('[VoiceInactivityManager] Triggering inactivity warning');
    this.onWarning?.();
    
    // Set a flag to prevent multiple warnings
    this.warningTimeoutId = setTimeout(() => {
      this.warningTimeoutId = null;
    }, (this.config.timeoutSeconds - this.config.warningSeconds) * 1000);
  }

  /**
   * Trigger timeout callback and mark session as inactive
   */
  private triggerTimeout(): void {
    console.log('[VoiceInactivityManager] Triggering inactivity timeout');
    this.activity.isActive = false;
    this.onTimeout?.();
    this.stopMonitoring();
  }

  /**
   * Reset timeout timers when activity is detected
   */
  private resetTimeouts(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    if (this.warningTimeoutId) {
      clearTimeout(this.warningTimeoutId);
      this.warningTimeoutId = null;
    }

    console.log('[VoiceInactivityManager] Timeouts reset due to activity');
  }

  /**
   * Stop monitoring
   */
  private stopMonitoring(): void {
    console.log('[VoiceInactivityManager] Stopping inactivity monitoring');
    
    if (this.checkIntervalId) {
      clearInterval(this.checkIntervalId);
      this.checkIntervalId = null;
    }

    this.resetTimeouts();
  }

  /**
   * Manually stop the inactivity manager
   */
  stop(): void {
    console.log('[VoiceInactivityManager] Manually stopping inactivity manager');
    this.activity.isActive = false;
    this.stopMonitoring();
  }

  /**
   * Resume monitoring (useful after reconnection)
   */
  resume(): void {
    console.log('[VoiceInactivityManager] Resuming inactivity monitoring');
    this.activity.isActive = true;
    this.activity.lastInteractionTime = Date.now();
    this.activity.lastSpeechTime = Date.now();
    
    if (!this.checkIntervalId) {
      this.startMonitoring();
    }
    
    this.onActivityUpdate?.(this.activity);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<InactivityTimeoutConfig>): void {
    console.log('[VoiceInactivityManager] Updating config:', newConfig);
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get session duration in seconds
   */
  getSessionDuration(): number {
    return Math.floor((Date.now() - this.activity.sessionStartTime) / 1000);
  }

  /**
   * Check if session is active
   */
  isSessionActive(): boolean {
    return this.activity.isActive;
  }
}

export default VoiceInactivityManager;