import React, { useState, useEffect, useRef } from 'react';
import { useChatErrorHandling } from '../../hooks/useChatErrorHandling';
import { useUnifiedVoice } from '../../contexts/rroom/UnifiedVoiceContext';
import { ConnectionStatusIndicator } from '../common/ConnectionStatusIndicator';

interface SessionChatProps {
  sessionId: string;
  config: {
    repRoom: {
      id: string;
      slug: string;
      title: string;
      settings: {
        voice: {
          enabled: boolean;
          provider: string;
          sttProvider: string;
          ttsProvider: string;
          voiceId: string;
        };
        appearance: {
          themeColor: string;
          backgroundColor: string;
          backgroundType: string;
        };
        behavior: {
          greeting_message: string;
          suggested_prompts: string[];
          voice_input_enabled: boolean;
          file_upload_enabled: boolean;
        };
      };
    };
    agent: {
      id: string;
      name: string;
      mastraApiBaseUrl: string;
      cloneId: string;
    };
  };
  onError?: (error: string) => void;
  className?: string;
}

export function SessionChat({ 
  sessionId, 
  config, 
  onError, 
  className 
}: SessionChatProps) {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Use hooks
  const chatErrorHandling = useChatErrorHandling({
    maxRetries: 3,
    onError: (error, messageId) => {
      console.error('[SessionChat] Message error:', error.message, messageId);
      if (onError) {
        onError(error.message);
      }
    }
  });

  const { state, controls, isVoiceActive } = useUnifiedVoice();

  // Handle connection errors
  useEffect(() => {
    if (state.connectionError && onError) {
      onError(state.connectionError);
    }
  }, [state.connectionError, onError]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [state.visibleMessages]);

  // Send message function
  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    try {
      await chatErrorHandling.sendMessageSafely(
        content,
        async (messageContent: string) => {
          // This would normally send to the voice context or API
          console.log('[SessionChat] Sending message:', messageContent);
          return {
            id: `msg-${Date.now()}`,
            sender: 'User',
            content: messageContent,
            timestamp: new Date(),
            type: 'human' as const
          };
        }
      );
      setInputValue('');
    } catch (error) {
      console.error('[SessionChat] Failed to send message:', error);
    }
  };

  // Handle input submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(inputValue);
  };

  // Handle Enter key
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputValue);
    }
  };

  // Handle suggested prompt click
  const handlePromptClick = (prompt: string) => {
    sendMessage(prompt);
  };

  // Get error stats
  const errorStats = chatErrorHandling.getErrorStats();

  return (
    <div data-testid="session-chat" className={className}>
      {/* Error Banner */}
      {errorStats.hasErrors && (
        <div data-testid="chat-error-banner" className="error-banner">
          <span>Failed to send message</span>
          {errorStats.totalFailedMessages > 0 && (
            <button 
              data-testid="retry-button"
              onClick={() => {
                // Retry the first failed message
                const firstFailedId = Array.from(chatErrorHandling.errorState.failedMessages)[0];
                if (firstFailedId) {
                  chatErrorHandling.retryMessage(firstFailedId, async () => {
                    console.log('Retrying message:', firstFailedId);
                  });
                }
              }}
            >
              Retry
            </button>
          )}
        </div>
      )}

      {/* Messages Container */}
      <div data-testid="chat-messages" role="log" className="messages-container">
        {/* Greeting Message */}
        <div className="greeting-message">
          {config.repRoom.settings.behavior.greeting_message}
        </div>

        {/* Suggested Prompts */}
        {config.repRoom.settings.behavior.suggested_prompts.length > 0 && (
          <div data-testid="suggested-prompts" className="suggested-prompts">
            {config.repRoom.settings.behavior.suggested_prompts.map((prompt, index) => (
              <button
                key={index}
                onClick={() => handlePromptClick(prompt)}
                className="suggested-prompt"
                tabIndex={-1}
              >
                {prompt}
              </button>
            ))}
          </div>
        )}

        {/* Messages */}
        {state.visibleMessages.map((message) => (
          <div key={message.id} className={`message ${message.role}`}>
            <div className="message-content">{message.content}</div>
          </div>
        ))}

        {/* Typing Indicator */}
        {state.isAgentSpeaking && (
          <div data-testid="typing-indicator" className="typing-indicator">
            Agent is responding...
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div data-testid="chat-input-area" className="input-area">
        <form onSubmit={handleSubmit} className="input-form">
          <input
            data-testid="chat-input"
            type="text"
            role="textbox"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="chat-input"
          />
          
          {/* Voice Input Button */}
          {config.repRoom.settings.behavior.voice_input_enabled && (
            <button
              data-testid="voice-input-button"
              type="button"
              className="voice-button"
              onClick={() => {
                // Voice input functionality would go here
                console.log('[SessionChat] Voice input clicked');
              }}
            >
              🎤
            </button>
          )}
          
          <button
            data-testid="send-button"
            type="submit"
            role="button"
            aria-label="Send message"
            className="send-button"
          >
            Send
          </button>
        </form>

        {/* Connection Status */}
        <ConnectionStatusIndicator
          status={state.connectionError ? 'failed' : 'connected'}
          isOnline={!state.connectionError}
        />
      </div>

      {/* Screen Reader Announcements */}
      <div role="status" aria-live="polite" className="sr-only">
        {state.visibleMessages.length > 0 && 
          `New message: ${state.visibleMessages[state.visibleMessages.length - 1]?.content}`
        }
      </div>
    </div>
  );
}

export default SessionChat;