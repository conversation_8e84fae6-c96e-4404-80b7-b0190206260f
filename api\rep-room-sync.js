// Rep Room WebSocket Sync Server
// This is a demo implementation for testing the real-time synchronization

import { WebSocketServer } from 'ws';

const clients = new Map();
const rooms = new Map();

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // For development, we'll return a simple response
  // In production, this would be handled by a proper WebSocket server
  res.status(200).json({ 
    message: 'WebSocket endpoint for Rep Room sync',
    note: 'This is a placeholder. In production, use a proper WebSocket server like Socket.io or native WebSocket server.'
  });
}

// Demo WebSocket server implementation (for reference)
export function createRepRoomSyncServer(port = 8080) {
  const wss = new WebSocketServer({ port });

  wss.on('connection', (ws, req) => {
    const url = new URL(req.url, `http://${req.headers.host}`);
    const sessionId = url.searchParams.get('sessionId') || 'default';
    
    console.log(`[RepRoomSync] Client connected to session: ${sessionId}`);
    
    // Add client to session
    if (!rooms.has(sessionId)) {
      rooms.set(sessionId, new Set());
    }
    rooms.get(sessionId).add(ws);
    clients.set(ws, { sessionId, lastPing: Date.now() });

    // Send welcome message
    ws.send(JSON.stringify({
      type: 'connection_established',
      payload: { sessionId, timestamp: Date.now() },
      timestamp: Date.now(),
      sessionId
    }));

    // Handle incoming messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log(`[RepRoomSync] Message received:`, message);

        // Update last ping time
        const clientInfo = clients.get(ws);
        if (clientInfo) {
          clientInfo.lastPing = Date.now();
        }

        // Handle different message types
        switch (message.type) {
          case 'ping':
            // Respond with pong
            ws.send(JSON.stringify({
              type: 'pong',
              payload: { timestamp: Date.now() },
              timestamp: Date.now(),
              sessionId: message.sessionId
            }));
            break;

          case 'pong':
            // Update ping time
            break;

          default:
            // Broadcast message to all clients in the same session
            broadcastToSession(sessionId, message, ws);
            break;
        }
      } catch (error) {
        console.error('[RepRoomSync] Error parsing message:', error);
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      console.log(`[RepRoomSync] Client disconnected from session: ${sessionId}`);
      
      // Remove client from session
      const room = rooms.get(sessionId);
      if (room) {
        room.delete(ws);
        if (room.size === 0) {
          rooms.delete(sessionId);
        }
      }
      clients.delete(ws);
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error('[RepRoomSync] WebSocket error:', error);
    });
  });

  // Broadcast message to all clients in a session except sender
  function broadcastToSession(sessionId, message, sender) {
    const room = rooms.get(sessionId);
    if (!room) return;

    const messageStr = JSON.stringify(message);
    room.forEach(client => {
      if (client !== sender && client.readyState === client.OPEN) {
        try {
          client.send(messageStr);
        } catch (error) {
          console.error('[RepRoomSync] Error sending message:', error);
        }
      }
    });
  }

  // Cleanup inactive connections
  setInterval(() => {
    const now = Date.now();
    const timeout = 60000; // 1 minute timeout

    clients.forEach((clientInfo, ws) => {
      if (now - clientInfo.lastPing > timeout) {
        console.log('[RepRoomSync] Closing inactive connection');
        ws.close();
      }
    });
  }, 30000); // Check every 30 seconds

  console.log(`[RepRoomSync] WebSocket server started on port ${port}`);
  return wss;
}