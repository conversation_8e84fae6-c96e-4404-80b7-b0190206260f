import React, { useState, useEffect } from 'react';
import { useLocalParticipant, useDisconnectButton, useRoomContext } from '@livekit/components-react';
import {
  Mi<PERSON>,
  MicOff,
  PhoneOff,
  Volume2,
  VolumeX,
  Settings,
  Wifi,
  WifiOff,
  AlertCircle,
  Radio
} from 'lucide-react';
import { Track } from 'livekit-client';

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';
export type VoiceActivityLevel = 'silent' | 'low' | 'medium' | 'high';

interface VoiceControlsProps {
  className?: string;
  onConnectionStatusChange?: (status: ConnectionStatus) => void;
  onVoiceActivityChange?: (level: VoiceActivityLevel) => void;
  showAdvancedControls?: boolean;
}

// Safe hook wrapper to handle missing LiveKit context
const useSafeLocalParticipant = () => {
  try {
    return useLocalParticipant();
  } catch (error) {
    console.warn('LiveKit Room context not available, using fallback values');
    return {
      isMicrophoneEnabled: false,
      localParticipant: null
    };
  }
};

const useSafeDisconnectButton = () => {
  try {
    return useDisconnectButton({});
  } catch (error) {
    console.warn('LiveKit Room context not available for disconnect button');
    return {
      buttonProps: {
        onClick: () => console.log('Disconnect clicked (no room context)')
      }
    };
  }
};

const useSafeRoomContext = () => {
  try {
    return useRoomContext();
  } catch (error) {
    console.warn('LiveKit Room context not available');
    return null;
  }
};

export const VoiceControls: React.FC<VoiceControlsProps> = ({
  className = '',
  onConnectionStatusChange,
  onVoiceActivityChange,
  showAdvancedControls = true
}) => {
  const { isMicrophoneEnabled, localParticipant } = useSafeLocalParticipant();
  const { buttonProps: disconnectProps } = useSafeDisconnectButton();
  const room = useSafeRoomContext();
  
  // State management
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('connecting');
  const [voiceActivity, setVoiceActivity] = useState<VoiceActivityLevel>('silent');
  const [speakerVolume, setSpeakerVolume] = useState(75);
  const [isSpeakerMuted, setIsSpeakerMuted] = useState(false);
  const [isPushToTalk, setIsPushToTalk] = useState(false);
  const [isPushToTalkActive, setIsPushToTalkActive] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [microphoneError, setMicrophoneError] = useState<string | null>(null);

  // Voice activity detection
  useEffect(() => {
    if (!localParticipant) return;

    const detectVoiceActivity = () => {
      // This would integrate with the audio analysis from WaveformVisualizer
      // For now, we'll simulate based on microphone state
      if (isMicrophoneEnabled) {
        const randomActivity = Math.random();
        let level: VoiceActivityLevel = 'silent';
        
        if (randomActivity > 0.8) level = 'high';
        else if (randomActivity > 0.6) level = 'medium';
        else if (randomActivity > 0.3) level = 'low';
        
        setVoiceActivity(level);
        onVoiceActivityChange?.(level);
      } else {
        setVoiceActivity('silent');
        onVoiceActivityChange?.('silent');
      }
    };

    const interval = setInterval(detectVoiceActivity, 100);
    return () => clearInterval(interval);
  }, [isMicrophoneEnabled, localParticipant, onVoiceActivityChange]);

  // Connection status monitoring
  useEffect(() => {
    if (!room) {
      setConnectionStatus('disconnected');
      return;
    }

    const updateConnectionStatus = () => {
      switch (room.state) {
        case 'connecting':
          setConnectionStatus('connecting');
          break;
        case 'connected':
          setConnectionStatus('connected');
          break;
        case 'disconnected':
          setConnectionStatus('disconnected');
          break;
        default:
          setConnectionStatus('error');
      }
    };

    updateConnectionStatus();
    
    room.on('connectionStateChanged', updateConnectionStatus);
    return () => {
      room.off('connectionStateChanged', updateConnectionStatus);
    };
  }, [room]);

  // Notify parent of connection status changes
  useEffect(() => {
    onConnectionStatusChange?.(connectionStatus);
  }, [connectionStatus, onConnectionStatusChange]);

  // Push-to-talk keyboard handling
  useEffect(() => {
    if (!isPushToTalk) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !e.repeat) {
        e.preventDefault();
        setIsPushToTalkActive(true);
        if (!isMicrophoneEnabled) {
          toggleMicrophone();
        }
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault();
        setIsPushToTalkActive(false);
        if (isMicrophoneEnabled) {
          toggleMicrophone();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [isPushToTalk, isMicrophoneEnabled]);

  const toggleMicrophone = async () => {
    console.log('🎤 Enhanced microphone toggle clicked, current state:', isMicrophoneEnabled);
    setMicrophoneError(null);
    
    if (!localParticipant) {
      console.warn('🎤 No local participant available (LiveKit room not connected)');
      setMicrophoneError('Voice chat not available - room not connected');
      return;
    }
    
    try {
      if (!isMicrophoneEnabled) {
        console.log('🎤 Enabling microphone...');
        await localParticipant.enableCameraAndMicrophone();
        console.log('🎤 Microphone enabled successfully');
      } else {
        console.log('🎤 Disabling microphone...');
        await localParticipant.setMicrophoneEnabled(false);
        console.log('🎤 Microphone disabled successfully');
      }
    } catch (error) {
      console.error('🎤 Error toggling microphone:', error);
      setMicrophoneError(error instanceof Error ? error.message : 'Unknown error');
      
      // Fallback: try direct track creation
      try {
        console.log('🎤 Trying fallback microphone activation...');
        const tracks = await localParticipant.createTracks({
          audio: true,
          video: false
        });
        
        for (const track of tracks) {
          await localParticipant.publishTrack(track);
        }
        console.log('🎤 Fallback microphone activation successful');
        setMicrophoneError(null);
      } catch (fallbackError) {
        console.error('🎤 Fallback microphone activation failed:', fallbackError);
        setMicrophoneError('Failed to enable microphone. Please check permissions.');
      }
    }
  };

  const toggleSpeaker = () => {
    setIsSpeakerMuted(!isSpeakerMuted);
    // In a real implementation, this would control the audio output
    console.log('🔊 Speaker toggled:', !isSpeakerMuted ? 'muted' : 'unmuted');
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const volume = parseInt(e.target.value);
    setSpeakerVolume(volume);
    // In a real implementation, this would control the actual audio volume
    console.log('🔊 Volume changed to:', volume);
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connecting':
        return <Wifi className="h-4 w-4 animate-pulse text-yellow-500" />;
      case 'connected':
        return <Wifi className="h-4 w-4 text-green-500" />;
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-gray-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <WifiOff className="h-4 w-4 text-gray-500" />;
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        return 'Connected';
      case 'disconnected':
        return 'Disconnected';
      case 'error':
        return 'Connection Error';
      default:
        return 'Unknown';
    }
  };

  const getVoiceActivityIndicator = () => {
    const baseClasses = "w-2 h-2 rounded-full transition-all duration-150";
    
    switch (voiceActivity) {
      case 'high':
        return `${baseClasses} bg-red-500 animate-pulse shadow-lg shadow-red-500/50`;
      case 'medium':
        return `${baseClasses} bg-yellow-500 animate-pulse shadow-md shadow-yellow-500/50`;
      case 'low':
        return `${baseClasses} bg-green-500 shadow-sm shadow-green-500/50`;
      case 'silent':
      default:
        return `${baseClasses} bg-gray-400`;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 p-4 ${className}`}>
      {/* Connection Status */}
      <div className="flex items-center justify-between mb-4 pb-3 border-b border-gray-100">
        <div className="flex items-center space-x-2">
          {getConnectionStatusIcon()}
          <span className="text-sm font-medium text-gray-700">
            {getConnectionStatusText()}
          </span>
        </div>
        
        {showAdvancedControls && (
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
            title="Voice settings"
          >
            <Settings className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Main Controls */}
      <div className="flex items-center justify-center space-x-4 mb-4">
        {/* Microphone Toggle */}
        <div className="flex flex-col items-center">
          <button
            onClick={toggleMicrophone}
            disabled={isPushToTalk && !isPushToTalkActive}
            className={`relative p-4 rounded-full transition-all duration-200 ${
              isMicrophoneEnabled
                ? 'bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/30'
                : 'bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/30'
            } ${isPushToTalk && !isPushToTalkActive ? 'opacity-50' : ''}`}
            title={
              isPushToTalk 
                ? 'Hold SPACE to talk' 
                : (isMicrophoneEnabled ? 'Mute microphone' : 'Unmute microphone')
            }
          >
            {isMicrophoneEnabled ? (
              <Mic className="h-6 w-6" />
            ) : (
              <MicOff className="h-6 w-6" />
            )}
            
            {/* Voice Activity Indicator */}
            <div className={`absolute -top-1 -right-1 ${getVoiceActivityIndicator()}`} />
          </button>
          
          <div className="text-xs text-gray-500 text-center mt-2">
            {isPushToTalk ? (
              isPushToTalkActive ? 'Speaking' : 'Hold SPACE'
            ) : (
              isMicrophoneEnabled ? 'Microphone On' : 'Microphone Off'
            )}
          </div>
        </div>

        {/* Speaker Toggle */}
        {showAdvancedControls && (
          <div className="flex flex-col items-center">
            <button
              onClick={toggleSpeaker}
              className={`p-3 rounded-full transition-colors ${
                isSpeakerMuted
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
              title={isSpeakerMuted ? 'Unmute speaker' : 'Mute speaker'}
            >
              {isSpeakerMuted ? (
                <VolumeX className="h-5 w-5" />
              ) : (
                <Volume2 className="h-5 w-5" />
              )}
            </button>
            
            <div className="text-xs text-gray-500 text-center mt-2">
              {isSpeakerMuted ? 'Speaker Off' : 'Speaker On'}
            </div>
          </div>
        )}

        {/* Disconnect Button */}
        <div className="flex flex-col items-center">
          <button
            {...disconnectProps}
            className="p-3 rounded-full bg-gray-700 hover:bg-gray-600 text-white transition-colors"
            title="End session"
          >
            <PhoneOff className="h-5 w-5" />
          </button>
          
          <div className="text-xs text-gray-500 text-center mt-2">
            End Session
          </div>
        </div>
      </div>

      {/* Error Display */}
      {microphoneError && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700">{microphoneError}</span>
          </div>
        </div>
      )}

      {/* Advanced Settings */}
      {showSettings && showAdvancedControls && (
        <div className="border-t border-gray-100 pt-4 space-y-4">
          {/* Volume Control */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Speaker Volume
            </label>
            <div className="flex items-center space-x-3">
              <VolumeX className="h-4 w-4 text-gray-400" />
              <input
                type="range"
                min="0"
                max="100"
                value={speakerVolume}
                onChange={handleVolumeChange}
                className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                disabled={isSpeakerMuted}
              />
              <Volume2 className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600 w-8">{speakerVolume}</span>
            </div>
          </div>

          {/* Push-to-Talk Toggle */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Radio className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Push-to-Talk</span>
            </div>
            <button
              onClick={() => setIsPushToTalk(!isPushToTalk)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isPushToTalk ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isPushToTalk ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {isPushToTalk && (
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
              Hold SPACE key to activate microphone
            </div>
          )}
        </div>
      )}
    </div>
  );
};