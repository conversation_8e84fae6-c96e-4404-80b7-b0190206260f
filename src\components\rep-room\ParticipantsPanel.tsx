import React, { useState } from 'react';
import { Users, Settings, UserPlus, Volume2, Mic } from 'lucide-react';
import { Agent, Human } from '../../types/rep-room';
import { AgentCard } from './AgentCard';
import { HumanCard } from './HumanCard';
import { VoiceControls, ConnectionStatus, VoiceActivityLevel } from './VoiceControls';
// import { AudioVisualizer } from './AudioVisualizer'; // Temporarily disabled due to LiveKit context issues

interface ParticipantsPanelProps {
  agents: Agent[];
  humans: Human[];
  onAgentSelect?: (agent: Agent) => void;
  onInviteUser?: () => void;
  onSettings?: () => void;
  className?: string;
  showVoiceControls?: boolean;
  showAudioVisualizer?: boolean;
  onConnectionStatusChange?: (status: ConnectionStatus) => void;
  onVoiceActivityChange?: (level: VoiceActivityLevel) => void;
}

export const ParticipantsPanel: React.FC<ParticipantsPanelProps> = ({
  agents,
  humans,
  onAgentSelect,
  onInviteUser,
  onSettings,
  className = '',
  showVoiceControls = true,
  showAudioVisualizer = true,
  onConnectionStatusChange,
  onVoiceActivityChange
}) => {
  const [showVoicePanel, setShowVoicePanel] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('connecting');
  const [voiceActivity, setVoiceActivity] = useState<VoiceActivityLevel>('silent');
  
  const mainAgent = agents.find(agent => agent.type === 'main');
  const specialistAgents = agents.filter(agent => agent.type === 'specialist');

  const handleConnectionStatusChange = (status: ConnectionStatus) => {
    setConnectionStatus(status);
    onConnectionStatusChange?.(status);
  };

  const handleVoiceActivityChange = (level: VoiceActivityLevel) => {
    setVoiceActivity(level);
    onVoiceActivityChange?.(level);
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-500';
      case 'connecting':
        return 'text-yellow-500';
      case 'error':
        return 'text-red-500';
      case 'disconnected':
      default:
        return 'text-gray-500';
    }
  };

  const getVoiceActivityColor = () => {
    switch (voiceActivity) {
      case 'high':
        return 'text-red-500';
      case 'medium':
        return 'text-yellow-500';
      case 'low':
        return 'text-green-500';
      case 'silent':
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className={`bg-gray-50 border-l border-gray-200 flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5 text-gray-600" />
            <h2 className="font-semibold text-gray-900">
              Participants ({agents.length + humans.length})
            </h2>
          </div>
          
          <div className="flex items-center space-x-2">
            {showVoiceControls && (
              <button
                onClick={() => setShowVoicePanel(!showVoicePanel)}
                className={`p-1.5 rounded-md transition-colors ${
                  showVoicePanel
                    ? 'text-blue-600 bg-blue-50 hover:bg-blue-100'
                    : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                }`}
                title="Toggle voice controls"
              >
                <Volume2 className="w-4 h-4" />
              </button>
            )}
            
            {onInviteUser && (
              <button
                onClick={onInviteUser}
                className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                title="Invite user"
              >
                <UserPlus className="w-4 h-4" />
              </button>
            )}
            
            {onSettings && (
              <button
                onClick={onSettings}
                className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                title="Settings"
              >
                <Settings className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Voice Status Indicators */}
        {showVoiceControls && (
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${getConnectionStatusColor().replace('text-', 'bg-')}`} />
                <span className={`${getConnectionStatusColor()}`}>
                  {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Mic className={`w-3 h-3 ${getVoiceActivityColor()}`} />
                <span className={`${getVoiceActivityColor()}`}>
                  {voiceActivity === 'silent' ? 'Quiet' : 'Active'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Voice Controls Section */}
        {showVoiceControls && showVoicePanel && (
          <div className="p-4 border-b border-gray-100">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Voice Controls</h3>
            <VoiceControls
              onConnectionStatusChange={handleConnectionStatusChange}
              onVoiceActivityChange={handleVoiceActivityChange}
              showAdvancedControls={true}
              className="mb-4"
            />
            
            {/* Audio Visualizer */}
            {showAudioVisualizer && (
              <div className="mt-4">
                {/* AudioVisualizer temporarily disabled due to LiveKit context issues */}
                <div className="w-full h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-sm text-gray-500">Audio Visualizer (Coming Soon)</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Main Agent Section */}
        {mainAgent && (
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Main Agent</h3>
            <AgentCard
              agent={mainAgent}
              isSelected={false}
              onClick={() => onAgentSelect?.(mainAgent)}
              className="mb-2"
            />
          </div>
        )}

        {/* Specialist Agents Section */}
        {specialistAgents.length > 0 && (
          <div className="px-4 pb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Specialist Agents ({specialistAgents.length})
            </h3>
            <div className="space-y-2">
              {specialistAgents.map((agent) => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  isSelected={false}
                  onClick={() => onAgentSelect?.(agent)}
                  size="compact"
                />
              ))}
            </div>
          </div>
        )}

        {/* Humans Section */}
        {humans.length > 0 && (
          <div className="px-4 pb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Humans ({humans.length})
            </h3>
            <div className="space-y-2">
              {humans.map((human) => (
                <HumanCard
                  key={human.id}
                  human={human}
                />
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {agents.length === 0 && humans.length === 0 && (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500">
            <Users className="w-8 h-8 mb-2" />
            <p className="text-sm">No participants yet</p>
            {onInviteUser && (
              <button
                onClick={onInviteUser}
                className="mt-2 text-sm text-blue-600 hover:text-blue-700"
              >
                Invite someone to join
              </button>
            )}
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-3 border-t border-gray-200 bg-white">
        <div className="flex justify-between text-xs text-gray-500">
          <span>{agents.length} agents</span>
          <span>{humans.length} humans</span>
        </div>
        
        {showVoiceControls && (
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span className={`${getConnectionStatusColor()}`}>
              {connectionStatus}
            </span>
            <span className={`${getVoiceActivityColor()}`}>
              Voice: {voiceActivity}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};