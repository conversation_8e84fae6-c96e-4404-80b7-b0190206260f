/* Rep Room V3 Responsive Styles */

/* Base responsive utilities */
.rep-room-v3 {
  /* Touch optimization */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  .rep-room-v3 {
    /* Ensure full viewport usage */
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  /* Touch-friendly button sizes */
  .rep-room-v3 button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved text readability on mobile */
  .rep-room-v3 {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Mobile tab navigation enhancements */
  .mobile-tab-navigation {
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: blur(8px);
    background-color: rgba(255, 255, 255, 0.95);
  }

  /* Swipe indicator animations */
  .swipe-indicator {
    animation: swipe-hint 2s ease-in-out infinite;
  }

  @keyframes swipe-hint {
    0%, 100% { transform: translateX(0); opacity: 0.5; }
    50% { transform: translateX(10px); opacity: 1; }
  }

  /* Mobile panel content */
  .mobile-panel-content {
    padding: 16px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Hide scrollbars on mobile for cleaner look */
  .mobile-panel-content::-webkit-scrollbar {
    display: none;
  }

  /* Voice button mobile optimization */
  .voice-button-mobile {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 20;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1199px) {
  .rep-room-v3 {
    /* Tablet layout optimizations */
  }

  /* Collapsible panel animations */
  .collapsible-panel {
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .collapsible-panel.collapsed {
    width: 60px !important;
  }

  .collapsible-panel.expanded {
    width: 320px !important;
  }

  /* Resize handle visibility */
  .resize-handle {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .collapsible-panel:hover .resize-handle {
    opacity: 1;
  }

  /* Touch-friendly resize handle */
  .resize-handle {
    width: 8px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  }

  .resize-handle:hover {
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
  }
}

/* Desktop optimizations */
@media (min-width: 1200px) {
  .rep-room-v3 {
    /* Desktop-specific optimizations */
  }

  /* Enhanced hover states for desktop */
  .desktop-hover-effects button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  }
}

/* Cross-device touch optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Touch device specific styles */
  .rep-room-v3 button {
    /* Larger touch targets */
    padding: 12px 16px;
  }

  /* Remove hover effects on touch devices */
  .rep-room-v3 *:hover {
    background-color: initial !important;
  }

  /* Enhanced focus states for touch navigation */
  .rep-room-v3 button:focus,
  .rep-room-v3 [tabindex]:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

/* Orientation-specific styles */
@media (orientation: landscape) and (max-width: 767px) {
  /* Mobile landscape optimizations */
  .mobile-tab-navigation {
    /* Reduce height in landscape mode */
    padding: 8px 0;
  }

  .mobile-tab-navigation .tab-label {
    font-size: 12px;
  }
}

@media (orientation: portrait) and (max-width: 767px) {
  /* Mobile portrait optimizations */
  .mobile-panel-content {
    /* More padding in portrait mode */
    padding: 20px 16px;
  }
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Badge animations */
.badge-pulse {
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .rep-room-v3 * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .rep-room-v3 {
    --border-color: #000000;
    --text-color: #000000;
    --bg-color: #ffffff;
  }

  .rep-room-v3 button {
    border: 2px solid var(--border-color);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .rep-room-v3 {
    --bg-primary: #1f2937;
    --bg-secondary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #4b5563;
  }
}

/* Print styles */
@media print {
  .rep-room-v3 {
    /* Hide interactive elements when printing */
  }

  .mobile-tab-navigation,
  .voice-button-mobile,
  .collapsible-panel button {
    display: none !important;
  }
}