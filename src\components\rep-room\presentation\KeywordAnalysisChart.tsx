import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON>xis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Search, Filter, TrendingUp, Eye, MousePointer } from 'lucide-react';

interface KeywordData {
  term: string;
  volume: number;
  difficulty: number;
  category: 'product' | 'competitor' | 'sentiment';
  trend?: 'up' | 'down' | 'stable';
}

interface KeywordAnalysisChartProps {
  keywords: KeywordData[];
  onKeywordClick?: (keyword: KeywordData) => void;
  onFilterChange?: (filters: string[]) => void;
  className?: string;
}

const CATEGORY_COLORS = {
  product: '#3B82F6',     // Blue
  competitor: '#EF4444',  // Red
  sentiment: '#10B981'    // Green
};

const CATEGORY_LABELS = {
  product: 'Product',
  competitor: 'Competitor',
  sentiment: 'Sentiment'
};

export const KeywordAnalysisChart: React.FC<KeywordAnalysisChartProps> = ({
  keywords,
  onKeywordClick,
  onFilterChange,
  className = ''
}) => {
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'chart' | 'cloud'>('chart');
  const [hoveredKeyword, setHoveredKeyword] = useState<string | null>(null);

  // Filter keywords based on active filters
  const filteredKeywords = useMemo(() => {
    if (activeFilters.length === 0) return keywords;
    return keywords.filter(keyword => activeFilters.includes(keyword.category));
  }, [keywords, activeFilters]);

  // Prepare chart data
  const chartData = useMemo(() => {
    return filteredKeywords
      .sort((a, b) => b.volume - a.volume)
      .slice(0, 15)
      .map(keyword => ({
        ...keyword,
        displayName: keyword.term.length > 12 ? `${keyword.term.slice(0, 12)}...` : keyword.term
      }));
  }, [filteredKeywords]);

  // Handle filter toggle
  const toggleFilter = (category: string) => {
    const newFilters = activeFilters.includes(category)
      ? activeFilters.filter(f => f !== category)
      : [...activeFilters, category];
    
    setActiveFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  // Handle keyword click
  const handleKeywordClick = (data: KeywordData & { displayName: string }) => {
    const keyword = filteredKeywords.find(k => k.term === data.term);
    if (keyword && onKeywordClick) {
      onKeywordClick(keyword);
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: { active?: boolean; payload?: Array<{ payload: KeywordData & { displayName: string } }>; label?: string }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{data.term}</p>
          <p className="text-sm text-gray-600">
            Volume: {data.volume.toLocaleString()}
          </p>
          <p className="text-sm text-gray-600">
            Difficulty: {data.difficulty}/100
          </p>
          <p className="text-sm text-gray-600">
            Category: {CATEGORY_LABELS[data.category as keyof typeof CATEGORY_LABELS]}
          </p>
          {data.trend && (
            <p className="text-sm text-gray-600">
              Trend: {data.trend === 'up' ? '↗️' : data.trend === 'down' ? '↘️' : '→'} {data.trend}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Word cloud component
  const WordCloud = () => {
    const maxVolume = Math.max(...filteredKeywords.map(k => k.volume));
    
    return (
      <div className="flex flex-wrap gap-2 p-4">
        {filteredKeywords.slice(0, 50).map((keyword, index) => {
          const size = Math.max(12, (keyword.volume / maxVolume) * 32);
          const opacity = hoveredKeyword === keyword.term ? 1 : 0.8;
          
          return (
            <button
              key={index}
              className="transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1"
              style={{
                fontSize: `${size}px`,
                color: CATEGORY_COLORS[keyword.category],
                opacity
              }}
              onClick={() => onKeywordClick?.(keyword)}
              onMouseEnter={() => setHoveredKeyword(keyword.term)}
              onMouseLeave={() => setHoveredKeyword(null)}
              title={`${keyword.term} - Volume: ${keyword.volume.toLocaleString()}, Difficulty: ${keyword.difficulty}`}
            >
              {keyword.term}
            </button>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Search className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Keyword Analysis</h3>
          </div>
          
          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('chart')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'chart'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <BarChart className="w-4 h-4 inline mr-1" />
              Chart
            </button>
            <button
              onClick={() => setViewMode('cloud')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'cloud'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Eye className="w-4 h-4 inline mr-1" />
              Cloud
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">Filter by category:</span>
          </div>
          
          {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
            <button
              key={key}
              onClick={() => toggleFilter(key)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                activeFilters.includes(key)
                  ? 'text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              style={{
                backgroundColor: activeFilters.includes(key) ? CATEGORY_COLORS[key as keyof typeof CATEGORY_COLORS] : undefined
              }}
            >
              {label}
            </button>
          ))}
          
          {activeFilters.length > 0 && (
            <button
              onClick={() => {
                setActiveFilters([]);
                onFilterChange?.([]);
              }}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              Clear all
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {filteredKeywords.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No keywords match the selected filters</p>
          </div>
        ) : viewMode === 'chart' ? (
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="displayName" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis 
                  tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                  fontSize={12}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="volume" 
                  radius={[4, 4, 0, 0]}
                  cursor="pointer"
                  onClick={handleKeywordClick}
                >
                  {chartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={CATEGORY_COLORS[entry.category]}
                      opacity={hoveredKeyword === entry.term ? 1 : 0.8}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <WordCloud />
        )}

        {/* Stats Summary */}
        <div className="mt-6 grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {filteredKeywords.length}
            </div>
            <div className="text-sm text-gray-600">Total Keywords</div>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(filteredKeywords.reduce((sum, k) => sum + k.volume, 0) / filteredKeywords.length).toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Avg. Volume</div>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(filteredKeywords.reduce((sum, k) => sum + k.difficulty, 0) / filteredKeywords.length)}
            </div>
            <div className="text-sm text-gray-600">Avg. Difficulty</div>
          </div>
        </div>

        {/* Click hint */}
        {onKeywordClick && (
          <div className="mt-4 flex items-center justify-center text-sm text-gray-500">
            <MousePointer className="w-4 h-4 mr-1" />
            Click on keywords for detailed analysis
          </div>
        )}
      </div>
    </div>
  );
};