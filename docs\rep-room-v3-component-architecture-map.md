# Rep Room V3 Component Architecture Map

## Visual Component Hierarchy

```
RepRoomPageV3 (Entry Point)
├── Loading State Component
├── Error State Component  
├── Fallback Warning Banner
├── UnifiedVoiceProvider (Context)
│   ├── CopilotKit Provider
│   │   └── RepRoomInterfaceEnhanced (Main Interface)
│   │       ├── ConnectionStatusBar
│   │       ├── Responsive Layout Manager
│   │       │   ├── Mobile View
│   │       │   │   ├── MobileTabNavigation
│   │       │   │   │   ├── Tab: Participants
│   │       │   │   │   ├── Tab: Presentation  
│   │       │   │   │   └── Tab: Conversation
│   │       │   │   └── Active Tab Content
│   │       │   │       ├── ParticipantsPanel
│   │       │   │       ├── PresentationArea
│   │       │   │       └── CopilotKitConversationFlow
│   │       │   ├── Tablet View
│   │       │   │   ├── CollapsiblePanel (Left)
│   │       │   │   │   └── ParticipantsPanel
│   │       │   │   ├── PresentationArea (Center)
│   │       │   │   └── CopilotKitConversationFlow (Right)
│   │       │   └── Desktop View
│   │       │       ├── ParticipantsPanel (20% Left)
│   │       │       ├── PresentationArea (50% Center)
│   │       │       └── CopilotKitConversationFlow (30% Right)
│   │       └── Real-time Sync Integration
└── Development Info Panel (Demo Mode)
```

## Component Integration Flow

### 1. Data Flow Architecture

```
URL Parameters → RepRoomPageV3 → Configuration Loading
                      ↓
                 Demo/Production Mode Decision
                      ↓
              Enhanced State Initialization
                      ↓
               Context Providers Setup
                      ↓
            RepRoomInterfaceEnhanced Rendering
                      ↓
              Responsive Layout Detection
                      ↓
               Component Tree Rendering
```

### 2. State Management Flow

```
RepRoomState (Central State)
├── activeAgent: string | null
├── presentationContent: PresentationContent
├── messages: ChatMessage[]
├── isVoiceActive: boolean
├── participants: {
│   ├── mainAgent: Agent
│   ├── humans: Human[]
│   └── specialists: Agent[]
│ }
└── sessionInfo: {
    ├── title: string
    ├── sessionId: string
    └── slug: string
  }

State Updates Flow:
User Interaction → Component Event → State Update → Context Propagation → UI Re-render
```

### 3. Voice Integration Architecture

```
UnifiedVoiceProvider
├── LiveKit Connection Management
├── Audio Processing Pipeline
│   ├── Microphone Input
│   ├── Audio Visualization
│   ├── Speech-to-Text (STT)
│   └── Text-to-Speech (TTS)
├── Voice State Management
│   ├── Connection Status
│   ├── Audio Levels
│   ├── Speaking Detection
│   └── Turn Management
└── Voice Controls Integration
    ├── VoiceControls Component
    ├── AudioVisualizer Component
    └── TranscriptionDisplay Component
```

### 4. Agent Orchestration Architecture

```
Agent Orchestration System
├── Main Agent (Always Present)
├── Specialist Agents Pool
│   ├── SEO Specialist
│   ├── Content Specialist
│   └── Custom Specialists
├── Orchestration Logic
│   ├── Agent Selection
│   ├── Handoff Management
│   ├── Context Preservation
│   └── Response Coordination
└── UI Components
    ├── AgentOrchestrationPanel
    ├── AgentHandoffIndicator
    ├── SpecialistAgentTrigger
    └── AgentCard/Profile Components
```

## Component Dependency Graph

### Core Dependencies

```
RepRoomPageV3
├── Dependencies:
│   ├── react-router-dom (useParams, useSearchParams)
│   ├── @copilotkit/react-core (CopilotKit)
│   ├── RepRoomInterfaceEnhanced
│   ├── UnifiedVoiceProvider
│   └── RepRoomState types

RepRoomInterfaceEnhanced
├── Dependencies:
│   ├── CopilotKitConversationFlow
│   ├── PresentationArea
│   ├── ParticipantsPanel
│   ├── ConnectionStatusBar
│   ├── MobileTabNavigation
│   ├── CollapsiblePanel
│   ├── useUnifiedVoice hook
│   ├── useRepRoomSync hook
│   └── useResponsiveLayout hook
```

### Hook Dependencies

```
useRepRoomSync
├── WebSocket Management
├── Message Queuing
├── Participant Tracking
└── State Synchronization

useUnifiedVoice
├── LiveKit Integration
├── Audio Processing
├── Voice State Management
└── Connection Handling

useResponsiveLayout
├── Device Detection
├── Breakpoint Management
├── Layout Adaptation
└── Panel Management
```

## Integration Points Map

### 1. External Service Integrations

```
Rep Room V3
├── LiveKit (Voice/Video)
│   ├── WebRTC Connection
│   ├── Audio Processing
│   └── Real-time Communication
├── CopilotKit (AI Assistant)
│   ├── Runtime API
│   ├── Chat Interface
│   └── AI Response Generation
├── WebSocket Server (Real-time Sync)
│   ├── Participant Management
│   ├── Message Synchronization
│   └── State Updates
└── Configuration API
    ├── Rep Room Settings
    ├── Agent Configuration
    └── Voice Settings
```

### 2. Context Provider Integration

```
Context Hierarchy:
App Level
├── Router Context
└── Theme Context

RepRoomPageV3 Level
├── UnifiedVoiceProvider
│   ├── Voice State
│   ├── Audio Controls
│   └── Connection Management
└── CopilotKit Provider
    ├── AI Runtime
    ├── Chat State
    └── Action Handlers

Component Level
├── Responsive Layout Context
├── Mobile Navigation Context
└── Panel State Context
```

## Component Communication Patterns

### 1. Parent-Child Communication

```
RepRoomPageV3 → RepRoomInterfaceEnhanced
├── Props: initialState, onStateChange, agentName, sessionId
├── State Updates: Bidirectional via onStateChange callback
└── Configuration: Voice config, CopilotKit URL

RepRoomInterfaceEnhanced → Child Components
├── ParticipantsPanel: agents, humans, event handlers
├── PresentationArea: content, loading states
└── CopilotKitConversationFlow: messages, voice state
```

### 2. Context-Based Communication

```
Voice Context Communication:
UnifiedVoiceProvider → All Voice Components
├── Voice State: isActive, isConnecting, audioLevels
├── Controls: connect, disconnect, toggle
└── Events: onVoiceStart, onVoiceEnd, onError

Sync Context Communication:
RepRoomSync → All Sync-Aware Components
├── Connection State: connected, reconnecting, error
├── Participant Updates: join, leave, status changes
└── Message Sync: send, receive, queue
```

### 3. Event-Driven Communication

```
User Interactions → Event Handlers → State Updates
├── Voice Button Click → handleToggleVoice → Voice State Update
├── Agent Selection → handleAgentSelect → Active Agent Update
├── Message Send → handleSendMessage → Messages Array Update
└── Panel Resize → handlePanelResize → Layout State Update

Real-time Events → Sync Handlers → State Propagation
├── WebSocket Message → handleMessage → State Update
├── Voice Activity → handleVoiceActivity → Voice State Update
├── Agent Status Change → handleAgentStatus → Agent State Update
└── Participant Join/Leave → handleParticipant → Participants Update
```

## Responsive Design Architecture

### 1. Breakpoint System

```
Mobile: < 768px
├── Single Panel View
├── Tab Navigation
├── Swipe Gestures
└── Touch Optimized

Tablet: 768px - 1024px
├── Two Panel View
├── Collapsible Left Panel
├── Resizable Panels
└── Touch + Mouse Support

Desktop: > 1024px
├── Three Panel View
├── Fixed Layout Ratios
├── Full Feature Set
└── Mouse Optimized
```

### 2. Layout Adaptation Flow

```
Screen Size Detection → Layout Mode Selection → Component Rendering
├── Mobile Mode → MobileTabNavigation + Single Panel
├── Tablet Mode → CollapsiblePanel + Two Panel Layout
└── Desktop Mode → Fixed Three Panel Layout

Dynamic Adaptation:
Window Resize → useResponsiveLayout → Layout Re-calculation → Component Re-render
```

## Performance Optimization Architecture

### 1. Component Optimization

```
Memoization Strategy:
├── React.memo for Pure Components
├── useMemo for Expensive Calculations
├── useCallback for Event Handlers
└── Lazy Loading for Heavy Components

State Management Optimization:
├── Selective State Updates
├── Debounced User Inputs
├── Optimistic UI Updates
└── Background Sync
```

### 2. Resource Management

```
Memory Management:
├── Component Cleanup on Unmount
├── Event Listener Removal
├── WebSocket Connection Cleanup
└── Audio Context Cleanup

Network Optimization:
├── Message Queuing for Offline
├── Connection Retry Logic
├── Bandwidth Adaptation
└── Compression for Large Payloads
```

## Security Architecture

### 1. Data Protection

```
Sensitive Data Handling:
├── No Hardcoded Secrets
├── Environment Variable Configuration
├── Secure Token Management
└── Input Validation and Sanitization

Communication Security:
├── HTTPS/WSS Connections
├── Token-Based Authentication
├── CORS Configuration
└── Rate Limiting
```

### 2. User Privacy

```
Privacy Protection:
├── Minimal Data Collection
├── Local Storage for Preferences
├── Secure Voice Processing
└── Optional Analytics
```

## Testing Architecture

### 1. Component Testing Strategy

```
Unit Tests:
├── Individual Component Testing
├── Hook Testing
├── Utility Function Testing
└── Type Safety Validation

Integration Tests:
├── Component Interaction Testing
├── Context Provider Testing
├── API Integration Testing
└── Real-time Feature Testing
```

### 2. End-to-End Testing

```
User Journey Testing:
├── Complete Session Flow
├── Voice Interaction Testing
├── Agent Orchestration Testing
└── Responsive Design Testing

Performance Testing:
├── Load Testing
├── Memory Usage Testing
├── Network Resilience Testing
└── Audio Processing Performance
```

## Deployment Architecture

### 1. Build Configuration

```
Production Build:
├── Code Splitting
├── Asset Optimization
├── Environment Configuration
└── Performance Monitoring

Development Build:
├── Hot Module Replacement
├── Source Maps
├── Debug Tools
└── Mock Services
```

### 2. Infrastructure Requirements

```
Frontend Hosting:
├── Static Asset CDN
├── HTTPS Certificate
├── Domain Configuration
└── Caching Strategy

Backend Services:
├── LiveKit Server
├── WebSocket Server
├── CopilotKit Runtime
└── Configuration API
```

This architecture map provides a comprehensive view of how all components in Rep Room V3 are structured, integrated, and communicate with each other. It serves as a reference for understanding the system's complexity and for future development and maintenance.