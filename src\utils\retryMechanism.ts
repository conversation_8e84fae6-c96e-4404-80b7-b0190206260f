/**
 * Retry Mechanism Utilities - Phase 6: Error Handling & Edge Cases
 * 
 * Features:
 * - Configurable retry logic with exponential backoff
 * - Circuit breaker pattern for failing services
 * - Request deduplication
 * - Timeout handling
 * - Error classification and retry decisions
 */

export interface RetryConfig {
  maxAttempts?: number;
  initialDelay?: number;
  maxDelay?: number;
  exponentialBase?: number;
  jitter?: boolean;
  timeout?: number;
  retryCondition?: (error: Error, attempt: number) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
  onFailure?: (error: Error, attempts: number) => void;
}

export interface CircuitBreakerConfig {
  failureThreshold?: number;
  resetTimeout?: number;
  monitoringPeriod?: number;
}

export type CircuitBreakerState = 'closed' | 'open' | 'half-open';

export class CircuitBreaker {
  private state: CircuitBreakerState = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private successCount = 0;

  constructor(private config: CircuitBreakerConfig = {}) {
    this.config = {
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      monitoringPeriod: 10000, // 10 seconds
      ...config
    };
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout!) {
        this.state = 'half-open';
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    
    if (this.state === 'half-open') {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successes to close
        this.state = 'closed';
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.config.failureThreshold!) {
      this.state = 'open';
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  reset(): void {
    this.state = 'closed';
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = 0;
  }
}

// Global circuit breakers for different services
const circuitBreakers = new Map<string, CircuitBreaker>();

export function getCircuitBreaker(service: string, config?: CircuitBreakerConfig): CircuitBreaker {
  if (!circuitBreakers.has(service)) {
    circuitBreakers.set(service, new CircuitBreaker(config));
  }
  return circuitBreakers.get(service)!;
}

// Request deduplication cache
const requestCache = new Map<string, Promise<Response>>();

export function createRequestKey(url: string, options?: RequestInit): string {
  const method = options?.method || 'GET';
  const body = options?.body ? JSON.stringify(options.body) : '';
  return `${method}:${url}:${body}`;
}

export function clearRequestCache(): void {
  requestCache.clear();
}

// Default retry condition
export function defaultRetryCondition(error: Error, attempt: number): boolean {
  // Don't retry on client errors (4xx) except for specific cases
  if (error.message.includes('400') || error.message.includes('401') || error.message.includes('403')) {
    return false;
  }

  // Don't retry on unrecoverable errors
  if (error.message.includes('404') && attempt > 1) {
    return false;
  }

  // Retry on network errors, timeouts, and server errors (5xx)
  return (
    error.message.includes('fetch') ||
    error.message.includes('timeout') ||
    error.message.includes('500') ||
    error.message.includes('502') ||
    error.message.includes('503') ||
    error.message.includes('504') ||
    error.name === 'AbortError' ||
    error.name === 'TimeoutError'
  );
}

// Calculate delay with exponential backoff and jitter
export function calculateDelay(
  attempt: number,
  initialDelay: number,
  maxDelay: number,
  exponentialBase: number,
  jitter: boolean
): number {
  const exponentialDelay = Math.min(
    initialDelay * Math.pow(exponentialBase, attempt - 1),
    maxDelay
  );

  if (!jitter) {
    return exponentialDelay;
  }

  // Add jitter to prevent thundering herd
  const jitterRange = exponentialDelay * 0.1;
  const jitterOffset = (Math.random() - 0.5) * 2 * jitterRange;
  
  return Math.max(0, exponentialDelay + jitterOffset);
}

// Main retry function
export async function withRetry<T>(
  fn: () => Promise<T>,
  config: RetryConfig = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    initialDelay = 1000,
    maxDelay = 30000,
    exponentialBase = 2,
    jitter = true,
    timeout = 10000,
    retryCondition = defaultRetryCondition,
    onRetry,
    onFailure
  } = config;

  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      // Add timeout to the function execution
      const result = await Promise.race([
        fn(),
        new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('TimeoutError')), timeout);
        })
      ]);
      
      return result;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      console.warn(`[RetryMechanism] Attempt ${attempt}/${maxAttempts} failed:`, lastError.message);
      
      // Check if we should retry
      if (attempt === maxAttempts || !retryCondition(lastError, attempt)) {
        onFailure?.(lastError, attempt);
        throw lastError;
      }
      
      // Calculate delay for next attempt
      const delay = calculateDelay(attempt, initialDelay, maxDelay, exponentialBase, jitter);
      
      onRetry?.(lastError, attempt);
      console.log(`[RetryMechanism] Retrying in ${delay}ms...`);
      
      // Wait before next attempt
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

// Fetch with retry and circuit breaker
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryConfig: RetryConfig = {},
  serviceName = 'default'
): Promise<Response> {
  const requestKey = createRequestKey(url, options);
  
  // Check if request is already in progress (deduplication)
  if (requestCache.has(requestKey)) {
    console.log(`[RetryMechanism] Deduplicating request: ${requestKey}`);
    return requestCache.get(requestKey);
  }
  
  const circuitBreaker = getCircuitBreaker(serviceName);
  
  const requestPromise = withRetry(
    () => circuitBreaker.execute(async () => {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return response;
    }),
    {
      retryCondition: (error, attempt) => {
        // Don't retry if circuit breaker is open
        if (error.message === 'Circuit breaker is open') {
          return false;
        }
        return defaultRetryCondition(error, attempt);
      },
      onRetry: (error, attempt) => {
        console.log(`[RetryMechanism] Retrying ${serviceName} request (${attempt}): ${url}`);
        retryConfig.onRetry?.(error, attempt);
      },
      onFailure: (error, attempts) => {
        console.error(`[RetryMechanism] ${serviceName} request failed after ${attempts} attempts: ${url}`, error);
        retryConfig.onFailure?.(error, attempts);
      },
      ...retryConfig
    }
  );
  
  // Cache the promise
  requestCache.set(requestKey, requestPromise);
  
  try {
    const result = await requestPromise;
    return result;
  } finally {
    // Remove from cache after completion
    requestCache.delete(requestKey);
  }
}

// Specialized retry functions for different types of operations

export async function retryDatabaseOperation<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: 3,
    initialDelay: 500,
    maxDelay: 5000,
    retryCondition: (error) => {
      // Retry on connection errors, timeouts, and temporary database issues
      return (
        error.message.includes('connection') ||
        error.message.includes('timeout') ||
        error.message.includes('temporary') ||
        error.message.includes('PGRST') // PostgREST errors
      );
    },
    ...config
  });
}

export async function retryLiveKitOperation<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: 5,
    initialDelay: 1000,
    maxDelay: 10000,
    retryCondition: (error) => {
      // Retry on connection errors and WebRTC issues
      return (
        error.message.includes('connection') ||
        error.message.includes('websocket') ||
        error.message.includes('webrtc') ||
        error.message.includes('ice') ||
        error.message.includes('signaling')
      );
    },
    ...config
  });
}

export async function retryVoiceTokenRequest<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 8000,
    retryCondition: (error, attempt) => {
      // Don't retry on authentication errors
      if (error.message.includes('401') || error.message.includes('403')) {
        return false;
      }
      
      // Retry on server errors and network issues
      return defaultRetryCondition(error, attempt);
    },
    ...config
  });
}

// Health check utility
export async function healthCheck(
  url: string,
  timeout = 5000
): Promise<{ healthy: boolean; latency: number; error?: string }> {
  const startTime = performance.now();
  
  try {
    const response = await Promise.race([
      fetch(url, { method: 'HEAD', cache: 'no-cache' }),
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), timeout);
      })
    ]);
    
    const latency = performance.now() - startTime;
    
    return {
      healthy: response.ok,
      latency: Math.round(latency),
      error: response.ok ? undefined : `HTTP ${response.status}`
    };
  } catch (error) {
    const latency = performance.now() - startTime;
    return {
      healthy: false,
      latency: Math.round(latency),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export default {
  withRetry,
  fetchWithRetry,
  retryDatabaseOperation,
  retryLiveKitOperation,
  retryVoiceTokenRequest,
  healthCheck,
  CircuitBreaker,
  getCircuitBreaker,
  clearRequestCache
};