
/* Rep Room V3 Responsive Styles */
@import './styles/rep-room-responsive.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Mem0 Light Mode Color Palette */
    --background: 210 20% 97%; /* #F7F9FA */
    --foreground: 210 9% 9%; /* #16181D */

    --card: 0 0% 100%; /* #FFFFFF */
    --card-foreground: 210 9% 9%; /* #16181D */

    --popover: 0 0% 100%; /* #FFFFFF */
    --popover-foreground: 210 9% 9%; /* #16181D */

    /* Primary Blue: #4583FF */
    --primary: 219 100% 63%; /* #4583FF */
    --primary-foreground: 0 0% 100%; /* White text on blue */

    /* Secondary colors */
    --secondary: 210 17% 96%; /* Light background for secondary elements */
    --secondary-foreground: 210 9% 9%; /* #16181D */

    /* Muted colors */
    --muted: 210 17% 96%; /* Light muted background */
    --muted-foreground: 215 14% 65%; /* #98A2B3 - muted text */

    /* Accent colors */
    --accent: 210 17% 96%;
    --accent-foreground: 210 9% 9%;

    /* Destructive (Error) */
    --destructive: 4 90% 58%; /* #FF3B30 */
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs */
    --border: 215 25% 89%; /* #E3E8EF */
    --input: 215 25% 89%; /* #E3E8EF */
    --ring: 219 100% 63%; /* #4583FF for focus rings */

    /* Mem0 Semantic Colors */
    --success: 142 76% 36%; /* #34C759 */
    --success-foreground: 0 0% 100%;
    
    --warning: 54 100% 50%; /* #FFD600 */
    --warning-foreground: 210 9% 9%; /* Dark text on yellow */
    
    --error: 4 90% 58%; /* #FF3B30 */
    --error-foreground: 0 0% 100%;
    
    --info: 219 100% 63%; /* #4583FF - same as primary */
    --info-foreground: 0 0% 100%;

    --radius: 0.75rem; /* 12px default radius */

    /* Sidebar colors for light mode */
    --sidebar-background: 0 0% 98%; /* Very light gray */
    --sidebar-foreground: 210 9% 9%; /* #16181D */
    --sidebar-primary: 219 100% 63%; /* #4583FF */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 17% 96%;
    --sidebar-accent-foreground: 210 9% 9%;
    --sidebar-border: 215 25% 89%; /* #E3E8EF */
    --sidebar-ring: 219 100% 63%; /* #4583FF */
  }

  .dark {
    /* Mem0 Dark Mode Color Palette */
    --background: 215 28% 11%; /* #181C22 */
    --foreground: 210 20% 97%; /* #F7F9FA */

    --card: 215 25% 17%; /* #23272F */
    --card-foreground: 210 20% 97%; /* #F7F9FA */

    --popover: 215 25% 17%; /* #23272F */
    --popover-foreground: 210 20% 97%; /* #F7F9FA */

    /* Primary remains the same blue */
    --primary: 219 100% 63%; /* #4583FF */
    --primary-foreground: 0 0% 100%;

    /* Secondary colors for dark mode */
    --secondary: 215 25% 17%; /* #23272F */
    --secondary-foreground: 210 20% 97%; /* #F7F9FA */

    /* Muted colors for dark mode */
    --muted: 215 25% 17%; /* #23272F */
    --muted-foreground: 215 14% 65%; /* #98A2B3 */

    /* Accent colors for dark mode */
    --accent: 215 25% 17%; /* #23272F */
    --accent-foreground: 210 20% 97%; /* #F7F9FA */

    /* Destructive (Error) - slightly darker for dark mode */
    --destructive: 4 90% 58%; /* #FF3B30 */
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs for dark mode */
    --border: 215 25% 17%; /* #23272F */
    --input: 215 25% 17%; /* #23272F */
    --ring: 219 100% 63%; /* #4583FF */

    /* Semantic colors remain mostly the same */
    --success: 142 76% 36%; /* #34C759 */
    --success-foreground: 0 0% 100%;
    
    --warning: 54 100% 50%; /* #FFD600 */
    --warning-foreground: 210 9% 9%; /* Dark text on yellow */
    
    --error: 4 90% 58%; /* #FF3B30 */
    --error-foreground: 0 0% 100%;
    
    --info: 219 100% 63%; /* #4583FF */
    --info-foreground: 0 0% 100%;

    /* Sidebar colors for dark mode */
    --sidebar-background: 215 28% 11%; /* #181C22 */
    --sidebar-foreground: 210 20% 97%; /* #F7F9FA */
    --sidebar-primary: 219 100% 63%; /* #4583FF */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 25% 17%; /* #23272F */
    --sidebar-accent-foreground: 210 20% 97%; /* #F7F9FA */
    --sidebar-border: 215 25% 17%; /* #23272F */
    --sidebar-ring: 219 100% 63%; /* #4583FF */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    line-height: 1.5;
  }

  /* Mem0 Typography Scale */
  h1 {
    @apply text-3xl font-semibold tracking-tight leading-relaxed;
  }

  h2 {
    @apply text-2xl font-semibold tracking-tight leading-relaxed;
  }

  h3 {
    @apply text-xl font-semibold tracking-tight leading-relaxed;
  }

  h4 {
    @apply text-lg font-medium leading-relaxed;
  }

  h5 {
    @apply text-base font-medium leading-relaxed;
  }

  h6 {
    @apply text-sm font-medium leading-relaxed;
  }

  /* Base text styles */
  p {
    @apply leading-relaxed;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2;
  }
}

@layer components {
  .active-nav-link {
    @apply font-medium;
  }
  
  /* Mem0 Card Components */
  .card-mem0 {
    @apply bg-card text-card-foreground rounded-mem0-card border border-border shadow-mem0;
  }
  
  .card-mem0:hover {
    @apply shadow-mem0-hover;
  }
  
  /* Card styling with semantic colors */
  .card-highlight {
    @apply card-mem0 border-l-4 border-l-primary/60;
  }
  
  .card-success {
    @apply card-mem0 border-l-4 border-l-success/60;
  }
  
  .card-warning {
    @apply card-mem0 border-l-4 border-l-warning/60;
  }
  
  .card-error {
    @apply card-mem0 border-l-4 border-l-error/60;
  }
  
  .card-info {
    @apply card-mem0 border-l-4 border-l-info/60;
  }

  /* Mem0 Button Components */
  .btn-mem0 {
    @apply inline-flex items-center justify-center rounded-mem0-input px-4 py-2 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-mem0-primary {
    @apply btn-mem0 bg-primary text-primary-foreground shadow-mem0 hover:bg-primary/90 hover:shadow-mem0-hover;
  }
  
  .btn-mem0-secondary {
    @apply btn-mem0 bg-secondary text-secondary-foreground border border-border shadow-mem0 hover:bg-secondary/80 hover:shadow-mem0-hover;
  }
  
  .btn-mem0-outline {
    @apply btn-mem0 border border-border bg-transparent hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-mem0-ghost {
    @apply btn-mem0 hover:bg-accent hover:text-accent-foreground;
  }

  /* Mem0 Input Components */
  .input-mem0 {
    @apply flex h-10 w-full rounded-mem0-input border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  /* Mem0 Badge Components */
  .badge-mem0 {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge-mem0 bg-muted text-muted-foreground hover:bg-muted/80;
  }
  
  .badge-success {
    @apply badge-mem0 bg-success/10 text-success border border-success/20;
  }
  
  .badge-warning {
    @apply badge-mem0 bg-warning/10 text-warning-foreground border border-warning/20;
  }
  
  .badge-error {
    @apply badge-mem0 bg-error/10 text-error-foreground border border-error/20;
  }
  
  .badge-info {
    @apply badge-mem0 bg-info/10 text-info-foreground border border-info/20;
  }

  /* Page header standardization */
  .page-header {
    @apply flex flex-col md:flex-row md:items-center justify-between gap-mem0-md mb-mem0-lg;
  }

  .page-header-content {
    @apply space-y-mem0-xs;
  }

  .page-header-actions {
    @apply flex flex-wrap items-center gap-mem0-sm mt-mem0-sm md:mt-0;
  }

  /* Standard page layout with Mem0 spacing */
  .page-container {
    @apply py-mem0-lg space-y-mem0-lg;
  }

  /* Standard form layout with Mem0 spacing */
  .form-container {
    @apply space-y-mem0-lg;
  }
  
  /* Mem0 Grid Layouts */
  .grid-mem0 {
    @apply grid gap-mem0-lg;
  }
  
  .grid-mem0-sm {
    @apply grid gap-mem0-md;
  }
  
  .grid-mem0-cols-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-mem0-lg;
  }
  
  .grid-mem0-cols-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-mem0-lg;
  }
  
  .grid-mem0-cols-4 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-mem0-lg;
  }
}

@layer utilities {
  /* New animation for skeleton loading */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite linear;
  }
  
  /* Mem0 Gradient backgrounds */
  .bg-gradient-card {
    @apply bg-gradient-to-br from-card to-background;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-br from-primary/5 to-primary/10;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-br from-secondary/20 to-secondary/30;
  }
  
  .bg-gradient-success {
    @apply bg-gradient-to-br from-success/5 to-success/10;
  }
  
  .bg-gradient-warning {
    @apply bg-gradient-to-br from-warning/5 to-warning/10;
  }
  
  .bg-gradient-error {
    @apply bg-gradient-to-br from-error/5 to-error/10;
  }

  /* Mem0 spacing utilities */
  .mem0-spacing {
    @apply space-y-mem0-lg;
  }
  
  .mem0-spacing-sm {
    @apply space-y-mem0-md;
  }
  
  .mem0-spacing-xs {
    @apply space-y-mem0-sm;
  }

  /* Mem0 responsive utilities */
  .mem0-container {
    @apply w-full max-w-none px-mem0-md sm:px-mem0-lg lg:px-mem0-xl;
  }
  
  .mem0-section {
    @apply py-mem0-2xl;
  }
  
  .mem0-section-sm {
    @apply py-mem0-xl;
  }

  /* Mem0 text utilities */
  .text-mem0-primary {
    @apply text-foreground font-medium;
  }
  
  .text-mem0-secondary {
    @apply text-muted-foreground;
  }
  
  .text-mem0-muted {
    @apply text-muted-foreground/70;
  }

  /* Mem0 border utilities */
  .border-mem0 {
    @apply border border-border;
  }
  
  .border-mem0-light {
    @apply border border-border/50;
  }
  
  .border-mem0-strong {
    @apply border-2 border-border;
  }

  /* Mem0 shadow utilities */
  .shadow-mem0-card {
    @apply shadow-mem0;
  }
  
  .shadow-mem0-elevated {
    @apply shadow-mem0-hover;
  }
  
  .shadow-mem0-focus {
    @apply shadow-mem0-focus;
  }

  /* Mem0 transition utilities */
  .transition-mem0 {
    @apply transition-all duration-200 ease-out;
  }
  
  .transition-mem0-fast {
    @apply transition-all duration-150 ease-out;
  }
  
  .transition-mem0-slow {
    @apply transition-all duration-300 ease-out;
  }

  /* Standard spacing utilities (backward compatibility) */
  .standard-spacing {
    @apply space-y-6;
  }

  .standard-grid {
    @apply grid gap-4;
  }

  .standard-grid-cols-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4;
  }

  .standard-grid-cols-3 {
    @apply grid grid-cols-1 md:grid-cols-3 gap-6;
  }

  /* CopilotKit Chat Layout Fixes */
  .copilotkit-chat-container {
    @apply h-full flex flex-col;
  }

  .copilotkit-chat-messages {
    @apply flex-1 overflow-y-auto;
  }

  .copilotkit-chat-input {
    @apply sticky bottom-0 bg-background border-t p-4;
  }

  /* Override CopilotKit default styles for proper layout */
  [data-copilot-chat] {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  [data-copilot-chat] > div:first-child {
    flex: 1 !important;
    overflow-y: auto !important;
  }

  [data-copilot-chat] > div:last-child {
    position: sticky !important;
    bottom: 0 !important;
    background: hsl(var(--background)) !important;
    border-top: 1px solid hsl(var(--border)) !important;
    padding: 1rem !important;
  }

  /* Ensure chat input stays at bottom */
  .copilotkit-input-container {
    position: sticky !important;
    bottom: 0 !important;
    background: hsl(var(--background)) !important;
    border-top: 1px solid hsl(var(--border)) !important;
    padding: 1rem !important;
    z-index: 10 !important;
  }

  /* More aggressive override */
  .poweredBy, 
  p.poweredBy, 
  div p.poweredBy, 
  [class*="copilotKit"] .poweredBy {
    color: white !important;
    background-color: transparent !important;
  }
}
