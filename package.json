{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:server": "node server/copilotkit-server.js", "dev:full": "concurrently \"npm run dev:server\" \"npm run dev\"", "build": "vite build", "build:dev": "vite build --mode development", "build:deploy": "tsc --project tsconfig.deploy.json && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,md}\"", "preview": "vite preview", "test-build": "node scripts/test-build.js", "test": "jest --config jest.config.cjs", "test:watch": "jest --config jest.config.cjs --watchAll", "test:coverage": "jest --config jest.config.cjs --coverage", "test:all": "npm run test", "test:integration": "node tests/integration/test-voice-system-complete.js", "setup-vad": "node scripts/setup-vad-files.js", "postinstall": "npm run setup-vad", "prebuild": "npm run setup-vad", "predev": "npm run setup-vad"}, "dependencies": {"@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@copilotkit/react-core": "^1.8.13", "@copilotkit/react-ui": "^1.8.13", "@copilotkit/runtime": "^1.8.13", "@deepgram/sdk": "^4.2.0", "@hookform/resolvers": "^3.9.0", "@livekit/components-react": "^2.0.0", "@mastra/agui": "^1.0.2-alpha.1", "@mastra/client-js": "^0.10.1", "@mastra/core": "^0.10.2-alpha.1", "@mastra/deployer": "^0.10.1", "@mastra/deployer-vercel": "^0.10.1", "@mastra/libsql": "^0.10.0", "@mastra/loggers": "^0.10.0", "@mastra/memory": "^0.10.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@ricky0123/vad-react": "^0.0.24", "@ricky0123/vad-web": "^0.0.24", "@supabase/supabase-js": "^2.49.4", "@swc/plugin-emotion": "^9.0.3", "@tanstack/react-query": "^5.56.2", "@types/dompurify": "^3.0.5", "ai": "^4.3.16", "axios": "^1.9.0", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "commander": "^13.1.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "http-proxy-middleware": "^3.0.5", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "livekit-client": "^2.13.4", "livekit-server-sdk": "^2.13.0", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "nanoid": "^5.1.5", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "open": "^10.1.2", "puppeteer": "^24.8.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "vitest": "^3.1.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "prettier": "^3.2.5", "serve-handler": "^6.1.5", "supabase": "^2.22.6", "tailwindcss": "^3.4.11", "terser": "^5.39.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}