#!/usr/bin/env node

/**
 * Rep Room V3 Integration Verification Script
 * 
 * This script verifies that all Rep Room V3 components are properly integrated
 * and the /rroom-v3/ route is production-ready.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '../..');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}${msg}${colors.reset}`)
};

// Verification results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

function addResult(type, category, item, status, message = '') {
  results.details.push({ type, category, item, status, message });
  if (status === 'pass') results.passed++;
  else if (status === 'fail') results.failed++;
  else if (status === 'warning') results.warnings++;
}

function fileExists(filePath) {
  return fs.existsSync(path.join(projectRoot, filePath));
}

function readFile(filePath) {
  try {
    return fs.readFileSync(path.join(projectRoot, filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

function checkImport(fileContent, importName) {
  return fileContent.includes(importName);
}

// Core component files verification
function verifyComponentFiles() {
  log.header('Verifying Core Component Files');
  
  const coreComponents = [
    'src/pages/RepRoomPageV3.tsx',
    'src/components/rep-room/RepRoomInterfaceEnhanced.tsx',
    'src/components/rep-room/PresentationArea.tsx',
    'src/components/rep-room/ParticipantsPanel.tsx',
    'src/components/rep-room/CopilotKitConversationFlow.tsx',
    'src/components/rep-room/ConnectionStatusBar.tsx'
  ];

  coreComponents.forEach(component => {
    if (fileExists(component)) {
      log.success(`Found: ${component}`);
      addResult('component', 'core', component, 'pass');
    } else {
      log.error(`Missing: ${component}`);
      addResult('component', 'core', component, 'fail', 'File not found');
    }
  });
}

// Voice integration components verification
function verifyVoiceComponents() {
  log.header('Verifying Voice Integration Components');
  
  const voiceComponents = [
    'src/components/rep-room/VoiceControls.tsx',
    'src/components/rep-room/VoiceControlsUnified.tsx',
    'src/components/rep-room/AudioVisualizer.tsx',
    'src/components/rep-room/TranscriptionDisplay.tsx',
    'src/contexts/rroom/UnifiedVoiceContext.tsx'
  ];

  voiceComponents.forEach(component => {
    if (fileExists(component)) {
      log.success(`Found: ${component}`);
      addResult('component', 'voice', component, 'pass');
    } else {
      log.error(`Missing: ${component}`);
      addResult('component', 'voice', component, 'fail', 'File not found');
    }
  });
}

// Agent orchestration components verification
function verifyAgentComponents() {
  log.header('Verifying Agent Orchestration Components');
  
  const agentComponents = [
    'src/components/rep-room/agents/AgentOrchestrationPanel.tsx',
    'src/components/rep-room/agents/AgentHandoffIndicator.tsx',
    'src/components/rep-room/agents/SpecialistAgentTrigger.tsx',
    'src/components/rep-room/AgentCard.tsx',
    'src/components/rep-room/AgentProfile.tsx'
  ];

  agentComponents.forEach(component => {
    if (fileExists(component)) {
      log.success(`Found: ${component}`);
      addResult('component', 'agent', component, 'pass');
    } else {
      log.error(`Missing: ${component}`);
      addResult('component', 'agent', component, 'fail', 'File not found');
    }
  });
}

// Mobile and responsive components verification
function verifyResponsiveComponents() {
  log.header('Verifying Responsive Design Components');
  
  const responsiveComponents = [
    'src/components/rep-room/mobile/MobileTabNavigation.tsx',
    'src/components/rep-room/mobile/CollapsiblePanel.tsx',
    'src/hooks/useResponsiveLayout.ts'
  ];

  responsiveComponents.forEach(component => {
    if (fileExists(component)) {
      log.success(`Found: ${component}`);
      addResult('component', 'responsive', component, 'pass');
    } else {
      log.error(`Missing: ${component}`);
      addResult('component', 'responsive', component, 'fail', 'File not found');
    }
  });
}

// Hooks and utilities verification
function verifyHooksAndUtils() {
  log.header('Verifying Hooks and Utilities');
  
  const hooks = [
    'src/hooks/useRepRoomSync.ts',
    'src/hooks/useResponsiveLayout.ts',
    'src/types/rep-room.ts'
  ];

  hooks.forEach(hook => {
    if (fileExists(hook)) {
      log.success(`Found: ${hook}`);
      addResult('hook', 'utility', hook, 'pass');
    } else {
      log.error(`Missing: ${hook}`);
      addResult('hook', 'utility', hook, 'fail', 'File not found');
    }
  });
}

// Route integration verification
function verifyRouteIntegration() {
  log.header('Verifying Route Integration');
  
  const appFile = readFile('src/App.tsx');
  if (!appFile) {
    log.error('Cannot read src/App.tsx');
    addResult('integration', 'routing', 'App.tsx', 'fail', 'File not readable');
    return;
  }

  // Check for RepRoomPageV3 import
  if (checkImport(appFile, 'RepRoomPageV3')) {
    log.success('RepRoomPageV3 imported in App.tsx');
    addResult('integration', 'routing', 'RepRoomPageV3 import', 'pass');
  } else {
    log.error('RepRoomPageV3 not imported in App.tsx');
    addResult('integration', 'routing', 'RepRoomPageV3 import', 'fail');
  }

  // Check for route definition
  if (appFile.includes('/rroom-v3/:slug/:sessionId?')) {
    log.success('Route /rroom-v3/:slug/:sessionId? defined');
    addResult('integration', 'routing', 'Route definition', 'pass');
  } else {
    log.error('Route /rroom-v3/:slug/:sessionId? not found');
    addResult('integration', 'routing', 'Route definition', 'fail');
  }
}

// Context providers verification
function verifyContextProviders() {
  log.header('Verifying Context Providers');
  
  const repRoomPageV3 = readFile('src/pages/RepRoomPageV3.tsx');
  if (!repRoomPageV3) {
    log.error('Cannot read RepRoomPageV3.tsx');
    addResult('integration', 'context', 'RepRoomPageV3.tsx', 'fail', 'File not readable');
    return;
  }

  // Check for UnifiedVoiceProvider
  if (checkImport(repRoomPageV3, 'UnifiedVoiceProvider')) {
    log.success('UnifiedVoiceProvider integrated');
    addResult('integration', 'context', 'UnifiedVoiceProvider', 'pass');
  } else {
    log.error('UnifiedVoiceProvider not integrated');
    addResult('integration', 'context', 'UnifiedVoiceProvider', 'fail');
  }

  // Check for CopilotKit
  if (checkImport(repRoomPageV3, 'CopilotKit')) {
    log.success('CopilotKit integrated');
    addResult('integration', 'context', 'CopilotKit', 'pass');
  } else {
    log.error('CopilotKit not integrated');
    addResult('integration', 'context', 'CopilotKit', 'fail');
  }
}

// Test suite verification
function verifyTestSuite() {
  log.header('Verifying Test Suite');
  
  const testSuite = 'src/components/rep-room/test/RepRoomV3TestSuite.tsx';
  if (fileExists(testSuite)) {
    log.success('RepRoomV3TestSuite component found');
    addResult('testing', 'suite', 'RepRoomV3TestSuite', 'pass');
  } else {
    log.error('RepRoomV3TestSuite component missing');
    addResult('testing', 'suite', 'RepRoomV3TestSuite', 'fail');
  }
}

// Documentation verification
function verifyDocumentation() {
  log.header('Verifying Documentation');
  
  const docs = [
    'docs/rep-room-v3-implementation-summary.md',
    'docs/rep-room-v3-component-architecture-map.md'
  ];

  docs.forEach(doc => {
    if (fileExists(doc)) {
      log.success(`Found: ${doc}`);
      addResult('documentation', 'guide', doc, 'pass');
    } else {
      log.error(`Missing: ${doc}`);
      addResult('documentation', 'guide', doc, 'fail');
    }
  });
}

// Configuration verification
function verifyConfiguration() {
  log.header('Verifying Configuration');
  
  const packageJson = readFile('package.json');
  if (!packageJson) {
    log.error('Cannot read package.json');
    addResult('config', 'dependencies', 'package.json', 'fail', 'File not readable');
    return;
  }

  const pkg = JSON.parse(packageJson);
  
  // Check for required dependencies
  const requiredDeps = [
    '@copilotkit/react-core',
    'react-router-dom'
  ];

  requiredDeps.forEach(dep => {
    if (pkg.dependencies?.[dep] || pkg.devDependencies?.[dep]) {
      log.success(`Dependency found: ${dep}`);
      addResult('config', 'dependencies', dep, 'pass');
    } else {
      log.warning(`Dependency missing: ${dep}`);
      addResult('config', 'dependencies', dep, 'warning', 'May need to be installed');
    }
  });
}

// Performance checks
function verifyPerformance() {
  log.header('Verifying Performance Considerations');
  
  const repRoomInterface = readFile('src/components/rep-room/RepRoomInterfaceEnhanced.tsx');
  if (!repRoomInterface) {
    log.warning('Cannot read RepRoomInterfaceEnhanced.tsx for performance checks');
    addResult('performance', 'optimization', 'RepRoomInterfaceEnhanced.tsx', 'warning', 'File not readable');
    return;
  }

  // Check for React.memo usage
  if (repRoomInterface.includes('React.memo') || repRoomInterface.includes('useMemo') || repRoomInterface.includes('useCallback')) {
    log.success('Performance optimizations found (memo/useMemo/useCallback)');
    addResult('performance', 'optimization', 'React optimizations', 'pass');
  } else {
    log.warning('No obvious performance optimizations found');
    addResult('performance', 'optimization', 'React optimizations', 'warning', 'Consider adding React.memo, useMemo, useCallback');
  }
}

// Main verification function
function runVerification() {
  console.log(`${colors.bold}${colors.blue}Rep Room V3 Integration Verification${colors.reset}`);
  console.log('='.repeat(50));

  verifyComponentFiles();
  verifyVoiceComponents();
  verifyAgentComponents();
  verifyResponsiveComponents();
  verifyHooksAndUtils();
  verifyRouteIntegration();
  verifyContextProviders();
  verifyTestSuite();
  verifyDocumentation();
  verifyConfiguration();
  verifyPerformance();

  // Summary
  log.header('Verification Summary');
  console.log(`${colors.green}Passed: ${results.passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${results.failed}${colors.reset}`);
  console.log(`${colors.yellow}Warnings: ${results.warnings}${colors.reset}`);

  // Detailed results by category
  const categories = [...new Set(results.details.map(r => r.category))];
  categories.forEach(category => {
    const categoryResults = results.details.filter(r => r.category === category);
    const passed = categoryResults.filter(r => r.status === 'pass').length;
    const failed = categoryResults.filter(r => r.status === 'fail').length;
    const warnings = categoryResults.filter(r => r.status === 'warning').length;
    
    console.log(`\n${colors.bold}${category.toUpperCase()}:${colors.reset} ${passed} passed, ${failed} failed, ${warnings} warnings`);
    
    categoryResults.forEach(result => {
      const statusColor = result.status === 'pass' ? colors.green : 
                         result.status === 'fail' ? colors.red : colors.yellow;
      const statusSymbol = result.status === 'pass' ? '✓' : 
                          result.status === 'fail' ? '✗' : '⚠';
      console.log(`  ${statusColor}${statusSymbol}${colors.reset} ${result.item}${result.message ? ` (${result.message})` : ''}`);
    });
  });

  // Production readiness assessment
  log.header('Production Readiness Assessment');
  
  const criticalFailures = results.details.filter(r => r.status === 'fail' && 
    (r.category === 'core' || r.category === 'routing')).length;
  
  if (criticalFailures === 0) {
    log.success('Rep Room V3 appears to be production-ready for demo environments');
    console.log(`${colors.green}✓ All critical components are in place${colors.reset}`);
    console.log(`${colors.green}✓ Route integration is complete${colors.reset}`);
    console.log(`${colors.green}✓ Context providers are properly configured${colors.reset}`);
  } else {
    log.error(`${criticalFailures} critical issues found - not production ready`);
  }

  if (results.warnings > 0) {
    log.warning(`${results.warnings} warnings found - review recommended before production deployment`);
  }

  // Exit code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Run verification
runVerification();