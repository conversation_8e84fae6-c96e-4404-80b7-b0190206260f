import '@testing-library/jest-dom';

// Setup DOM environment
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost',
    origin: 'http://localhost',
    protocol: 'http:',
    host: 'localhost',
    hostname: 'localhost',
    port: '',
    pathname: '/',
    search: '',
    hash: '',
  },
  writable: true,
});

// Mock matchMedia for tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock IntersectionObserver
global.IntersectionObserver = class MockIntersectionObserver implements IntersectionObserver {
  readonly root: Element | null = null;
  readonly rootMargin: string = '0px';
  readonly thresholds: ReadonlyArray<number> = [0];
  
  constructor(private callback: IntersectionObserverCallback) {}
  
  observe() {}
  unobserve() {}
  disconnect() {}
  takeRecords(): IntersectionObserverEntry[] { return []; }
};

// Mock browser APIs for connection state tests
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
});

// Mock window event listeners
const mockAddEventListener = jest.fn();
const mockRemoveEventListener = jest.fn();

Object.defineProperty(window, 'addEventListener', {
  value: mockAddEventListener,
  writable: true,
});

Object.defineProperty(window, 'removeEventListener', {
  value: mockRemoveEventListener,
  writable: true,
});

// Mock Response constructor first
global.Response = class MockResponse {
  ok: boolean;
  status: number;
  statusText: string;
  headers: Headers;
  body: string | null;

  constructor(body?: string | null, init?: ResponseInit) {
    this.body = body || null;
    this.status = init?.status || 200;
    this.statusText = init?.statusText || 'OK';
    this.ok = this.status >= 200 && this.status < 300;
    this.headers = new Headers(init?.headers);
  }

  async text() { return this.body || ''; }
  async json() { return this.body ? JSON.parse(this.body) : {}; }
  async blob() { return new Blob(); }
  async arrayBuffer() { return new ArrayBuffer(0); }
} as typeof Response;

// Mock fetch (now that Response is defined)
global.fetch = jest.fn().mockResolvedValue(new Response('', { status: 200, statusText: 'OK' }));

// Mock performance API
Object.defineProperty(global, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
  },
  writable: true,
});

// Mock AbortController
global.AbortController = class MockAbortController {
  signal = {
    aborted: false,
    onabort: null,
    reason: undefined,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
    throwIfAborted: jest.fn(),
  };
  
  abort() {
    this.signal.aborted = true;
  }
} as typeof AbortController;

// Export mocks for use in tests
export { mockAddEventListener, mockRemoveEventListener };