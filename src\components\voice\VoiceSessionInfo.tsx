import React from 'react';
import { useUnifiedVoice } from '../../contexts/rroom/UnifiedVoiceContext';

interface VoiceSessionInfoProps {
  className?: string;
  showInactivityTimer?: boolean;
}

/**
 * VoiceSessionInfo Component
 * 
 * Displays voice session information including:
 * - Session duration
 * - Inactivity timeout countdown
 * - Connection status
 */
export function VoiceSessionInfo({ 
  className = '',
  showInactivityTimer = true 
}: VoiceSessionInfoProps) {
  const { state } = useUnifiedVoice();

  // Format time in MM:SS format
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Don't show if not connected
  if (!state.isConnected) {
    return null;
  }

  const sessionDuration = state.sessionDuration;
  const inactivityTimeRemaining = state.inactivityTimeRemaining;
  const isWarningActive = state.inactivityWarningActive;

  return (
    <div className={`text-sm text-gray-600 space-y-1 ${className}`}>
      {/* Session Duration */}
      <div className="flex items-center space-x-2">
        <svg 
          className="h-4 w-4 text-green-500" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
          />
        </svg>
        <span>Session: {formatTime(sessionDuration)}</span>
      </div>

      {/* Inactivity Timer */}
      {showInactivityTimer && (
        <div className="flex items-center space-x-2">
          <svg 
            className={`h-4 w-4 ${
              isWarningActive 
                ? inactivityTimeRemaining <= 10 
                  ? 'text-red-500' 
                  : 'text-yellow-500'
                : 'text-gray-400'
            }`}
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 9v3l3 3m-3-12a9 9 0 110 18 9 9 0 010-18z" 
            />
          </svg>
          <span className={
            isWarningActive 
              ? inactivityTimeRemaining <= 10 
                ? 'text-red-600 font-medium' 
                : 'text-yellow-600 font-medium'
              : 'text-gray-600'
          }>
            Auto-disconnect: {formatTime(inactivityTimeRemaining)}
          </span>
        </div>
      )}

      {/* Connection Status */}
      <div className="flex items-center space-x-2">
        <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
        <span className="text-green-600">Connected</span>
      </div>

      {/* Warning Message */}
      {isWarningActive && (
        <div className={`text-xs mt-2 p-2 rounded ${
          inactivityTimeRemaining <= 10 
            ? 'bg-red-50 text-red-700 border border-red-200' 
            : 'bg-yellow-50 text-yellow-700 border border-yellow-200'
        }`}>
          {inactivityTimeRemaining <= 10 
            ? '⚠️ Disconnecting soon! Speak or interact to stay connected.'
            : '💤 Inactive session detected. Speak or interact to avoid disconnection.'
          }
        </div>
      )}
    </div>
  );
}

export default VoiceSessionInfo;