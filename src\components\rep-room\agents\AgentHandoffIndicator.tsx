import React from 'react';
import { ArrowR<PERSON>, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { Agent, AgentHandoff, HandoffStatus } from '../../../types/rep-room';
import { StatusIndicator } from '../StatusIndicator';

interface AgentHandoffIndicatorProps {
  handoff: AgentHandoff;
  fromAgent?: Agent;
  toAgent?: Agent;
  showDetails?: boolean;
  className?: string;
  animated?: boolean;
}

export const AgentHandoffIndicator: React.FC<AgentHandoffIndicatorProps> = ({
  handoff,
  fromAgent,
  toAgent,
  showDetails = false,
  className = '',
  animated = true
}) => {
  // Format duration helper
  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date();
    const duration = Math.floor((end.getTime() - startTime.getTime()) / 1000);
    if (duration < 60) return `${duration}s`;
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}m ${seconds}s`;
  };

  // Get status icon and color
  const getStatusDisplay = (status: HandoffStatus) => {
    switch (status) {
      case 'pending':
        return {
          icon: AlertCircle,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200'
        };
      case 'in-progress':
        return {
          icon: Clock,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'failed':
        return {
          icon: XCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const statusDisplay = getStatusDisplay(handoff.status);
  const StatusIcon = statusDisplay.icon;
  const duration = formatDuration(handoff.startTime, handoff.endTime);

  if (!showDetails) {
    // Compact view - just the animated arrow between agents
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {/* From Agent */}
        {fromAgent && (
          <div className="flex items-center space-x-1">
            {fromAgent.avatar ? (
              <img 
                src={fromAgent.avatar} 
                alt={fromAgent.name}
                className="w-6 h-6 rounded-full"
              />
            ) : (
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-xs font-medium text-blue-600">
                  {fromAgent.name.charAt(0)}
                </span>
              </div>
            )}
            <span className="text-xs text-gray-600">{fromAgent.name}</span>
          </div>
        )}

        {/* Animated Arrow */}
        <div className="relative">
          <ArrowRight 
            className={`w-4 h-4 ${statusDisplay.color} ${
              animated && handoff.status === 'in-progress' ? 'animate-pulse' : ''
            }`} 
          />
          {handoff.status === 'in-progress' && animated && (
            <div className="absolute inset-0 w-4 h-4">
              <ArrowRight className="w-4 h-4 text-blue-300 animate-ping" />
            </div>
          )}
        </div>

        {/* To Agent */}
        {toAgent && (
          <div className="flex items-center space-x-1">
            {toAgent.avatar ? (
              <img 
                src={toAgent.avatar} 
                alt={toAgent.name}
                className="w-6 h-6 rounded-full"
              />
            ) : (
              <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center">
                <span className="text-xs font-medium text-purple-600">
                  {toAgent.name.charAt(0)}
                </span>
              </div>
            )}
            <span className="text-xs text-gray-600">{toAgent.name}</span>
          </div>
        )}

        {/* Status Indicator */}
        <StatusIcon className={`w-3 h-3 ${statusDisplay.color}`} />
      </div>
    );
  }

  // Detailed view
  return (
    <div className={`p-4 rounded-lg border ${statusDisplay.borderColor} ${statusDisplay.bgColor} ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* Header */}
          <div className="flex items-center space-x-2 mb-2">
            <StatusIcon className={`w-4 h-4 ${statusDisplay.color}`} />
            <span className="text-sm font-medium text-gray-900 capitalize">
              {handoff.status.replace('-', ' ')} Handoff
            </span>
            <span className="text-xs text-gray-500">
              {duration}
            </span>
          </div>

          {/* Agent Flow */}
          <div className="flex items-center space-x-3 mb-3">
            {/* From Agent */}
            <div className="flex items-center space-x-2">
              {fromAgent ? (
                <>
                  {fromAgent.avatar ? (
                    <img 
                      src={fromAgent.avatar} 
                      alt={fromAgent.name}
                      className="w-8 h-8 rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {fromAgent.name.charAt(0)}
                      </span>
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{fromAgent.name}</p>
                    <p className="text-xs text-gray-500">{fromAgent.specialization || 'Main Agent'}</p>
                  </div>
                </>
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                  <span className="text-sm text-gray-500">?</span>
                </div>
              )}
            </div>

            {/* Arrow with animation */}
            <div className="relative flex-shrink-0">
              <ArrowRight 
                className={`w-5 h-5 ${statusDisplay.color} ${
                  animated && handoff.status === 'in-progress' ? 'animate-pulse' : ''
                }`} 
              />
              {handoff.status === 'in-progress' && animated && (
                <div className="absolute inset-0 w-5 h-5">
                  <ArrowRight className="w-5 h-5 text-blue-300 animate-ping" />
                </div>
              )}
            </div>

            {/* To Agent */}
            <div className="flex items-center space-x-2">
              {toAgent ? (
                <>
                  {toAgent.avatar ? (
                    <img 
                      src={toAgent.avatar} 
                      alt={toAgent.name}
                      className="w-8 h-8 rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-purple-600">
                        {toAgent.name.charAt(0)}
                      </span>
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{toAgent.name}</p>
                    <p className="text-xs text-gray-500">{toAgent.specialization || 'Specialist'}</p>
                  </div>
                </>
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                  <span className="text-sm text-gray-500">?</span>
                </div>
              )}
            </div>
          </div>

          {/* Handoff Reason */}
          <div className="mb-2">
            <p className="text-sm text-gray-700">
              <span className="font-medium">Reason:</span> {handoff.reason}
            </p>
          </div>

          {/* Timing Information */}
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>Started {handoff.startTime.toLocaleTimeString()}</span>
            </div>
            {handoff.endTime && (
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-3 h-3" />
                <span>Completed {handoff.endTime.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};