import React, { createContext, useContext, useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Room, RoomEvent, RemoteTrack, Track, ConnectionState, Participant, RemoteParticipant } from 'livekit-client';
import { useMicVAD } from '@ricky0123/vad-react';
import {
  ParticipantInfo,
  ParticipantState,
  AllDataChannelMessages,
  isValidDataChannelMessage,
  createParticipantListUpdate,
  createSystemEventMessage,
  createVoiceActivityMessage,
  createInterruptionMessage
} from '../../types/DataChannelMessages';

// Voice configuration interface
interface VoiceConfig {
  enabled: boolean;
  provider: string;
  sttProvider: string;
  ttsProvider: string;
  voiceId: string;
}

// VAD optimization configuration
interface VADOptimizationConfig {
  speechThreshold: number;
  silenceThreshold: number;
  minSpeechDurationMs: number;
  maxSilenceDurationMs: number;
  costTrackingEnabled: boolean;
  debugLogging: boolean;
}

// Cost tracking interface
interface VoiceCostStats {
  sessionDuration: number;
  totalSttTime: number;
  sttActivations: number;
  sttEfficiency: number;
  estimatedCostSavings: number;
  vadState: string;
  audioLevel: number;
}

// Event data types for type safety
type EventData = string | number | boolean | Record<string, unknown>;

// Message interface for AG-UI events
export interface VoiceMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isComplete: boolean;
}

// Enhanced voice state interface with cost optimization
export interface VoiceOptimizedState {
  isConnecting: boolean;
  isListening: boolean;
  isSpeaking: boolean;
  connectionError: string | null;
  isAgentSpeaking: boolean;
  vadActive: boolean;
  visibleMessages: VoiceMessage[];
  
  // Enhanced participant tracking
  participants: ParticipantInfo[];
  participantStates: Record<string, ParticipantState>;
  currentUser?: ParticipantInfo;
  totalParticipants: number;
  
  // LiveKit connection state
  livekitConnectionState: ConnectionState;
  isConnected: boolean;
  
  // Inactivity timeout state
  inactivityTimeRemaining: number;
  inactivityWarningActive: boolean;
  sessionDuration: number;
  
  // VAD optimization and cost tracking
  vadOptimization: {
    enabled: boolean;
    state: string;
    audioLevel: number;
    speechDetected: boolean;
    sttSessionActive: boolean;
  };
  
  costStats: VoiceCostStats;
}

// Voice controls interface with cost optimization
export interface VoiceOptimizedControls {
  connect: () => Promise<void>;
  connectForPresence: () => Promise<void>;
  disconnect: () => Promise<void>;
  enableAudio: () => Promise<void>;
  disableAudio: () => Promise<void>;
  sendInterruption: () => void;
  
  // VAD and cost optimization controls
  enableVADOptimization: () => void;
  disableVADOptimization: () => void;
  resetCostStats: () => void;
  getCostReport: () => VoiceCostStats;
  
  // Event system for bridge communication
  on: (event: string, callback: (data: EventData) => void) => void;
  off: (event: string, callback: (data: EventData) => void) => void;
}

// Context interface
export interface VoiceOptimizedContextType {
  state: VoiceOptimizedState;
  controls: VoiceOptimizedControls;
  isVoiceActive: boolean;
}

// Create context
const VoiceOptimizedContext = createContext<VoiceOptimizedContextType | null>(null);

// Hook to use the context
export function useVoiceOptimized(): VoiceOptimizedContextType {
  const context = useContext(VoiceOptimizedContext);
  if (!context) {
    throw new Error('useVoiceOptimized must be used within a VoiceOptimizedProvider');
  }
  return context;
}

// Provider props
interface VoiceOptimizedProviderProps {
  children: React.ReactNode;
  voiceConfig: VoiceConfig;
  sessionId?: string;
  vadConfig?: Partial<VADOptimizationConfig>;
}

/**
 * VoiceOptimizedProvider - Ultra-low-cost voice service with VAD optimization
 *
 * Features:
 * - VAD-based STT lifecycle management
 * - Real-time cost tracking and optimization
 * - Advanced audio level monitoring
 * - Intelligent speech detection
 * - Cost savings reporting
 */
export function VoiceOptimizedProvider({
  children,
  voiceConfig,
  sessionId,
  vadConfig = {}
}: VoiceOptimizedProviderProps) {
  // Default VAD configuration
  const defaultVADConfig: VADOptimizationConfig = {
    speechThreshold: 0.5,
    silenceThreshold: 0.3,
    minSpeechDurationMs: 300,
    maxSilenceDurationMs: 2000,
    costTrackingEnabled: true,
    debugLogging: false,
    ...vadConfig
  };

  // LiveKit room instance
  const [room, setRoom] = useState<Room | null>(null);
  const [connectionState, setConnectionState] = useState<ConnectionState>(ConnectionState.Disconnected);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isAgentSpeaking, setIsAgentSpeaking] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // VAD optimization state
  const [vadOptimizationEnabled, setVADOptimizationEnabled] = useState(true);
  const [vadState, setVADState] = useState('idle');
  const [currentAudioLevel, setCurrentAudioLevel] = useState(0);
  const [speechDetected, setSpeechDetected] = useState(false);
  const [sttSessionActive, setSTTSessionActive] = useState(false);

  // Cost tracking state
  const [costStats, setCostStats] = useState<VoiceCostStats>({
    sessionDuration: 0,
    totalSttTime: 0,
    sttActivations: 0,
    sttEfficiency: 0,
    estimatedCostSavings: 0,
    vadState: 'idle',
    audioLevel: 0
  });

  // Session tracking
  const sessionStartTime = useRef<number>(Date.now());
  const lastSttStartTime = useRef<number>(0);
  const totalSttTime = useRef<number>(0);
  const sttActivationCount = useRef<number>(0);

  // Message state management
  const [visibleMessages, setVisibleMessages] = useState<VoiceMessage[]>([]);
  const [activeMessages, setActiveMessages] = useState<Map<string, VoiceMessage>>(new Map());

  // Enhanced participant tracking state
  const [participants, setParticipants] = useState<ParticipantInfo[]>([]);
  const [participantStates, setParticipantStates] = useState<Record<string, ParticipantState>>({});
  const [currentUser, setCurrentUser] = useState<ParticipantInfo | undefined>();

  // Inactivity timeout state
  const [inactivityTimeRemaining, setInactivityTimeRemaining] = useState<number>(90);
  const [inactivityWarningActive, setInactivityWarningActive] = useState<boolean>(false);
  const [sessionDuration, setSessionDuration] = useState<number>(0);

  // Event system for bridge communication
  const eventListeners = useRef<Map<string, Set<(data: EventData) => void>>>(new Map());

  // Enhanced VAD with cost optimization
  const vad = useMicVAD({
    startOnLoad: false,
    onSpeechStart: () => {
      console.log('[VoiceOptimized] 🎤 VAD: User started speaking');
      setSpeechDetected(true);
      
      if (vadOptimizationEnabled) {
        // Start STT session if not already active
        if (!sttSessionActive) {
          setSTTSessionActive(true);
          lastSttStartTime.current = Date.now();
          sttActivationCount.current += 1;
          
          console.log(`[VoiceOptimized] 🎯 STT Session Started (Activation #${sttActivationCount.current})`);
          emitEvent('stt_session_start', {
            timestamp: Date.now(),
            activationCount: sttActivationCount.current
          });
        }
      }
      
      emitEvent('user_speech_start', true);
      
      // Send interruption signal to backend agent
      if (room && isAgentSpeaking) {
        sendInterruption();
      }
    },
    onSpeechEnd: () => {
      console.log('[VoiceOptimized] 🎤 VAD: User stopped speaking');
      setSpeechDetected(false);
      
      if (vadOptimizationEnabled) {
        // Schedule STT session stop with delay
        setTimeout(() => {
          if (!speechDetected && sttSessionActive) {
            setSTTSessionActive(false);
            
            const sessionDuration = (Date.now() - lastSttStartTime.current) / 1000;
            totalSttTime.current += sessionDuration;
            
            console.log(`[VoiceOptimized] 🛑 STT Session Stopped (Duration: ${sessionDuration.toFixed(2)}s)`);
            emitEvent('stt_session_stop', {
              timestamp: Date.now(),
              sessionDuration,
              totalSttTime: totalSttTime.current
            });
            
            updateCostStats();
          }
        }, defaultVADConfig.maxSilenceDurationMs);
      }
      
      emitEvent('user_speech_end', true);
    },
    onVADMisfire: () => {
      console.log('[VoiceOptimized] VAD: Misfire detected');
    },
    // Enhanced VAD configuration for cost optimization
    workletURL: '/vad.worklet.bundle.min.js',
    modelURL: '/silero_vad.onnx',
    frameSamples: 512,
    positiveSpeechThreshold: defaultVADConfig.speechThreshold,
    negativeSpeechThreshold: defaultVADConfig.silenceThreshold,
    redemptionFrames: 8,
    preSpeechPadFrames: 1,
    minSpeechFrames: Math.ceil(defaultVADConfig.minSpeechDurationMs / 32), // Convert ms to frames
    submitUserSpeechOnPause: true,
    // Enhanced ORT config
    ortConfig: (ort: { env?: { logLevel?: string; debug?: boolean } }) => {
      if (ort && ort.env) {
        ort.env.logLevel = defaultVADConfig.debugLogging ? 'info' : 'error';
        ort.env.debug = defaultVADConfig.debugLogging;
      }
      
      return {
        executionProviders: ['wasm'],
        graphOptimizationLevel: 'all',
        executionMode: 'sequential',
        enableProfiling: false,
        enableMemPattern: true,
        enableCpuMemArena: true,
        logSeverityLevel: defaultVADConfig.debugLogging ? 1 : 3,
        logVerbosityLevel: 0
      };
    }
  });

  // Update cost statistics
  const updateCostStats = useCallback(() => {
    const currentTime = Date.now();
    const sessionDurationSeconds = (currentTime - sessionStartTime.current) / 1000;
    const efficiency = sessionDurationSeconds > 0 ? (totalSttTime.current / sessionDurationSeconds * 100) : 0;
    const costSavings = Math.max(0, 100 - efficiency);

    setCostStats({
      sessionDuration: sessionDurationSeconds,
      totalSttTime: totalSttTime.current,
      sttActivations: sttActivationCount.current,
      sttEfficiency: efficiency,
      estimatedCostSavings: costSavings,
      vadState,
      audioLevel: currentAudioLevel
    });
  }, [vadState, currentAudioLevel]);

  // Update cost stats periodically
  useEffect(() => {
    if (!defaultVADConfig.costTrackingEnabled) return;

    const interval = setInterval(updateCostStats, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [updateCostStats, defaultVADConfig.costTrackingEnabled]);

  // Event system implementation
  const emitEvent = useCallback((event: string, data: EventData) => {
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[VoiceOptimized] Error in event listener for ${event}:`, error);
        }
      });
    }
  }, []);

  const addEventListener = useCallback((event: string, callback: (data: EventData) => void) => {
    if (!eventListeners.current.has(event)) {
      eventListeners.current.set(event, new Set());
    }
    eventListeners.current.get(event)!.add(callback);
  }, []);

  const removeEventListener = useCallback((event: string, callback: (data: EventData) => void) => {
    const listeners = eventListeners.current.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        eventListeners.current.delete(event);
      }
    }
  }, []);

  // VAD optimization controls
  const enableVADOptimization = useCallback(() => {
    setVADOptimizationEnabled(true);
    console.log('[VoiceOptimized] VAD optimization enabled');
  }, []);

  const disableVADOptimization = useCallback(() => {
    setVADOptimizationEnabled(false);
    console.log('[VoiceOptimized] VAD optimization disabled');
  }, []);

  const resetCostStats = useCallback(() => {
    sessionStartTime.current = Date.now();
    totalSttTime.current = 0;
    sttActivationCount.current = 0;
    updateCostStats();
    console.log('[VoiceOptimized] Cost statistics reset');
  }, [updateCostStats]);

  const getCostReport = useCallback((): VoiceCostStats => {
    updateCostStats();
    return costStats;
  }, [costStats, updateCostStats]);

  // Send interruption signal via data channel
  const sendInterruption = useCallback(() => {
    if (!room || !sessionId) {
      console.warn('[VoiceOptimized] Cannot send interruption - not connected to room or no session ID');
      return;
    }

    try {
      const interruptionMessage = createInterruptionMessage(
        sessionId,
        room.localParticipant.identity,
        'User speech detected (VAD-optimized)'
      );
      
      room.localParticipant.publishData(
        new TextEncoder().encode(JSON.stringify(interruptionMessage)),
        { reliable: true }
      );
      
      console.log('[VoiceOptimized] VAD-optimized interruption signal sent:', interruptionMessage);
    } catch (error) {
      console.error('[VoiceOptimized] Failed to send interruption signal:', error);
    }
  }, [room, sessionId]);

  // Connect to LiveKit room (reusing existing logic with VAD enhancements)
  const connect = useCallback(async () => {
    // Implementation similar to UnifiedVoiceContext but with VAD optimization
    console.log('[VoiceOptimized] Connecting with VAD optimization enabled...');
    
    // Reset cost tracking on new connection
    resetCostStats();
    
    // TODO: Implement full connection logic similar to UnifiedVoiceContext
    // but with enhanced VAD callbacks and cost tracking
    
  }, [resetCostStats]);

  // Other control methods (simplified for brevity)
  const connectForPresence = useCallback(async () => {
    console.log('[VoiceOptimized] Connecting for presence with cost optimization...');
    await connect();
  }, [connect]);

  const disconnect = useCallback(async () => {
    console.log('[VoiceOptimized] Disconnecting and generating cost report...');
    
    // Generate final cost report
    const finalReport = getCostReport();
    console.log('[VoiceOptimized] Final Cost Report:', finalReport);
    
    // TODO: Implement full disconnect logic
  }, [getCostReport]);

  const enableAudio = useCallback(async () => {
    console.log('[VoiceOptimized] Enabling audio with VAD optimization...');
    // TODO: Implement audio enabling with VAD
  }, []);

  const disableAudio = useCallback(async () => {
    console.log('[VoiceOptimized] Disabling audio and stopping STT sessions...');
    if (sttSessionActive) {
      setSTTSessionActive(false);
      updateCostStats();
    }
    // TODO: Implement audio disabling
  }, [sttSessionActive, updateCostStats]);

  // Map state to optimized interface
  const state: VoiceOptimizedState = useMemo(() => ({
    isConnecting,
    isListening: vad.listening && connectionState === ConnectionState.Connected,
    isSpeaking: vad.userSpeaking,
    connectionError,
    isAgentSpeaking,
    vadActive: vad.listening,
    visibleMessages,
    participants,
    participantStates,
    currentUser,
    totalParticipants: participants.length,
    livekitConnectionState: connectionState,
    isConnected: connectionState === ConnectionState.Connected,
    inactivityTimeRemaining,
    inactivityWarningActive,
    sessionDuration,
    vadOptimization: {
      enabled: vadOptimizationEnabled,
      state: vadState,
      audioLevel: currentAudioLevel,
      speechDetected,
      sttSessionActive
    },
    costStats
  }), [
    isConnecting,
    connectionState,
    vad.listening,
    vad.userSpeaking,
    connectionError,
    isAgentSpeaking,
    visibleMessages,
    participants,
    participantStates,
    currentUser,
    inactivityTimeRemaining,
    inactivityWarningActive,
    sessionDuration,
    vadOptimizationEnabled,
    vadState,
    currentAudioLevel,
    speechDetected,
    sttSessionActive,
    costStats
  ]);

  // Map controls to optimized interface
  const controls: VoiceOptimizedControls = useMemo(() => ({
    connect,
    connectForPresence,
    disconnect,
    enableAudio,
    disableAudio,
    sendInterruption,
    enableVADOptimization,
    disableVADOptimization,
    resetCostStats,
    getCostReport,
    on: addEventListener,
    off: removeEventListener
  }), [
    connect,
    connectForPresence,
    disconnect,
    enableAudio,
    disableAudio,
    sendInterruption,
    enableVADOptimization,
    disableVADOptimization,
    resetCostStats,
    getCostReport,
    addEventListener,
    removeEventListener
  ]);

  // Track voice active state
  const isVoiceActive = connectionState === ConnectionState.Connected && voiceConfig.enabled;

  // Context value
  const contextValue: VoiceOptimizedContextType = useMemo(() => ({
    state,
    controls,
    isVoiceActive
  }), [state, controls, isVoiceActive]);

  return (
    <VoiceOptimizedContext.Provider value={contextValue}>
      <div data-testid="voice-optimized-provider">
        {children}
      </div>
    </VoiceOptimizedContext.Provider>
  );
}