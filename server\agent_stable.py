#!/usr/bin/env python3
"""
Stable Voice AI Agent for Rep Room v7.1 - FIXED VERSION
Implements proper STT/TTS handling with timeout mechanisms and circuit breakers
"""

import asyncio
import os
import logging
import time
import json
import uuid
import re
from typing import Optional, Dict, Any

# Suppress ONNX Runtime verbose logging to prevent log spam
os.environ['ORT_LOG_LEVEL'] = '3'  # Only show errors
os.environ['ONNXRUNTIME_LOG_LEVEL'] = '3'  # Only show errors
from livekit.agents import Agent, JobContext, cli
from livekit.plugins.deepgram import STT
from livekit.plugins.elevenlabs import TTS
from livekit import rtc
from flask import Flask, jsonify, request
import threading

# Import OpenAI for LLM interaction (fallback)
try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError as e:
    logger.warning(f"OpenAI not available: {e}")
    OPENAI_AVAILABLE = False

# Import LiveKit LLM interface for CopilotKit integration
try:
    from livekit.agents import llm
    LIVEKIT_LLM_AVAILABLE = True
except ImportError as e:
    logger.warning(f"LiveKit LLM interface not available: {e}")
    LIVEKIT_LLM_AVAILABLE = False

# Configure logging with enhanced debugging - FIXED: Remove emoji characters for Windows compatibility
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import HTTP client for Mastra agent integration
try:
    import aiohttp
    import asyncpg
    HTTP_CLIENT_AVAILABLE = True
except ImportError as e:
    logger.warning(f"HTTP client or PostgreSQL not available: {e}")
    HTTP_CLIENT_AVAILABLE = False

# STT Event Debugging Counters
class STTEventCounters:
    """Track STT event statistics for debugging."""
    def __init__(self):
        self.total_events = 0
        self.interim_count = 0
        self.final_count = 0
        self.timeout_fallback_count = 0
        self.unknown_events = 0
        self.speech_start_count = 0
        self.speech_end_count = 0
        self.recognition_usage_count = 0
        self.start_time = time.time()
    
    def log_stats(self):
        """Log current statistics."""
        elapsed = time.time() - self.start_time
        logger.info(f"[CHART] STT Event Statistics (after {elapsed:.1f}s):")
        logger.info(f"   Total Events: {self.total_events}")
        logger.info(f"   Interim Transcripts: {self.interim_count}")
        logger.info(f"   Final Transcripts: {self.final_count}")
        logger.info(f"   Timeout Fallbacks: {self.timeout_fallback_count}")
        logger.info(f"   Speech Start Events: {self.speech_start_count}")
        logger.info(f"   Speech End Events: {self.speech_end_count}")
        logger.info(f"   Recognition Usage Events: {self.recognition_usage_count}")
        logger.info(f"   Unknown Events: {self.unknown_events}")

# Global STT event counter
stt_counters = STTEventCounters()

# Import Deepgram event types if available
try:
    from livekit.plugins.deepgram import SpeechEventType
    DEEPGRAM_EVENTS_AVAILABLE = True
except ImportError:
    logger.warning("Deepgram SpeechEventType not available - using string matching")
    DEEPGRAM_EVENTS_AVAILABLE = False

# Import OpenAI plugins with fallback handling
try:
    from livekit.plugins.openai import TTS as OpenAITTS, STT as OpenAISTT
    OPENAI_AVAILABLE = True
except ImportError as e:
    logger.warning(f"OpenAI plugins not available: {e}")
    OpenAITTS = None
    OpenAISTT = None
    OPENAI_AVAILABLE = False

# --- REP-106 Phase 3: Room Discovery and Session Management ---

class RepRoomSessionManager:
    """Manages Rep Room session discovery and coordination for REP-106 Phase 3."""
    
    def __init__(self):
        self.active_sessions = {}  # session_id -> session_info
        self.room_pattern = re.compile(r'^rrs-([^-]+)-(.+)$')  # rrs-{slug}-{sessionId}
        self.logger = logging.getLogger(f"{__name__}.SessionManager")
        
    def parse_room_name(self, room_name: str) -> Optional[Dict[str, str]]:
        """Parse room name to extract slug and session ID.
        
        Expected format: rrs-{slug}-{sessionId}
        Returns: {'slug': str, 'session_id': str} or None
        """
        try:
            match = self.room_pattern.match(room_name)
            if match:
                slug, session_id = match.groups()
                self.logger.info(f"[REP-106] Parsed room name '{room_name}': slug='{slug}', session_id='{session_id}'")
                return {
                    'slug': slug,
                    'session_id': session_id,
                    'room_name': room_name
                }
            else:
                self.logger.debug(f"[REP-106] Room name '{room_name}' does not match rrs-* pattern")
                return None
        except Exception as e:
            self.logger.error(f"[REP-106] Error parsing room name '{room_name}': {e}")
            return None
    
    def should_join_room(self, room_name: str) -> bool:
        """Determine if agent should join this room based on naming pattern."""
        return self.parse_room_name(room_name) is not None
    
    def register_session(self, session_info: Dict[str, Any]):
        """Register an active session."""
        session_id = session_info.get('session_id')
        if session_id:
            self.active_sessions[session_id] = session_info
            self.logger.info(f"[REP-106] Registered session: {session_id}")
    
    def unregister_session(self, session_id: str):
        """Unregister a session."""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            self.logger.info(f"[REP-106] Unregistered session: {session_id}")
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information."""
        return self.active_sessions.get(session_id)

# Global session manager
session_manager = RepRoomSessionManager()

# --- Health Check and Webhook Server ---
app = Flask(__name__)

@app.route('/health')
def health_check():
    """Health check endpoint for Fly.io monitoring."""
    return jsonify({
        "status": "healthy",
        "service": "stable-voice-agent",
        "version": "v7.1-stable-rep106",
        "active_sessions": len(session_manager.active_sessions)
    })

@app.route('/webhook/livekit', methods=['POST'])
def livekit_webhook_handler():
    """Handle LiveKit webhook events for REP-106 Phase 3 coordination."""
    try:
        # Get webhook payload
        payload = request.get_json()
        if not payload:
            logger.warning("[REP-106] Webhook received with no payload")
            return jsonify({"error": "No payload"}), 400
        
        event_type = payload.get('event')
        room_name = payload.get('room', {}).get('name', '')
        
        logger.info(f"[REP-106] Webhook received: event='{event_type}', room='{room_name}'")
        
        # Check if this is a rep room session
        session_info = session_manager.parse_room_name(room_name)
        if not session_info:
            logger.debug(f"[REP-106] Ignoring webhook for non-rep-room: {room_name}")
            return jsonify({"status": "ignored"}), 200
        
        # Handle different event types
        if event_type == 'room_started':
            logger.info(f"[REP-106] Room started event for session: {session_info['session_id']}")
            # Extract additional metadata from room
            room_metadata = payload.get('room', {}).get('metadata', {})
            session_info.update({
                'room_metadata': room_metadata,
                'status': 'active',
                'started_at': time.time()
            })
            session_manager.register_session(session_info)
            
        elif event_type == 'participant_joined':
            participant = payload.get('participant', {})
            participant_identity = participant.get('identity', '')
            logger.info(f"[REP-106] Participant joined: {participant_identity} in session {session_info['session_id']}")
            
            # Update session with participant info
            existing_session = session_manager.get_session_info(session_info['session_id'])
            if existing_session:
                if 'participants' not in existing_session:
                    existing_session['participants'] = []
                existing_session['participants'].append({
                    'identity': participant_identity,
                    'joined_at': time.time()
                })
        
        elif event_type == 'room_finished':
            logger.info(f"[REP-106] Room finished event for session: {session_info['session_id']}")
            session_manager.unregister_session(session_info['session_id'])
        
        return jsonify({"status": "processed"}), 200
        
    except Exception as e:
        logger.error(f"[REP-106] Webhook handler error: {e}")
        return jsonify({"error": str(e)}), 500

def start_health_server():
    """Start the health check and webhook server in a separate thread."""
    app.run(host='0.0.0.0', port=8080, debug=False)

# --- Circuit Breaker Pattern ---
class CircuitBreaker:
    """Circuit breaker to prevent endless loops and cascading failures."""
    
    def __init__(self, failure_threshold=5, recovery_timeout=30):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN - too many failures")
        
        try:
            result = func(*args, **kwargs)
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                logger.info("Circuit breaker CLOSED - service recovered")
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                logger.error(f"Circuit breaker OPEN - {self.failure_count} failures")
            
            raise e

# Global circuit breakers
stt_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=20)
tts_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=20)
llm_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=20)

def reset_tts_circuit_breaker():
    """Reset TTS circuit breaker to allow recovery."""
    global tts_circuit_breaker
    tts_circuit_breaker.state = 'CLOSED'
    tts_circuit_breaker.failure_count = 0
    tts_circuit_breaker.last_failure_time = None
    logger.info("[LOUD_SOUND] TTS circuit breaker has been reset to CLOSED state")

# --- CopilotKit Mastra Agent Integration Classes ---

class CopilotKitMastraAgentClient:
    """CopilotKit/AG-UI compliant Mastra Agent client for REP-105 integration"""
    
    def __init__(self, agent_config: dict, context: dict):
        # CopilotKit Client Initialization
        logger.debug("CopilotKit client initialization")
        
        # Log received parameters
        logger.debug(f"Agent Config keys: {list(agent_config.keys()) if agent_config else 'None'}")
        logger.debug(f"Context keys: {list(context.keys()) if context else 'None'}")
        
        self.agent_config = agent_config
        self.context = context
        self.conversation_history = []
        
        # Supabase edge function endpoint
        self.edge_function_url = os.getenv('SUPABASE_EDGE_FUNCTION_URL',
            'https://kjkehonxatogcwrybslr.supabase.co/functions/v1/copilotkit-runtime-handler')
        
        # Required for edge function authentication
        self.supabase_anon_key = os.getenv('SUPABASE_ANON_KEY')
        self.user_jwt_token = context.get('userJwtToken')
        
        # Log Supabase configuration
        logger.debug(f"Edge Function URL: {self.edge_function_url}")
        logger.debug(f"Supabase Anon Key available: {'Yes' if self.supabase_anon_key else 'No'}")
        logger.debug(f"User JWT Token available: {'Yes' if self.user_jwt_token else 'No'}")
        
        # Log context parameters for edge function
        logger.debug("Context Parameters for Edge Function prepared")
        
        # Log agent configuration details
        if not agent_config:
            logger.warning("No agent configuration provided")
        
        logger.info(f"REP-105 Initialized CopilotKit client for edge function: {self.edge_function_url}")

    async def process_message(self, user_message: str) -> str:
        """Process user message through deployed Supabase edge function"""
        try:
            # Message Processing
            logger.debug("CopilotKit message processing")
            
            logger.debug(f"User Message: '{user_message}' (length: {len(user_message)})")
            
            # Prepare headers for edge function
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream, application/json',
                'Authorization': f'Bearer {self.user_jwt_token}',
                'apikey': self.supabase_anon_key,
                'X-Rep-Room-ID': self.context.get('repRoomId'),
                'X-User-ID': self.context.get('userId'),
                'X-Tenant-ID': self.context.get('tenantId')
            }
            
            # Log header construction (debug only)
            logger.debug("Edge Function Headers prepared")
            
            # Prepare payload for edge function
            conversation_history = self._format_conversation_history()
            current_message = {
                'role': 'user',
                'content': user_message,
                'timestamp': self._get_current_timestamp()
            }
            payload = {
                'messages': conversation_history + [current_message],
                'threadId': self.context.get('threadId'),
                'customParameters': self.agent_config.get('customParameters', {}),
                'agentConfig': {
                    'agentId': self.context.get('agentId') or self.agent_config.get('id'),
                    'mastraApiBaseUrl': self.context.get('mastraApiBaseUrl') or self.agent_config.get('mastra_api_base_url'),
                    'mastraAgentId': self.context.get('mastraAgentId') or self.agent_config.get('mastra_agent_id'),
                    'agentName': self.context.get('agentName') or self.agent_config.get('name'),
                    'agentType': self.context.get('agentType') or self.agent_config.get('type')
                },
                'context': {
                    'repRoomId': self.context.get('repRoomId'),
                    'userId': self.context.get('userId'),
                    'tenantId': self.context.get('tenantId'),
                    'agentCloneId': self.context.get('agentCloneId')
                }
            }
            
            # DIAGNOSTIC: Log payload construction
            logger.info("[DIAGNOSTIC] Edge Function Payload Analysis:")
            logger.info(f"[DIAGNOSTIC]    - Conversation History Length: {len(conversation_history)}")
            logger.info(f"[DIAGNOSTIC]    - Thread ID: {self.context.get('threadId')}")
            logger.info(f"[DIAGNOSTIC]    - Custom Parameters: {self.agent_config.get('customParameters', {})}")
            logger.info(f"[DIAGNOSTIC]    - Total Messages in Payload: {len(payload['messages'])}")
            
            # Log the complete message chain for debugging
            logger.info("[DIAGNOSTIC] Complete Message Chain:")
            for i, msg in enumerate(payload['messages']):
                logger.info(f"[DIAGNOSTIC]    - Message {i+1}: {msg['role']} - '{msg['content'][:100]}...' ({msg.get('timestamp', 'No timestamp')})")
            
            logger.info(f"[REP-105] Sending request to edge function")
            logger.debug(f"[REP-105] Headers: {headers}")
            logger.debug(f"[REP-105] Payload keys: {list(payload.keys()) if isinstance(payload, dict) else 'Non-dict payload'}")
            
            # Send request to deployed edge function
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.edge_function_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"REP-105 Edge function request failed: {response.status} - {error_text}")
                        return "I'm sorry, I'm having trouble processing your request right now."
                    
                    # Handle Server-Sent Events response
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' in content_type:
                        agent_response = await self._handle_sse_response(response)
                    else:
                        # Handle JSON response
                        response_data = await response.json()
                        agent_response = response_data.get('content', 'No response from agent')
                    
                    # Update conversation history
                    self._update_conversation_history(user_message, agent_response)
                    
                    logger.info(f"REP-105 Agent response received: {len(agent_response)} characters")
                    return agent_response
                    
        except Exception as e:
            logger.error(f"REP-105 Error communicating with edge function: {str(e)}")
            return "I apologize, but I'm experiencing technical difficulties. Please try again."

    async def _handle_sse_response(self, response) -> str:
        """Handle Server-Sent Events streaming response from edge function"""
        accumulated_content = ""
        
        async for line in response.content:
            line = line.decode('utf-8').strip()
            
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])  # Remove 'data: ' prefix
                    
                    if data.get('type') == 'textDelta':
                        accumulated_content += data.get('textDelta', '')
                    elif data.get('type') == 'done':
                        break
                    elif data.get('type') == 'error':
                        logger.error(f"REP-105 SSE error: {data.get('error')}")
                        break
                        
                except json.JSONDecodeError:
                    continue
        
        return accumulated_content

    def _format_conversation_history(self) -> list:
        """Format conversation history for edge function"""
        return [
            {
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg.get("timestamp", self._get_current_timestamp())
            }
            for msg in self.conversation_history[-10:]  # Keep last 10 messages
        ]
    
    def _update_conversation_history(self, user_message: str, agent_response: str):
        """Update conversation history"""
        timestamp = self._get_current_timestamp()
        
        self.conversation_history.extend([
            {"role": "user", "content": user_message, "timestamp": timestamp},
            {"role": "assistant", "content": agent_response, "timestamp": timestamp}
        ])
        
        # Keep history manageable (last 20 messages)
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'


class CopilotKitMastraLLM(llm.LLM):
    """Enhanced LLM that routes requests through deployed edge function"""
    
    def __init__(self, copilotkit_client: CopilotKitMastraAgentClient):
        super().__init__()
        self.copilotkit_client = copilotkit_client
    
    async def chat(
        self,
        chat_ctx: llm.ChatContext,
        **kwargs
    ) -> llm.LLMStream:
        """Chat method required by LLM interface - routes through edge function"""
        
        # Extract user message from chat context
        user_message = ""
        for msg in chat_ctx.messages:
            if msg.role == "user":
                user_message = msg.content
        
        if not user_message:
            user_message = "Hello"
        
        # Process through edge function
        try:
            response = await self.copilotkit_client.process_message(user_message)
            
            # Create LLM stream response (same pattern as agenerate method)
            stream = llm.LLMStream()
            stream._content = response
            return stream
            
        except Exception as e:
            logger.error(f"REP-105 Edge function chat error: {str(e)}")
            # Return fallback response
            stream = llm.LLMStream()
            stream._content = "I apologize, but I'm experiencing technical difficulties."
            return stream
    
    async def agenerate(
        self,
        chat_ctx: llm.ChatContext,
        **kwargs
    ) -> llm.LLMStream:
        """Generate response using deployed edge function"""
        
        # Extract user message from chat context
        user_message = ""
        for msg in chat_ctx.messages:
            if msg.role == "user":
                user_message = msg.content
        
        if not user_message:
            user_message = "Hello"
        
        # Process through edge function
        try:
            response = await self.copilotkit_client.process_message(user_message)
            
            # Create LLM stream response
            stream = llm.LLMStream()
            stream._content = response
            return stream
            
        except Exception as e:
            logger.error(f"REP-105 Edge function LLM generation error: {str(e)}")
            # Return fallback response
            stream = llm.LLMStream()
            stream._content = "I apologize, but I'm experiencing technical difficulties."
            return stream

def get_tts_provider():
    """Get TTS provider with fallback support."""
    # Try ElevenLabs first
    eleven_api_key = os.getenv("ELEVEN_API_KEY")
    if eleven_api_key:
        try:
            logger.info("Using ElevenLabs TTS")
            return TTS(
                model="eleven_flash_v2_5",
                voice_id=os.getenv("ELEVEN_VOICE_ID", "EXAVITQu4vr4xnSDxMaL"),
                streaming_latency=1,
                chunk_length_schedule=[50, 80, 120, 200]
            )
        except Exception as e:
            logger.warning(f"Failed to initialize ElevenLabs TTS: {e}")
    
    # Fallback to OpenAI TTS if available
    if OPENAI_AVAILABLE and OpenAITTS:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if openai_api_key:
            try:
                logger.info("Using OpenAI TTS as fallback")
                return OpenAITTS(
                    model="tts-1",
                    voice="alloy"
                )
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI TTS: {e}")
    
    logger.error("No TTS provider available - missing API keys or plugins")
    return None

def get_stt_provider():
    """Get STT provider with fallback support."""
    # Try Deepgram first
    deepgram_api_key = os.getenv("DEEPGRAM_API_KEY")
    if deepgram_api_key:
        try:
            logger.info("Using Deepgram STT")
            
            # CRITICAL FIX: The issue is that Deepgram is configured correctly but may have API issues
            # Let's add enhanced debugging and try a more reliable configuration
            logger.info("Using enhanced Deepgram STT configuration")
            
            # Log API key status (masked)
            api_key_masked = deepgram_api_key[:8] + "..." + deepgram_api_key[-4:] if len(deepgram_api_key) > 12 else "***"
            logger.debug(f"Deepgram API Key: {api_key_masked}")
            
            # CRITICAL FIX: Use only supported Deepgram STT parameters
            # Removed unsupported parameters: diarize, multichannel, alternatives, redact, ner, search, replace, measurements
            stt_provider = STT(
                model="nova-2",           # Use stable nova-2 model
                language="en-US",         # Use full language code for better compatibility
                interim_results=True,     # Enable interim results - CRITICAL for transcript generation
                smart_format=False,       # Disable smart formatting to avoid processing delays
                punctuate=False,          # Disable punctuation to avoid processing delays
                filler_words=True,        # Enable filler words for more complete transcription
                profanity_filter=False,   # Disable profanity filter for faster processing
                numerals=False           # Disable numeral conversion for faster processing
            )
            
            logger.info("Enhanced Deepgram STT provider initialized successfully")
            return stt_provider
            
        except Exception as e:
            logger.error(f"Failed to initialize Deepgram STT: {e}")
            logger.debug(f"Exception type: {type(e).__name__}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
    
    # Fallback to OpenAI STT if available
    if OPENAI_AVAILABLE and OpenAISTT:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if openai_api_key:
            try:
                logger.info("Using OpenAI STT as fallback")
                return OpenAISTT(
                    model="whisper-1",
                    timeout=30.0  # Add timeout parameter
                )
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI STT: {e}")
    
    logger.error("No STT provider available - missing API keys or plugins")
    return None

def get_llm_client():
    """Get OpenAI LLM client for chat completions."""
    if not OPENAI_AVAILABLE:
        logger.error("OpenAI not available for LLM interactions")
        return None
    
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        logger.error("OPENAI_API_KEY not found in environment")
        return None
    
    try:
        client = OpenAI(api_key=openai_api_key)
        logger.info("OpenAI LLM client initialized")
        return client
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI LLM client: {e}")
        return None

def debug_stt_event(event, event_number: int = 0):
    """Minimal STT event debugging function."""
    try:
        # Only log essential information for debugging
        has_type = hasattr(event, 'type')
        has_is_final = hasattr(event, 'is_final')
        has_alternatives = hasattr(event, 'alternatives')
        
        event_attrs = {
            'has_type': has_type,
            'has_is_final': has_is_final,
            'has_alternatives': has_alternatives
        }
        
        if has_type:
            event_attrs['type'] = str(getattr(event, 'type', None))
        
        if has_is_final:
            event_attrs['is_final'] = getattr(event, 'is_final', None)
        
        if has_alternatives:
            alternatives = getattr(event, 'alternatives', None)
            if alternatives and len(alternatives) > 0 and hasattr(alternatives[0], 'text'):
                text = alternatives[0].text
                event_attrs['text_length'] = len(text)
        
        return event_attrs
        
    except Exception as e:
        logger.error(f"Error in STT event debugging: {e}")
        return {}

async def process_final_transcript(final_text: str, ctx: JobContext, enhanced_llm, llm_client, audio_source: rtc.AudioSource, tts_provider):
    """Process a final transcript through the enhanced LLM pipeline."""
    try:
        logger.info(f"Processing final transcript: '{final_text}' (length: {len(final_text)})")
        
        # REP-106 Phase 3: Send user transcript via AG-UI data channel with session info
        try:
            session_info = session_manager.parse_room_name(ctx.room.name)
            session_id = session_info['session_id'] if session_info else None
            await send_ag_ui_data_channel_events(
                ctx, "user_transcript", final_text, "user", session_id
            )
        except Exception as e:
            logger.error(f"Failed to send user transcript via data channel: {e}")
        
        # Generate LLM response using enhanced CopilotKit LLM or fallback
        response_text = None
        
        # LLM routing decision
        logger.debug("LLM routing decision analysis")
        
        # Try enhanced LLM first (CopilotKit Mastra integration)
        if enhanced_llm:
            logger.debug("Attempting CopilotKit Mastra LLM (Primary Route)")
            
            try:
                logger.info(f"REP-105 Sending to CopilotKit Mastra LLM: '{final_text}'")
                
                # Create chat context for enhanced LLM
                if LIVEKIT_LLM_AVAILABLE:
                    chat_ctx = llm.ChatContext().append(
                        role="user",
                        text=final_text
                    )
                    
                    llm_stream = await enhanced_llm.agenerate(chat_ctx)
                    response_text = llm_stream._content
                    
                    if response_text:
                        logger.info(f"REP-105 CopilotKit Mastra LLM response received: '{response_text}'")
                    else:
                        logger.warning(f"CopilotKit Mastra LLM returned empty response")
                else:
                    logger.warning(f"LiveKit LLM interface not available")
                    logger.error(f"REP-105 LIVEKIT_LLM_AVAILABLE is False - this is why CopilotKit LLM is failing!")
                    
            except Exception as e:
                logger.error(f"REP-105 CopilotKit Mastra LLM response generation failed: {e}")
                import traceback
                logger.debug(f"REP-105 Full traceback: {traceback.format_exc()}")
        else:
            logger.error(f"REP-105 Enhanced LLM is None - CopilotKit initialization failed!")
        
        # Fallback to OpenAI LLM if enhanced LLM fails
        if not response_text and llm_client:
            try:
                logger.info(f"Falling back to OpenAI LLM: '{final_text}'")
                response_text = await generate_llm_response(final_text, llm_client)
                if response_text:
                    logger.info(f"OpenAI LLM response received: '{response_text}'")
                else:
                    logger.warning(f"OpenAI LLM returned empty response")
            except Exception as e:
                logger.error(f"OpenAI LLM response generation failed: {e}")
        
        # Final fallback response if all LLMs fail
        if not response_text:
            response_text = f"I heard you say: {final_text}. How can I help you with that?"
            logger.info(f"Using final fallback response: {response_text}")
        
        # REP-106 Phase 3: Send agent response via AG-UI data channel with session info
        try:
            session_info = session_manager.parse_room_name(ctx.room.name)
            session_id = session_info['session_id'] if session_info else None
            await send_ag_ui_data_channel_events(
                ctx, "agent_response", response_text, "assistant", session_id
            )
        except Exception as e:
            logger.error(f"Failed to send agent response via data channel: {e}")
        
        # Convert response to speech with circuit breaker protection
        try:
            await asyncio.wait_for(
                generate_and_play_tts(response_text, audio_source, tts_provider),
                timeout=10.0  # 10 second timeout for TTS
            )
            
            # Log TTS audio publication
            await log_tts_audio_publication(audio_source, response_text)
            
            logger.info("Response sent to user")
        except asyncio.TimeoutError:
            logger.error("TTS generation timed out")
        except Exception as tts_error:
            logger.error(f"TTS generation failed: {tts_error}")
            
    except Exception as e:
        logger.error(f"Failed to process final transcript: {e}")

async def get_rep_room_by_slug(slug: str) -> Optional[dict]:
    """Fetch rep room configuration from database using slug."""
    if not HTTP_CLIENT_AVAILABLE:
        logger.error("[DINGBAT] Database client not available")
        return None
    
    try:
        # Get database connection string from environment
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            logger.error("[DINGBAT] DATABASE_URL not found in environment")
            return None
        
        # Connect to database and fetch rep room by slug
        conn = await asyncpg.connect(database_url)
        try:
            query = """
            SELECT
                rr.id as rep_room_id,
                rr.public_slug,
                rr.title,
                uac.id as clone_id,
                uac.name as clone_name,
                a.id as agent_id,
                a.name as agent_name,
                a.mastra_agent_id,
                a.mastra_api_base_url
            FROM rep_rooms rr
            JOIN user_agent_clones uac ON rr.user_agent_clone_id = uac.id
            JOIN agents a ON uac.parent_agent_id = a.id
            WHERE rr.public_slug = $1
            """
            
            result = await conn.fetchrow(query, slug)
            if result:
                config = {
                    "rep_room_id": result["rep_room_id"],
                    "public_slug": result["public_slug"],
                    "title": result["title"],
                    "clone_id": result["clone_id"],
                    "clone_name": result["clone_name"],
                    "agent_id": result["agent_id"],
                    "agent_name": result["agent_name"],
                    "mastra_agent_id": result["mastra_agent_id"],
                    "mastra_api_base_url": result["mastra_api_base_url"]
                }
                logger.info(f"[DINGBAT] Rep room config fetched by slug '{slug}': {config['agent_name']} ({config['mastra_agent_id']})")
                return config
            else:
                logger.error(f"[DINGBAT] No rep room found for slug: {slug}")
                return None
                
        finally:
            await conn.close()
            
    except Exception as e:
        logger.error(f"[DINGBAT] Database query failed for slug '{slug}': {e}")
        return None

async def get_mastra_config_from_db(rep_room_id: str) -> Optional[dict]:
    """Fetch Mastra agent configuration from database using rep room ID."""
    if not HTTP_CLIENT_AVAILABLE:
        logger.error("[DINGBAT] Database client not available")
        return None
    
    try:
        # Get database connection string from environment
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            logger.error("[DINGBAT] DATABASE_URL not found in environment")
            return None
        
        # Connect to database and fetch Mastra configuration
        conn = await asyncpg.connect(database_url)
        try:
            query = """
            SELECT
                rr.id as rep_room_id,
                rr.public_slug,
                rr.title,
                uac.id as clone_id,
                uac.name as clone_name,
                a.id as agent_id,
                a.name as agent_name,
                a.mastra_agent_id,
                a.mastra_api_base_url
            FROM rep_rooms rr
            JOIN user_agent_clones uac ON rr.user_agent_clone_id = uac.id
            JOIN agents a ON uac.parent_agent_id = a.id
            WHERE rr.id = $1
            """
            
            result = await conn.fetchrow(query, rep_room_id)
            if result:
                config = {
                    "rep_room_id": result["rep_room_id"],
                    "public_slug": result["public_slug"],
                    "title": result["title"],
                    "clone_id": result["clone_id"],
                    "clone_name": result["clone_name"],
                    "agent_id": result["agent_id"],
                    "agent_name": result["agent_name"],
                    "mastra_agent_id": result["mastra_agent_id"],
                    "mastra_api_base_url": result["mastra_api_base_url"]
                }
                logger.info(f"[DINGBAT] Mastra config fetched: {config['agent_name']} ({config['mastra_agent_id']})")
                return config
            else:
                logger.error(f"[DINGBAT] No Mastra config found for rep room ID: {rep_room_id}")
                return None
                
        finally:
            await conn.close()
            
    except Exception as e:
        logger.error(f"[DINGBAT] Database query failed: {e}")
        return None

async def generate_llm_response(user_text: str, llm_client) -> Optional[str]:
    """Generate response using Mastra agent or fallback to OpenAI."""
    try:
        # COMPREHENSIVE DIAGNOSTIC LOGGING - LLM Response Generation
        logger.info("=" * 80)
        logger.info("[DIAGNOSTIC] LLM RESPONSE GENERATION")
        logger.info("=" * 80)
        
        logger.info(f"[DIAGNOSTIC] User Text: '{user_text}'")
        logger.info(f"[DIAGNOSTIC] User Text Length: {len(user_text)}")
        logger.info(f"[DIAGNOSTIC] LLM Client Available: {'Yes' if llm_client else 'No'}")
        logger.info(f"[DIAGNOSTIC] LLM Client Type: {type(llm_client) if llm_client else 'None'}")
        
        # DIAGNOSTIC: Log environment context analysis
        logger.info("[DIAGNOSTIC] Environment Context Analysis:")
        env_context = {
            'REP_ROOM_ID': os.getenv("REP_ROOM_ID"),
            'REP_ROOM_SLUG': os.getenv("REP_ROOM_SLUG"),
            'LIVEKIT_ROOM_NAME': os.getenv("LIVEKIT_ROOM_NAME"),
            'SESSION_URL': os.getenv("SESSION_URL"),
            'DATABASE_URL': 'Available' if os.getenv("DATABASE_URL") else 'Not Available'
        }
        
        for key, value in env_context.items():
            logger.info(f"[DIAGNOSTIC]    - {key}: {value}")
        
        # CRITICAL FIX: Get rep room ID from LiveKit room name or environment
        rep_room_id = os.getenv("REP_ROOM_ID")
        
        logger.info(f"[DIAGNOSTIC] Initial Rep Room ID from environment: {rep_room_id}")
        
        # If not in environment, try to extract from LiveKit room context
        if not rep_room_id:
            logger.info("[DIAGNOSTIC] No Rep Room ID in environment, attempting extraction from LiveKit context")
            # Try to get room name from LiveKit context and extract rep room slug
            try:
                room_name = os.getenv("LIVEKIT_ROOM_NAME", "")
                if room_name:
                    logger.info(f"[MAGNIFYING_GLASS_LEFT] Attempting to resolve rep room ID from room name: {room_name}")
                    
                    # CRITICAL FIX: Extract rep room slug from LiveKit room name
                    # LiveKit room names can be in different formats:
                    # 1. Direct slug: "t1"
                    # 2. Prefixed format: "reproom-1750594992642" (timestamp-based)
                    # 3. Session format: "session-3d43005b-936c-472f-9565-c260eebcca9b"
                    
                    rep_room_slug = None
                    
                    # Method 1: Check if room name is a direct slug (simple format like "t1")
                    if len(room_name) <= 10 and not room_name.startswith("reproom-") and not room_name.startswith("session-"):
                        rep_room_slug = room_name
                        logger.info(f"[SYMBOL] Direct slug detected: {rep_room_slug}")
                    
                    # Method 2: Extract from prefixed format "reproom-{timestamp}"
                    elif room_name.startswith("reproom-"):
                        # For timestamp-based room names, we need to find the actual rep room
                        # This might require a different database query or mapping
                        logger.info(f"[MAGNIFYING_GLASS_LEFT] Timestamp-based room name detected: {room_name}")
                        
                        # Try to extract timestamp and find corresponding rep room
                        timestamp_part = room_name.replace("reproom-", "")
                        if timestamp_part.isdigit():
                            # Query database for rep rooms created around this timestamp
                            # For now, let's try a different approach - check environment for slug
                            rep_room_slug = os.getenv("REP_ROOM_SLUG", "")
                            if rep_room_slug:
                                logger.info(f"[SYMBOL] Using REP_ROOM_SLUG from environment: {rep_room_slug}")
                            else:
                                # Fallback: try common slugs or extract from session context
                                logger.warning(f"[WARNING] Could not extract slug from timestamp room name: {room_name}")
                                # Try to get slug from session URL or other context
                                session_url = os.getenv("SESSION_URL", "")
                                if session_url and "/rroom/" in session_url:
                                    # Extract slug from URL like "/rroom/t1/session-..."
                                    url_parts = session_url.split("/rroom/")
                                    if len(url_parts) > 1:
                                        slug_part = url_parts[1].split("/")[0]
                                        if slug_part:
                                            rep_room_slug = slug_part
                                            logger.info(f"[SYMBOL] Extracted slug from session URL: {rep_room_slug}")
                    
                    # Method 3: Extract from session format
                    elif room_name.startswith("session-"):
                        # For session-based room names, check environment variables
                        rep_room_slug = os.getenv("REP_ROOM_SLUG", "")
                        if rep_room_slug:
                            logger.info(f"[SYMBOL] Using REP_ROOM_SLUG from environment for session room: {rep_room_slug}")
                        else:
                            logger.warning(f"[WARNING] Session room detected but no REP_ROOM_SLUG in environment: {room_name}")
                    
                    # Query database with the extracted slug
                    if rep_room_slug:
                        logger.info(f"[MAGNIFYING_GLASS_LEFT] Querying database with slug: {rep_room_slug}")
                        rep_room_config = await get_rep_room_by_slug(rep_room_slug)
                        if rep_room_config:
                            rep_room_id = rep_room_config["rep_room_id"]
                            logger.info(f"[DINGBAT] Resolved rep room ID from slug '{rep_room_slug}': {rep_room_id}")
                        else:
                            logger.warning(f"[WARNING] Could not resolve rep room ID from slug: {rep_room_slug}")
                    else:
                        logger.warning(f"[WARNING] Could not extract rep room slug from room name: {room_name}")
                        
            except Exception as e:
                logger.error(f"[DINGBAT] Error resolving rep room ID from room context: {e}")
        
        if not rep_room_id:
            logger.warning("[WARNING] REP_ROOM_ID not found - falling back to OpenAI")
            return await generate_openai_fallback_response(user_text, llm_client)
        
        # Fetch Mastra configuration from database
        mastra_config = await get_mastra_config_from_db(rep_room_id)
        if not mastra_config:
            logger.warning("[WARNING] Mastra config not found - falling back to OpenAI")
            return await generate_openai_fallback_response(user_text, llm_client)
        
        # Generate response using Mastra agent
        return await generate_mastra_agent_response(user_text, mastra_config)
        
    except Exception as e:
        logger.error(f"[DINGBAT] Response generation failed: {e}")
        # Fallback to OpenAI on any error
        return await generate_openai_fallback_response(user_text, llm_client)

async def generate_mastra_agent_response(user_text: str, mastra_config: dict) -> Optional[str]:
    """Generate response using Mastra agent HTTP API."""
    if not HTTP_CLIENT_AVAILABLE:
        logger.error("[DINGBAT] HTTP client not available for Mastra integration")
        return None
    
    try:
        mastra_agent_id = mastra_config["mastra_agent_id"]
        mastra_api_base_url = mastra_config["mastra_api_base_url"]
        
        # Log Mastra request
        logger.info(f"[ROBOT] Mastra Agent Request: {mastra_agent_id} at {mastra_api_base_url}")
        logger.info(f"[ROBOT] User input: {user_text}")
        
        # Construct Mastra agent endpoint
        agent_endpoint = f"{mastra_api_base_url}/api/agents/{mastra_agent_id}/generate"
        
        # Prepare payload for Mastra agent
        thread_id = f"voice-session-{int(time.time())}"
        resource_id = f"rep-room-{mastra_config['rep_room_id']}"
        
        payload = {
            "threadId": thread_id,
            "resourceId": resource_id,
            "messages": [
                {
                    "role": "user",
                    "content": user_text
                }
            ],
            "tools": []
        }
        
        # Make HTTP request to Mastra agent with circuit breaker protection
        async with aiohttp.ClientSession() as session:
            response = await llm_circuit_breaker.call(
                session.post,
                agent_endpoint,
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "RepRoom-VoiceAgent/1.0"
                },
                timeout=aiohttp.ClientTimeout(total=10.0)
            )
            
            if response.status == 200:
                response_data = await response.json()
                
                # Extract response text from Mastra agent response
                if "text" in response_data:
                    mastra_response = response_data["text"].strip()
                elif "response" in response_data and "content" in response_data["response"]:
                    mastra_response = response_data["response"]["content"].strip()
                elif "content" in response_data:
                    mastra_response = response_data["content"].strip()
                elif "message" in response_data:
                    mastra_response = response_data["message"].strip()
                else:
                    logger.error(f"[DINGBAT] Unexpected Mastra response format: {response_data}")
                    return None
                
                # Log Mastra response
                logger.info(f"[ROBOT] Mastra Agent Response: {mastra_response}")
                
                return mastra_response
            else:
                logger.error(f"[DINGBAT] Mastra agent request failed: {response.status} - {await response.text()}")
                return None
                
    except Exception as e:
        logger.error(f"[DINGBAT] Mastra agent request failed: {e}")
        return None

async def generate_openai_fallback_response(user_text: str, llm_client) -> Optional[str]:
    """Generate fallback response using OpenAI GPT-4o-mini."""
    if not llm_client:
        logger.error("[DINGBAT] No LLM client available for fallback")
        return None
    
    try:
        # Log fallback request
        logger.info(f"[ROBOT] OpenAI Fallback Request: {user_text}")
        
        # Create chat completion with circuit breaker protection
        response = llm_circuit_breaker.call(
            llm_client.chat.completions.create,
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful AI assistant in a voice conversation. Keep responses concise and conversational."},
                {"role": "user", "content": user_text}
            ],
            max_tokens=150,
            temperature=0.7
        )
        
        llm_response = response.choices[0].message.content.strip()
        
        # Log fallback response
        logger.info(f"[ROBOT] OpenAI Fallback Response: {response}")
        
        return llm_response
        
    except Exception as e:
        logger.error(f"[DINGBAT] OpenAI fallback generation failed: {e}")
        return None

async def send_ag_ui_data_channel_events(ctx: JobContext, message_type: str, content: str, role: str = "assistant", session_id: str = None):
    """Send AG-UI data channel events for user transcripts and agent responses with REP-106 Phase 3 session support."""
    try:
        # Generate unique message ID
        message_id = str(uuid.uuid4())
        
        # REP-106 Phase 3: Extract session info from room name if not provided
        if not session_id:
            session_info = session_manager.parse_room_name(ctx.room.name)
            if session_info:
                session_id = session_info['session_id']
        
        # Create data channel message following AG-UI TextMessage event format
        ag_ui_events = []
        
        # REP-106 Phase 3: Enhanced events with session information
        base_timestamp = int(time.time() * 1000)
        
        # TextMessageStart event
        start_event = {
            "type": "TEXT_MESSAGE_START",
            "messageId": message_id,
            "role": role,
            "timestamp": base_timestamp
        }
        if session_id:
            start_event["sessionId"] = session_id
        ag_ui_events.append(start_event)
        
        # TextMessageContent event
        content_event = {
            "type": "TEXT_MESSAGE_CONTENT",
            "messageId": message_id,
            "delta": content,
            "timestamp": base_timestamp
        }
        if session_id:
            content_event["sessionId"] = session_id
        ag_ui_events.append(content_event)
        
        # TextMessageEnd event
        end_event = {
            "type": "TEXT_MESSAGE_END",
            "messageId": message_id,
            "timestamp": base_timestamp
        }
        if session_id:
            end_event["sessionId"] = session_id
        ag_ui_events.append(end_event)
        
        # Send events via data channel
        for event in ag_ui_events:
            try:
                # Validate event structure before processing
                if not isinstance(event, dict):
                    logger.error(f"[DINGBAT] Invalid event type: {type(event)}, expected dict")
                    continue
                
                if 'type' not in event:
                    logger.error(f"[DINGBAT] Event missing 'type' field: {event}")
                    continue
                
                # Convert to JSON string for data channel with error handling
                try:
                    event_json = json.dumps(event)
                    if not isinstance(event_json, str):
                        logger.error(f"[DINGBAT] JSON serialization failed: got {type(event_json)}, expected str")
                        continue
                except (TypeError, ValueError) as json_error:
                    logger.error(f"[DINGBAT] JSON serialization error for event {event}: {json_error}")
                    continue
                
                # Encode to bytes with validation
                try:
                    event_bytes = event_json.encode('utf-8')
                    if not isinstance(event_bytes, bytes):
                        logger.error(f"[DINGBAT] UTF-8 encoding failed: got {type(event_bytes)}, expected bytes")
                        continue
                except (UnicodeEncodeError, AttributeError) as encode_error:
                    logger.error(f"[DINGBAT] UTF-8 encoding error for event_json '{event_json}': {encode_error}")
                    continue
                
                # Validate payload before sending
                if not event_bytes:
                    logger.error(f"[DINGBAT] Empty payload after encoding for event: {event}")
                    continue
                
                # Send via LiveKit data channel with detailed error logging
                try:
                    await ctx.room.local_participant.publish_data(
                        payload=event_bytes,
                        destination_identities=[],  # Send to all participants (empty list for broadcast)
                        topic="ag-ui-events",
                        reliable=True
                    )
                    
                    logger.info(f"[SATELLITE] AG-UI Data Channel Event Sent: {event['type']} for {message_type}")
                    
                except Exception as publish_error:
                    logger.error(f"[DINGBAT] LiveKit publish_data failed for event {event['type']}: {publish_error}")
                    logger.error(f"[DINGBAT] Payload type: {type(event_bytes)}, length: {len(event_bytes) if event_bytes else 0}")
                    logger.error(f"[DINGBAT] Event JSON: {event_json[:200]}..." if len(event_json) > 200 else f"[DINGBAT] Event JSON: {event_json}")
                    continue
                
            except Exception as e:
                logger.error(f"[DINGBAT] Failed to send AG-UI data channel event: {e}")
                logger.error(f"[DINGBAT] Event details: {event}")
                logger.error(f"[DINGBAT] Exception type: {type(e).__name__}")
                import traceback
                logger.error(f"[DINGBAT] Full traceback: {traceback.format_exc()}")
                
    except Exception as e:
        logger.error(f"[DINGBAT] Failed to create AG-UI data channel events: {e}")

async def log_tts_audio_publication(audio_source: rtc.AudioSource, text: str):
    """Log TTS audio publication details."""
    try:
        logger.info(f"[LOUD_SOUND] TTS Audio Published: '{text}' via audio source {id(audio_source)}")
        logger.info(f"[LOUD_SOUND] TTS Audio Details: Sample Rate: {audio_source.sample_rate}, Channels: {audio_source.num_channels}")
    except Exception as e:
        logger.error(f"[DINGBAT] Failed to log TTS audio publication: {e}")

# --- Enhanced Agent Entry Point with REP-105 Integration and REP-106 Phase 3 ---
async def entrypoint(ctx: JobContext):
    """Enhanced entrypoint with REP-105 edge function integration and REP-106 Phase 3 session coordination"""
    logger.info(f"[REP-106] PYTHON AGENT ENTRYPOINT CALLED - ROOM: {ctx.room.name}")
    logger.info(f"[REP-106] Stable Voice Agent started for room: {ctx.room.name}")
    
    # REP-106 Phase 3: Check if this is a rep room session
    session_info = session_manager.parse_room_name(ctx.room.name)
    if session_info:
        logger.info(f"[REP-106] Detected Rep Room session: slug='{session_info['slug']}', session_id='{session_info['session_id']}'")
        # Register this session
        session_info.update({
            'room_metadata': ctx.room.metadata if hasattr(ctx.room, 'metadata') else {},
            'status': 'agent_joining',
            'agent_started_at': time.time()
        })
        session_manager.register_session(session_info)
    else:
        logger.info(f"[REP-106] Non-rep-room session detected: {ctx.room.name}")
    
    # Log raw metadata received from LiveKit
    logger.debug(f"Job metadata type: {type(ctx.job.metadata)}")
    logger.debug(f"Job metadata length: {len(str(ctx.job.metadata)) if ctx.job.metadata else 0}")
    
    # Log room context
    logger.info(f"Room name: {ctx.room.name}")
    
    # Extract configuration from job context - ENHANCED: Check both job and room metadata
    agent_config = {}
    context = {}
    
    try:
        logger.debug("Enhanced metadata parsing analysis")
        
        # First, try to extract from job metadata
        job_agent_config = {}
        job_context = {}
        
        if isinstance(ctx.job.metadata, dict):
            logger.debug("Job metadata is dict type")
            job_agent_config = ctx.job.metadata.get('agentConfig', {})
            job_context = ctx.job.metadata.get('context', {})
            logger.debug(f"Extracted job agentConfig keys: {list(job_agent_config.keys()) if job_agent_config else 'None'}")
            logger.debug(f"Extracted job context keys: {list(job_context.keys()) if job_context else 'None'}")
            
        elif isinstance(ctx.job.metadata, str):
            logger.debug("Job metadata is string type, attempting JSON parse")
            
            # Try to parse JSON string
            try:
                metadata_dict = json.loads(ctx.job.metadata)
                logger.debug("Job metadata JSON parse successful")
                
                job_agent_config = metadata_dict.get('agentConfig', {})
                job_context = metadata_dict.get('context', {})
                logger.debug(f"Extracted job agentConfig keys: {list(job_agent_config.keys()) if job_agent_config else 'None'}")
                logger.debug(f"Extracted job context keys: {list(job_context.keys()) if job_context else 'None'}")
                
            except json.JSONDecodeError as json_err:
                logger.warning(f"Job metadata JSON parse failed: {json_err}")
                job_agent_config = {}
                job_context = {}
        else:
            logger.warning(f"Unexpected job metadata type: {type(ctx.job.metadata)}")
            job_agent_config = {}
            job_context = {}
        
        # Now, try to extract from room metadata
        room_agent_config = {}
        room_context = {}
        
        logger.debug("[REP-106] Room metadata analysis")
        
        if hasattr(ctx.room, 'metadata') and ctx.room.metadata:
            logger.debug(f"[REP-106] Room metadata type: {type(ctx.room.metadata)}, length: {len(str(ctx.room.metadata)) if ctx.room.metadata else 0}")
            
            if isinstance(ctx.room.metadata, dict):
                logger.debug("[REP-106] Room metadata is dict type")
                # Use correct field names from voice token function, mapping to expected format
                room_agent_config = ctx.room.metadata.get('agent_config', {})  # snake_case not camelCase
                
                # REP-106 Phase 3: Enhanced session-specific context extraction
                room_context = {
                    # Map snake_case from voice token to camelCase expected by agent
                    'repRoomId': ctx.room.metadata.get('rep_room_id'),
                    'agentCloneId': ctx.room.metadata.get('agent_clone_id'),
                    'tenantId': ctx.room.metadata.get('organization_id'),  # map organization_id to tenantId
                    'userId': ctx.room.metadata.get('user_id'),
                    'sessionId': ctx.room.metadata.get('session_id'),
                    'participantId': ctx.room.metadata.get('participant_id'),
                    'participantName': ctx.room.metadata.get('participant_name'),
                    'isPublicAccess': ctx.room.metadata.get('is_public_access'),
                    'isAnonymous': ctx.room.metadata.get('is_anonymous'),
                    # REP-106 Phase 3: Additional session context
                    'repRoomSlug': ctx.room.metadata.get('rep_room_slug'),
                    'sessionType': ctx.room.metadata.get('session_type', 'voice'),
                    'isInitiator': ctx.room.metadata.get('is_initiator', False)
                }
                
                # REP-106 Phase 3: If we have session info from room name parsing, merge it
                if session_info:
                    room_context.update({
                        'repRoomSlug': session_info['slug'],
                        'sessionId': session_info['session_id'],
                        'roomName': session_info['room_name']
                    })
                    logger.info(f"[REP-106] Merged session info from room name: slug='{session_info['slug']}', session_id='{session_info['session_id']}'")
                
                logger.debug(f"[REP-106] Extracted room agent_config keys: {list(room_agent_config.keys()) if room_agent_config else 'None'}")
                logger.debug(f"[REP-106] Extracted room context keys: {list(room_context.keys()) if room_context else 'None'}")
                
            elif isinstance(ctx.room.metadata, str):
                logger.debug("Room metadata is string type, attempting JSON parse")
                
                # Try to parse JSON string
                try:
                    room_metadata_dict = json.loads(ctx.room.metadata)
                    logger.debug("Room metadata JSON parse successful")
                    
                    # Use correct field names from voice token function, mapping to expected format
                    room_agent_config = room_metadata_dict.get('agent_config', {})  # snake_case not camelCase
                    room_context = {
                        # Map snake_case from voice token to camelCase expected by agent
                        'repRoomId': room_metadata_dict.get('rep_room_id'),
                        'agentCloneId': room_metadata_dict.get('agent_clone_id'),
                        'tenantId': room_metadata_dict.get('organization_id'),  # map organization_id to tenantId
                        'userId': room_metadata_dict.get('user_id'),
                        'sessionId': room_metadata_dict.get('session_id'),
                        'participantId': room_metadata_dict.get('participant_id'),
                        'participantName': room_metadata_dict.get('participant_name'),
                        'isPublicAccess': room_metadata_dict.get('is_public_access'),
                        'isAnonymous': room_metadata_dict.get('is_anonymous')
                    }
                    logger.debug(f"Extracted agent_config keys: {list(room_agent_config.keys()) if room_agent_config else 'None'}")
                    logger.debug(f"Extracted context keys: {list(room_context.keys()) if room_context else 'None'}")
                    
                except json.JSONDecodeError as json_err:
                    logger.warning(f"Room metadata JSON parse failed: {json_err}")
                    room_agent_config = {}
                    room_context = {}
            else:
                logger.warning(f"Unexpected room metadata type: {type(ctx.room.metadata)}")
                room_agent_config = {}
                room_context = {}
        else:
            logger.debug("No room metadata available")
            room_agent_config = {}
            room_context = {}
        
        # Merge metadata with room metadata taking precedence over job metadata
        logger.debug("Metadata merging analysis")
        
        # Start with job metadata as base
        agent_config = job_agent_config.copy()
        context = job_context.copy()
        
        # Override with room metadata if available
        if room_agent_config:
            logger.debug("Merging room agentConfig over job agentConfig")
            agent_config.update(room_agent_config)
        
        if room_context:
            logger.debug("Merging room context over job context")
            context.update(room_context)
        
        # Log final merged results
        logger.debug(f"Final merged agentConfig keys: {list(agent_config.keys()) if agent_config else 'None'}")
        logger.debug(f"Final merged context keys: {list(context.keys()) if context else 'None'}")
        
        # REP-106 Phase 3: Enhanced key context parameters validation
        key_params = ['repRoomId', 'userId', 'tenantId', 'agentCloneId']
        session_params = ['sessionId', 'repRoomSlug'] if session_info else []
        all_params = key_params + session_params
        
        missing_params = []
        for param in all_params:
            final_value = context.get(param) or agent_config.get(param)
            if not final_value:
                missing_params.append(param)
        
        if missing_params:
            logger.warning(f"[REP-106] Missing parameters: {missing_params}")
            if session_info:
                logger.info(f"[REP-106] Session-specific parameters available: sessionId='{session_info.get('session_id')}', slug='{session_info.get('slug')}'")
            
    except Exception as e:
        logger.error(f"Error extracting metadata: {e}")
        import traceback
        logger.debug(f"Full traceback: {traceback.format_exc()}")
        agent_config = {}
        context = {}
    
    # Log parsed context parameters summary
    logger.debug(f"agentConfig keys: {list(agent_config.keys()) if agent_config else 'None'}")
    logger.debug(f"context keys: {list(context.keys()) if context else 'None'}")
    
    # Agent clone details analysis
    agent_clone_id = agent_config.get('agentCloneId') or context.get('agentCloneId')
    if agent_clone_id:
        logger.info(f"Agent Clone ID found: {agent_clone_id}")
    else:
        logger.warning("No Agent Clone ID found in agentConfig or context")
    
    # LLM Selection Logic Analysis
    openai_key = os.getenv('OPENAI_API_KEY')
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_ANON_KEY')
    
    use_copilotkit = bool(supabase_url and supabase_key and context.get('repRoomId'))
    logger.info(f"Will use CopilotKit: {use_copilotkit}")
    
    if not use_copilotkit and not openai_key:
        logger.error("No LLM integration available - missing both CopilotKit and OpenAI credentials")
    
    # Final Context Summary
    logger.info(f"REP-105 Starting voice agent for user: {context.get('userId')}")
    logger.info(f"REP-105 Rep room: {context.get('repRoomSlug')}")
    logger.info(f"REP-105 Using edge function integration")
    
    # Validate configuration and use environment variable fallbacks
    required_params = ['repRoomId', 'userId', 'tenantId']
    missing_params = [param for param in required_params if not context.get(param)]
    
    if missing_params:
        logger.warning(f"Missing required context parameters: {missing_params} - falling back to environment variables")
        
        # Create fallback context
        context = {
            'repRoomId': os.getenv('REP_ROOM_ID', 'default'),
            'userId': os.getenv('USER_ID', 'default-user'),
            'tenantId': os.getenv('TENANT_ID', 'default-tenant'),
            'repRoomSlug': os.getenv('REP_ROOM_SLUG', 't1'),
            'userJwtToken': os.getenv('USER_JWT_TOKEN', ''),
            'threadId': f"room-{ctx.room.name}-{int(time.time())}"
        }
        logger.debug(f"Created fallback context with keys: {list(context.keys())}")
    else:
        logger.debug("All required context parameters found - using provided context")
    
    try:
        # Get TTS, STT providers
        tts_provider = get_tts_provider()
        stt_provider = get_stt_provider()
        
        # Check if we have required providers
        if not tts_provider:
            logger.error("No TTS provider available - cannot proceed")
            return
            
        if not stt_provider:
            logger.error("No STT provider available - cannot proceed")
            return
        
        # Initialize CopilotKit edge function client with enhanced agent config validation
        copilotkit_client = None
        enhanced_llm = None
        
        # CRITICAL FIX: Validate that we have agent configuration before proceeding
        has_agent_config = bool(agent_config and (
            agent_config.get('mastra_api_base_url') or
            agent_config.get('mastra_agent_id') or
            agent_config.get('id')
        ))
        
        logger.info(f"REP-105 Agent config validation: has_config={has_agent_config}")
        if agent_config:
            logger.info(f"REP-105 Agent config keys: {list(agent_config.keys())}")
            logger.info(f"REP-105 Mastra URL: {agent_config.get('mastra_api_base_url')}")
            logger.info(f"REP-105 Mastra Agent ID: {agent_config.get('mastra_agent_id')}")
        
        if has_agent_config:
            try:
                # REP-106 Phase 3: Enhance context with agent configuration and session info for edge function
                enhanced_context = context.copy()
                enhanced_context.update({
                    'agentId': agent_config.get('id') or agent_config.get('mastra_agent_id'),
                    'mastraApiBaseUrl': agent_config.get('mastra_api_base_url'),
                    'mastraAgentId': agent_config.get('mastra_agent_id'),
                    'agentName': agent_config.get('name'),
                    'agentType': agent_config.get('type')
                })
                
                # REP-106 Phase 3: Add session-specific context if available
                if session_info:
                    enhanced_context.update({
                        'sessionId': session_info['session_id'],
                        'repRoomSlug': session_info['slug'],
                        'roomName': session_info['room_name'],
                        'sessionType': 'persistent_voice',
                        'threadId': f"session-{session_info['session_id']}"  # Use session-specific thread ID
                    })
                    logger.info(f"[REP-106] Enhanced context with session info: {session_info['session_id']}")
                
                copilotkit_client = CopilotKitMastraAgentClient(agent_config, enhanced_context)
                enhanced_llm = CopilotKitMastraLLM(copilotkit_client)
                logger.info("[REP-106] CopilotKit edge function client initialized successfully with session-specific agent config")
                logger.info(f"[REP-106] Will route to Mastra agent: {agent_config.get('name')} ({agent_config.get('mastra_agent_id')})")
                if session_info:
                    logger.info(f"[REP-106] Session context: slug='{session_info['slug']}', session_id='{session_info['session_id']}'")
            except Exception as e:
                logger.error(f"[REP-106] Failed to initialize CopilotKit client: {str(e)}")
                logger.warning("[REP-106] Falling back to OpenAI LLM")
        else:
            logger.warning("REP-105 No valid agent configuration found - will use OpenAI fallback")
            logger.warning("REP-105 This means voice responses will be generic instead of agent-specific")
            
        # Get fallback LLM client
        llm_client = get_llm_client()
        if not enhanced_llm and not llm_client:
            logger.warning("No LLM client available - will use fallback responses")
        
        # Connect to the room
        logger.info("Connecting to LiveKit room...")
        await ctx.connect()
        
        logger.info("Agent connected and ready for voice interactions")
        
        # Create audio source for agent responses - match TTS provider output
        audio_source = rtc.AudioSource(sample_rate=22050, num_channels=1)  # ElevenLabs default
        track = rtc.LocalAudioTrack.create_audio_track("agent-voice", audio_source)
        options = rtc.TrackPublishOptions()
        options.source = rtc.TrackSource.SOURCE_MICROPHONE
        
        # Publish the agent's audio track
        publication = await ctx.room.local_participant.publish_track(track, options)
        logger.info("Agent audio track published")
        
        try:
            # Wait for participant to join
            participant = await ctx.wait_for_participant()
            logger.info(f"Participant joined: {participant.identity}")
            
            logger.info("Stable voice agent ready - waiting for user speech")
            
            # Set up audio processing for participant with proper error handling
            async def handle_participant_audio(participant: rtc.RemoteParticipant):
                """Handle audio from a participant with timeout and circuit breaker protection."""
                logger.info(f"Setting up audio processing for participant: {participant.identity}")
                
                # Subscribe to participant's audio tracks
                for publication in participant.track_publications.values():
                    if publication.track and publication.track.kind == rtc.TrackKind.KIND_AUDIO:
                        logger.info(f"Subscribing to audio track: {publication.track.sid}")
                        
                        # Create audio stream for STT
                        audio_stream = rtc.AudioStream(publication.track)
                        stt_stream = stt_provider.stream()
                        
                        # Audio processing with timeout protection
                        async def process_audio():
                            """Process audio frames with timeout protection."""
                            frame_count = 0
                            last_log_time = time.time()
                            
                            try:
                                async for audio_frame_event in audio_stream:
                                    frame_count += 1
                                    current_time = time.time()
                                    
                                    # Log every 5 seconds to avoid spam
                                    if current_time - last_log_time > 5:
                                        logger.debug(f"Processed {frame_count} audio frames")
                                        last_log_time = current_time
                                        frame_count = 0
                                    
                                    # Extract and push audio frame with circuit breaker protection
                                    try:
                                        audio_frame = audio_frame_event.frame
                                        stt_circuit_breaker.call(stt_stream.push_frame, audio_frame)
                                    except Exception as e:
                                        logger.error(f"STT circuit breaker triggered: {e}")
                                        await asyncio.sleep(1)  # Brief pause before retry
                                        
                            except Exception as e:
                                logger.error(f"Audio processing error: {e}")
                        
                        # STT results handling with timeout mechanism
                        async def handle_stt_results():
                            """Handle STT results with proper timeout and error handling."""
                            last_interim_text = ""
                            last_interim_time = None
                            processing_timeout = 2.0  # 2 second timeout for STT processing
                            accumulated_final_text = ""  # Accumulate final transcripts
                            event_counter = 0
                            
                            # Enhanced transcript tracking for END_OF_SPEECH detection
                            current_interim_transcript = ""
                            speech_started = False
                            last_transcript_time = None
                            end_of_speech_timeout = 1.5  # 1.5 seconds after END_OF_SPEECH to finalize
                            
                            logger.info("STT Results Handler Started")
                            
                            try:
                                async for event in stt_stream:
                                    current_time = time.time()
                                    event_counter += 1
                                    stt_counters.total_events += 1
                                    
                                    # Minimal event debugging
                                    event_attrs = debug_stt_event(event, event_counter)
                                    
                                    # Handle Deepgram-specific event types with enhanced END_OF_SPEECH detection
                                    if hasattr(event, 'type'):
                                        event_type = getattr(event, 'type', None)
                                        # Check event type
                                        
                                        # Handle Deepgram speech events with proper type checking
                                        if DEEPGRAM_EVENTS_AVAILABLE:
                                            # Use enum comparison if available
                                            
                                            # Use proper enum comparison if available
                                            if event_type == SpeechEventType.START_OF_SPEECH:
                                                logger.info("START_OF_SPEECH detected")
                                                stt_counters.speech_start_count += 1
                                                speech_started = True
                                                current_interim_transcript = ""  # Reset transcript accumulation
                                                last_transcript_time = current_time
                                                continue
                                            elif event_type == SpeechEventType.END_OF_SPEECH:
                                                logger.info("END_OF_SPEECH detected")
                                                stt_counters.speech_end_count += 1
                                                speech_started = False
                                                
                                                if current_interim_transcript and len(current_interim_transcript.strip()) > 0:
                                                    final_text = current_interim_transcript.strip()
                                                    logger.info(f"END_OF_SPEECH FINAL TRANSCRIPT: '{final_text}'")
                                                    stt_counters.final_count += 1
                                                    
                                                    # Process as final transcript
                                                    await process_final_transcript(final_text, ctx, enhanced_llm, llm_client, audio_source, tts_provider)
                                                    
                                                    # Reset transcript tracking
                                                    current_interim_transcript = ""
                                                    last_transcript_time = None
                                                else:
                                                    logger.warning("END_OF_SPEECH received but no accumulated transcript available")
                                                continue
                                            elif (hasattr(SpeechEventType, 'RECOGNITION_USAGE') and
                                                  event_type == SpeechEventType.RECOGNITION_USAGE):
                                                logger.debug("Recognition usage event (metadata)")
                                                stt_counters.recognition_usage_count += 1
                                                continue
                                        
                                        # Fallback to string matching when Deepgram enums not available
                                        event_type_str = str(event_type)
                                        event_type_value = getattr(event_type, 'value', str(event_type))  # Get enum value
                                        
                                        # Check both the string representation and the enum value
                                        if (event_type_value in ['start_of_speech', 'START_OF_SPEECH'] or
                                            event_type_str in ['start_of_speech', 'START_OF_SPEECH']):
                                            logger.info("START_OF_SPEECH detected via string matching")
                                            stt_counters.speech_start_count += 1
                                            speech_started = True
                                            current_interim_transcript = ""  # Reset transcript accumulation
                                            last_transcript_time = current_time
                                            continue
                                        elif (event_type_value in ['end_of_speech', 'END_OF_SPEECH'] or
                                              event_type_str in ['end_of_speech', 'END_OF_SPEECH']):
                                            logger.info("END_OF_SPEECH detected via string matching")
                                            stt_counters.speech_end_count += 1
                                            speech_started = False
                                            
                                            if current_interim_transcript and len(current_interim_transcript.strip()) > 0:
                                                final_text = current_interim_transcript.strip()
                                                logger.info(f"END_OF_SPEECH FINAL TRANSCRIPT: '{final_text}'")
                                                stt_counters.final_count += 1
                                                
                                                # Process as final transcript
                                                await process_final_transcript(final_text, ctx, enhanced_llm, llm_client, audio_source, tts_provider)
                                                
                                                # Reset transcript tracking
                                                current_interim_transcript = ""
                                                last_transcript_time = None
                                            else:
                                                logger.warning("END_OF_SPEECH received but no accumulated transcript available")
                                            continue
                                        elif (event_type_value in ['recognition_usage', 'RECOGNITION_USAGE'] or
                                              event_type_str in ['recognition_usage', 'RECOGNITION_USAGE']):
                                            logger.debug("Recognition usage event via string matching (metadata)")
                                            stt_counters.recognition_usage_count += 1
                                            continue
                                    # Final transcript detection logic
                                    
                                    # Check for alternatives first to ensure we have transcript content
                                    has_alternatives = hasattr(event, 'alternatives') and event.alternatives and len(event.alternatives) > 0
                                    has_text_content = False
                                    transcript_text = ""
                                    
                                    if has_alternatives:
                                        try:
                                            transcript_text = event.alternatives[0].text.strip()
                                            has_text_content = len(transcript_text) > 0
                                        except Exception as e:
                                            logger.debug(f"Error extracting text: {e}")
                                    
                                    # Check for transcript content
                                    
                                    # Check is_final attribute
                                    is_final_flag = False
                                    if hasattr(event, 'is_final'):
                                        is_final_value = getattr(event, 'is_final')
                                        is_final_flag = bool(is_final_value)
                                    
                                    # Check multiple conditions for final transcript detection
                                    is_final_transcript = False
                                    
                                    # Method 1: Standard is_final flag
                                    if is_final_flag and has_text_content:
                                        is_final_transcript = True
                                        logger.debug("Final transcript detected via is_final flag")
                                    
                                    # Method 2: Check for Deepgram-specific final indicators
                                    elif has_text_content and hasattr(event, 'type'):
                                        event_type_str = str(getattr(event, 'type', ''))
                                        if event_type_str in ['Results', 'FinalResult', 'FINAL']:
                                            is_final_transcript = True
                                            logger.debug("Final transcript detected via event type")
                                    
                                    # Method 3: Check for speech_final attribute (some Deepgram versions)
                                    elif has_text_content and hasattr(event, 'speech_final') and getattr(event, 'speech_final'):
                                        is_final_transcript = True
                                        logger.debug("Final transcript detected via speech_final flag")
                                    
                                    # Method 4: Check for channel.alternatives[0].transcript with is_final
                                    elif has_alternatives:
                                        try:
                                            alternative = event.alternatives[0]
                                            if hasattr(alternative, 'is_final') and getattr(alternative, 'is_final') and has_text_content:
                                                is_final_transcript = True
                                                logger.debug("Final transcript detected via alternative.is_final flag")
                                        except Exception as e:
                                            logger.debug(f"Error checking alternative.is_final: {e}")
                                    
                                    # Process final transcript
                                    if is_final_transcript:
                                        logger.info(f"FINAL TRANSCRIPT DETECTED!")
                                        stt_counters.final_count += 1
                                        
                                        if has_text_content:
                                            user_text = transcript_text
                                            logger.info(f"FINAL transcription: {user_text}")
                                            logger.info(f"Triggering LLM response generation...")
                                            
                                            # Process the final transcript through the LLM pipeline
                                            await process_final_transcript(user_text, ctx, enhanced_llm, llm_client, audio_source, tts_provider)
                                            
                                            # Reset tracking after successful response
                                            last_interim_text = ""
                                            last_interim_time = None
                                            accumulated_final_text = ""
                                                
                                    elif hasattr(event, 'alternatives') and event.alternatives and len(event.alternatives) > 0:
                                        # Handle interim results
                                        stt_counters.interim_count += 1
                                        
                                        interim_text = event.alternatives[0].text.strip()
                                        
                                        if interim_text:
                                            logger.debug(f"Interim: {interim_text}")
                                            last_interim_text = interim_text
                                            last_interim_time = current_time
                                            last_transcript_time = current_time
                                            
                                            # Accumulate interim transcript for END_OF_SPEECH detection
                                            current_interim_transcript = interim_text
                                            
                                            # Timeout fallback for interim results (reduced timeout)
                                            if (len(interim_text) > 3 and
                                                last_interim_time and
                                                current_time - last_interim_time >= processing_timeout):
                                                
                                                logger.warning(f"STT timeout - treating interim as final: {interim_text}")
                                                stt_counters.timeout_fallback_count += 1
                                                
                                                # Process as final transcript via timeout
                                                await process_final_transcript(interim_text, ctx, enhanced_llm, llm_client, audio_source, tts_provider)
                                                
                                                # Reset tracking after timeout processing
                                                last_interim_text = ""
                                                last_interim_time = None
                                                current_interim_transcript = ""
                                                speech_started = False
                                    
                                    # ENHANCED: Check for alternative event structures that might contain transcript data
                                    elif hasattr(event, 'text') and event.text:
                                        # Some STT providers might put text directly on the event object
                                        logger.info(f"[MAGNIFYING_GLASS_LEFT] DIRECT TEXT TRANSCRIPT DETECTED!")
                                        transcript_text = event.text.strip()
                                        logger.info(f"[MAGNIFYING_GLASS_LEFT] Direct text transcript: '{transcript_text}' (length: {len(transcript_text)})")
                                        
                                        if transcript_text:
                                            # Check if this is a final transcript based on event attributes
                                            is_final_direct = False
                                            if hasattr(event, 'is_final') and getattr(event, 'is_final'):
                                                is_final_direct = True
                                                logger.info(f"[SYMBOL] FINAL TRANSCRIPT via direct text: '{transcript_text}'")
                                                stt_counters.final_count += 1
                                                await process_final_transcript(transcript_text, ctx, llm_client, audio_source, tts_provider)
                                            else:
                                                # Treat as interim and accumulate
                                                logger.debug(f"[MICROPHONE] Interim via direct text: {transcript_text}")
                                                stt_counters.interim_count += 1
                                                last_interim_text = transcript_text
                                                last_interim_time = current_time
                                                last_transcript_time = current_time
                                                
                                                if speech_started:
                                                    current_interim_transcript = transcript_text
                                                    logger.debug(f"[ARROWS_COUNTERCLOCKWISE] Updated accumulated transcript via direct text: '{current_interim_transcript}'")
                                                else:
                                                    current_interim_transcript = transcript_text
                                                    logger.debug(f"🆕 Started new transcript accumulation via direct text: '{current_interim_transcript}'")
                                    
                                    # ENHANCED: Check for channel-based transcript structure
                                    elif hasattr(event, 'channel') and hasattr(event.channel, 'alternatives'):
                                        logger.info(f"[MAGNIFYING_GLASS_LEFT] CHANNEL-BASED TRANSCRIPT DETECTED!")
                                        channel_alternatives = event.channel.alternatives
                                        if channel_alternatives and len(channel_alternatives) > 0:
                                            transcript_text = channel_alternatives[0].transcript.strip()
                                            logger.info(f"[MAGNIFYING_GLASS_LEFT] Channel transcript: '{transcript_text}' (length: {len(transcript_text)})")
                                            
                                            if transcript_text:
                                                # Check if this is final
                                                is_final_channel = False
                                                if hasattr(channel_alternatives[0], 'is_final') and channel_alternatives[0].is_final:
                                                    is_final_channel = True
                                                    logger.info(f"[SYMBOL] FINAL TRANSCRIPT via channel: '{transcript_text}'")
                                                    stt_counters.final_count += 1
                                                    await process_final_transcript(transcript_text, ctx, llm_client, audio_source, tts_provider)
                                                else:
                                                    # Treat as interim
                                                    logger.debug(f"[MICROPHONE] Interim via channel: {transcript_text}")
                                                    stt_counters.interim_count += 1
                                                    last_interim_text = transcript_text
                                                    last_interim_time = current_time
                                                    last_transcript_time = current_time
                                                    
                                                    if speech_started:
                                                        current_interim_transcript = transcript_text
                                                        logger.debug(f"[ARROWS_COUNTERCLOCKWISE] Updated accumulated transcript via channel: '{current_interim_transcript}'")
                                                    else:
                                                        current_interim_transcript = transcript_text
                                                        logger.debug(f"🆕 Started new transcript accumulation via channel: '{current_interim_transcript}'")
                                    else:
                                        # Log unknown events with more detail for debugging
                                        logger.warning(f"[TRANSPORT] UNKNOWN STT EVENT DETECTED!")
                                        stt_counters.unknown_events += 1
                                        
                                        logger.debug(f"[MAGNIFYING_GLASS_LEFT] STT event type: {type(event).__name__}")
                                        
                                        # Only warn about events that have actual content but aren't handled
                                        # Skip events that are just metadata or empty speech events
                                        if (hasattr(event, 'alternatives') and event.alternatives and len(event.alternatives) > 0):
                                            logger.warning(f"[TRANSPORT] Unhandled STT event with content: {event}")
                                            logger.warning(f"[TRANSPORT] This event has alternatives but wasn't processed as final or interim!")
                                        else:
                                            # These are likely metadata events - just log at debug level
                                            logger.debug(f"[MAGNIFYING_GLASS_LEFT] Metadata STT event (ignored): {event}")
                                    
                                    # TIMEOUT-BASED FINAL TRANSCRIPT DETECTION (Additional Fallback)
                                    # Check if we have an accumulated transcript that hasn't been processed
                                    if (current_interim_transcript and
                                        last_transcript_time and
                                        current_time - last_transcript_time >= end_of_speech_timeout and
                                        not speech_started):  # Only if no active speech session
                                        
                                        logger.warning(f"[WARNING] TIMEOUT FALLBACK: No END_OF_SPEECH received, using timeout detection")
                                        logger.info(f"[SYMBOL] TIMEOUT FINAL TRANSCRIPT: '{current_interim_transcript}'")
                                        stt_counters.timeout_fallback_count += 1
                                        stt_counters.final_count += 1
                                        
                                        # Process as final transcript via timeout
                                        await process_final_transcript(current_interim_transcript, ctx, llm_client, audio_source, tts_provider)
                                        
                                        # Reset tracking after timeout processing
                                        current_interim_transcript = ""
                                        last_transcript_time = None
                                    
                                    # Log statistics every 10 events
                                    if event_counter % 10 == 0:
                                        stt_counters.log_stats()
                                        
                            except Exception as e:
                                logger.error(f"STT results handling error: {e}")
                        
                        # Start processing tasks with proper error handling
                        audio_task = asyncio.create_task(process_audio())
                        stt_task = asyncio.create_task(handle_stt_results())
                        
                        # Monitor tasks and restart if they fail
                        async def monitor_tasks():
                            """Monitor processing tasks and restart if needed."""
                            while True:
                                await asyncio.sleep(5)  # Check every 5 seconds
                                
                                if audio_task.done() and not audio_task.cancelled():
                                    if audio_task.exception():
                                        logger.error(f"Audio task failed: {audio_task.exception()}")
                                        # Could restart task here if needed
                                
                                if stt_task.done() and not stt_task.cancelled():
                                    if stt_task.exception():
                                        logger.error(f"STT task failed: {stt_task.exception()}")
                                        # Could restart task here if needed
                        
                        asyncio.create_task(monitor_tasks())
            
            # Handle existing participants
            for participant in ctx.room.remote_participants.values():
                await handle_participant_audio(participant)
            
            # Handle new participants joining
            @ctx.room.on("participant_connected")
            def on_participant_connected(participant: rtc.RemoteParticipant):
                logger.info(f"🆕 New participant connected: {participant.identity}")
                asyncio.create_task(handle_participant_audio(participant))
            
            # Handle track subscriptions
            @ctx.room.on("track_subscribed")
            def on_track_subscribed(track: rtc.Track, publication: rtc.RemoteTrackPublication, participant: rtc.RemoteParticipant):
                if track.kind == rtc.TrackKind.KIND_AUDIO:
                    logger.info(f"[MICROPHONE] Audio track subscribed from {participant.identity}")
                    asyncio.create_task(handle_participant_audio(participant))
            
            # REP-106 Phase 3: Keep the agent alive and responsive with session monitoring
            conversation_count = 0
            while ctx.room.connection_state == rtc.ConnectionState.CONN_CONNECTED:
                await asyncio.sleep(1)  # Check every second
                
                # Log periodic status
                conversation_count += 1
                if conversation_count % 30 == 0:  # Every 30 seconds
                    logger.info(f"[REP-106] Agent still connected and listening... ({conversation_count}s)")
                    logger.info(f"[REP-106] STT Circuit Breaker: {stt_circuit_breaker.state}, TTS Circuit Breaker: {tts_circuit_breaker.state}")
                    
                    # REP-106 Phase 3: Log session status
                    if session_info:
                        current_session = session_manager.get_session_info(session_info['session_id'])
                        if current_session:
                            participant_count = len(current_session.get('participants', []))
                            logger.info(f"[REP-106] Session status: {session_info['session_id']} - {participant_count} participants")
                        else:
                            logger.warning(f"[REP-106] Session {session_info['session_id']} no longer registered")
                    
                    # Log STT statistics every 30 seconds
                    stt_counters.log_stats()
                    
        except Exception as agent_error:
            logger.error(f"[REP-106] Agent error: {agent_error}")
        finally:
            # REP-106 Phase 3: Clean up session when agent disconnects
            if session_info:
                session_id = session_info['session_id']
                current_session = session_manager.get_session_info(session_id)
                if current_session:
                    current_session['status'] = 'agent_disconnected'
                    current_session['agent_ended_at'] = time.time()
                    logger.info(f"[REP-106] Updated session {session_id} status to agent_disconnected")
                else:
                    logger.warning(f"[REP-106] Session {session_id} not found during cleanup")
            
            logger.info("[REP-106] Agent session ended")
        
    except Exception as e:
        logger.error(f"[DINGBAT] Failed to initialize agent: {e}")
        raise e

async def generate_and_play_tts(text: str, audio_source: rtc.AudioSource, tts_provider):
    """Generate TTS and play audio with circuit breaker protection."""
    try:
        logger.info(f"[LOUD_SOUND] Starting TTS generation for text: '{text}'")
        
        # Generate TTS stream - DO NOT wrap with circuit breaker
        # The stream creation itself should not be protected, only the operations
        tts_stream = tts_provider.stream()
        logger.info(f"[LOUD_SOUND] TTS stream created successfully")
        
        # Push text and end input - these operations can be protected
        # But we need to be careful not to block the actual audio generation
        try:
            tts_stream.push_text(text)
            tts_stream.end_input()
            logger.info(f"[LOUD_SOUND] Text pushed to TTS stream and input ended")
        except Exception as e:
            logger.error(f"[DINGBAT] TTS stream operations failed: {e}")
            # Don't re-raise here - let the audio processing continue
            logger.warning(f"[WARNING] Continuing with TTS audio processing despite input error")
        
        # Track audio frame processing
        frame_count = 0
        total_samples_processed = 0
        
        # Play TTS audio
        logger.info(f"[LOUD_SOUND] Starting TTS audio frame processing...")
        async for synthesized_audio_event in tts_stream:
            frame_count += 1
            logger.debug(f"[LOUD_SOUND] Processing TTS frame #{frame_count}")
            try:
                # Debug: Log the type and attributes of the synthesized audio event
                logger.debug(f"[LOUD_SOUND] TTS Event Type: {type(synthesized_audio_event)}")
                logger.debug(f"[LOUD_SOUND] TTS Event ID: {id(synthesized_audio_event)}")
                
                # Check if this is a SynthesizedAudio event with frame attribute
                if hasattr(synthesized_audio_event, 'frame'):
                    audio_frame = synthesized_audio_event.frame
                    logger.debug(f"[LOUD_SOUND] Found audio frame: {type(audio_frame)}")
                    
                    # Check if the frame is an rtc.AudioFrame
                    if isinstance(audio_frame, rtc.AudioFrame):
                        logger.info(f"[LOUD_SOUND] Frame #{frame_count} is rtc.AudioFrame, capturing directly")
                        await audio_source.capture_frame(audio_frame)
                        total_samples_processed += audio_frame.samples_per_channel
                        logger.info(f"[LOUD_SOUND] Successfully captured audio frame #{frame_count} to audio source")
                        
                        # Log audio frame details for debugging
                        logger.info(f"[LOUD_SOUND] Audio frame details: sample_rate={audio_frame.sample_rate}, "
                                  f"num_channels={audio_frame.num_channels}, "
                                  f"samples_per_channel={audio_frame.samples_per_channel}")
                    else:
                        logger.warning(f"[WARNING] Frame #{frame_count} is not rtc.AudioFrame: {type(audio_frame)}")
                        continue
                        
                elif hasattr(synthesized_audio_event, 'data') and hasattr(synthesized_audio_event, 'sample_rate'):
                    # This might already be an AudioFrame
                    logger.debug("[LOUD_SOUND] Event appears to be an AudioFrame, using directly")
                    await audio_source.capture_frame(synthesized_audio_event)
                    logger.debug("[DINGBAT] Successfully captured audio frame directly")
                else:
                    # Simplified attribute debugging
                    audio_attrs = {}
                    key_attrs = ['data', 'sample_rate', 'num_channels', 'frame']
                    for attr in key_attrs:
                        if hasattr(synthesized_audio_event, attr):
                            try:
                                value = getattr(synthesized_audio_event, attr)
                                if not callable(value):
                                    audio_attrs[attr] = type(value).__name__
                            except Exception as e:
                                audio_attrs[attr] = f"Error: {e}"
                    
                    logger.debug(f"[LOUD_SOUND] TTS Event Key Attributes: {audio_attrs}")
                    
                    # Try different possible attribute names for audio data
                    audio_data = None
                    sample_rate = None
                    num_channels = None
                    
                    # Common attribute names to try for audio data
                    data_attrs = ['data', 'audio', 'audio_data', 'raw_data', 'pcm_data']
                    for attr in data_attrs:
                        if hasattr(synthesized_audio_event, attr):
                            try:
                                audio_data = getattr(synthesized_audio_event, attr)
                                logger.debug(f"[DINGBAT] Found audio data in attribute: {attr}")
                                break
                            except Exception as e:
                                logger.debug(f"[DINGBAT] Failed to get audio data from {attr}: {e}")
                    
                    # Common attribute names for sample rate
                    rate_attrs = ['sample_rate', 'sampleRate', 'rate', 'frequency']
                    for attr in rate_attrs:
                        if hasattr(synthesized_audio_event, attr):
                            try:
                                sample_rate = getattr(synthesized_audio_event, attr)
                                logger.debug(f"[DINGBAT] Found sample rate in attribute: {attr} = {sample_rate}")
                                break
                            except Exception as e:
                                logger.debug(f"[DINGBAT] Failed to get sample rate from {attr}: {e}")
                    
                    # Common attribute names for channels
                    channel_attrs = ['num_channels', 'numChannels', 'channels', 'channel_count']
                    for attr in channel_attrs:
                        if hasattr(synthesized_audio_event, attr):
                            try:
                                num_channels = getattr(synthesized_audio_event, attr)
                                logger.debug(f"[DINGBAT] Found num_channels in attribute: {attr} = {num_channels}")
                                break
                            except Exception as e:
                                logger.debug(f"[DINGBAT] Failed to get num_channels from {attr}: {e}")
                    
                    if audio_data is not None:
                        # We found audio data, create AudioFrame
                        # Check if audio_data has len() method before calling it
                        try:
                            data_length = len(audio_data) if hasattr(audio_data, '__len__') else 0
                            logger.debug(f"[LOUD_SOUND] Creating AudioFrame with data length: {data_length}")
                        except Exception as e:
                            logger.warning(f"[WARNING] Could not get length of audio_data: {e}")
                            data_length = 0
                        
                        # Use defaults if we couldn't find the attributes
                        if sample_rate is None:
                            sample_rate = 24000  # Default sample rate
                            logger.warning(f"[WARNING] Using default sample rate: {sample_rate}")
                        
                        if num_channels is None:
                            num_channels = 1  # Default mono
                            logger.warning(f"[WARNING] Using default num_channels: {num_channels}")
                        
                        # Calculate samples per channel only if we have valid data length
                        if data_length > 0:
                            samples_per_channel = data_length // (num_channels * 2)  # 2 bytes per sample for 16-bit
                            
                            audio_frame = rtc.AudioFrame(
                                data=audio_data,
                                sample_rate=sample_rate,
                                num_channels=num_channels,
                                samples_per_channel=samples_per_channel
                            )
                            await audio_source.capture_frame(audio_frame)
                            logger.debug(f"[DINGBAT] Successfully captured audio frame: {samples_per_channel} samples")
                        else:
                            logger.warning("[WARNING] Skipping audio frame with no valid data")
                    else:
                        logger.error(f"[DINGBAT] Could not find audio data in synthesized audio event")
                        logger.debug(f"[DINGBAT] Available attributes: {list(audio_attrs.keys())}")
                    
            except Exception as audio_error:
                logger.error(f"[DINGBAT] Error processing TTS audio event: {audio_error}")
                continue  # Continue with next audio event
        
        # Log completion summary
        logger.info(f"[LOUD_SOUND] TTS processing completed: {frame_count} audio frames processed and sent to audio source")
        logger.info(f"[LOUD_SOUND] Total samples processed: {total_samples_processed}")
        logger.info(f"[LOUD_SOUND] Audio source details: sample_rate={audio_source.sample_rate}, num_channels={audio_source.num_channels}")
        
        # Verify audio was actually published to LiveKit track
        if frame_count > 0:
            logger.info(f"[LOUD_SOUND] ✅ TTS audio successfully generated and published to LiveKit audio track")
            logger.info(f"[LOUD_SOUND] Users should now hear the voice response")
        else:
            logger.error(f"[DINGBAT] ❌ No TTS audio frames were processed - users will not hear voice response")
            
    except Exception as e:
        logger.error(f"TTS generation/playback error: {e}")
        raise e

# CLI Entry Point
if __name__ == "__main__":
    # Start health check server in background
    health_thread = threading.Thread(target=start_health_server, daemon=True)
    health_thread.start()
    
    # Give the health server time to start
    time.sleep(2)
    logger.info("Health check server started on port 8080")
    logger.info("PYTHON AGENT MAIN STARTUP - WAITING FOR ROOMS")
    
    # Start the LiveKit agent
    from livekit.agents import WorkerOptions
    logger.info("STARTING LIVEKIT AGENT WITH WORKER OPTIONS")
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))