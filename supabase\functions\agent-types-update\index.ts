import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';
import { withAuth, AuthenticatedRequest } from '../shared/middleware/auth.ts';
import { Permissions } from '../shared/rbac.ts';

// Define the expected schema for agent type update
interface AgentTypeUpdatePayload {
  id: string;
  name?: string;
  description?: string;
  category?: string;
  configuration?: Record<string, unknown>;
  // Configuration fields that might come directly in the payload
  configuration_schema?: Record<string, unknown>;
  runtime_context_schema?: Record<string, unknown>;
  knowledge_source_config_schema?: Record<string, unknown>;
  human_in_the_loop_schema?: Record<string, unknown>;
  default_config_values?: Record<string, unknown>;
  presentation_config?: Record<string, unknown>;
  available_channels?: string[];
  avatar_type?: string;
  mastra_agent_id?: string;
  mastra_api_base_url?: string;
  capabilities?: string[];
  agent_operational_mode?: string;
  trigger_events?: string[];
  // Legacy fields
  customizable_parameters?: Record<string, unknown>;
  voice_config?: Record<string, unknown>;
  applicable_metrics?: Record<string, unknown>;
  chat_ui_settings?: Record<string, unknown>;
  phone_settings?: Record<string, unknown>;
  static_overrides?: Record<string, unknown>;
  presentation_overrides?: Record<string, unknown>;
  // Other fields
  pricing?: Record<string, unknown>;
  version?: string;
  status?: string;
  is_public?: boolean;
}

// Required configuration fields that should always be present
interface RequiredConfigFields {
  presentation_config: Record<string, unknown>;
  available_channels: string[];
  avatar_type: string;
  capabilities: string[];
  agent_operational_mode: string;
}

serve(
  withAuth(async (req: AuthenticatedRequest) => {
    console.log('agent-types-update: Request received');
    console.log('agent-types-update: Request method:', req.method);
    
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
      console.log('agent-types-update: Handling OPTIONS request');
      return new Response('ok', { headers: corsHeaders });
    }

    // Only allow PUT and POST requests for updates
    if (req.method !== 'PUT' && req.method !== 'POST') {
      console.log('agent-types-update: Method not allowed:', req.method);
      return new Response(
        JSON.stringify({ error: 'Method not allowed. Use PUT or POST for agent type updates.' }),
        {
          status: 405,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    try {
      // Get the authenticated user from the request context
      const user = req.user;
      console.log('agent-types-update: User from request:', user ? JSON.stringify({
        id: user.id,
        organization_id: user.organization_id,
        role: user.role,
        isHouseRole: user.isHouseRole
      }) : 'null');

      // Check if user has house role or admin permissions to update agent types
      const isAdmin = ['house_admin', 'house_manager'].includes(user.role);
      if (!isAdmin && !user.isHouseRole) {
        console.error('agent-types-update: Permission denied. User role:', user.role);
        return new Response(
          JSON.stringify({
            error: 'Permission denied',
            details: 'You do not have permission to update agent types. This action requires house admin privileges.'
          }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Parse request body
      let payload: AgentTypeUpdatePayload;
      try {
        payload = await req.json();
        console.log('agent-types-update: Request payload received:', JSON.stringify(payload));
      } catch (e) {
        console.error('agent-types-update: Failed to parse request body:', e);
        return new Response(
          JSON.stringify({
            error: 'Invalid request body',
            details: 'The request body could not be parsed as valid JSON.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Validate required fields
      if (!payload.id) {
        console.error('agent-types-update: Missing required ID field in payload');
        return new Response(
          JSON.stringify({
            error: 'Missing required field',
            details: 'Agent ID is required'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      } else {
        console.log('agent-types-update: Valid ID found in payload:', payload.id);
      }

      // Validate other fields
      const validationErrors: string[] = [];
      
      if (payload.name !== undefined && (typeof payload.name !== 'string' || payload.name.length > 255)) {
        validationErrors.push('Name must be a string and cannot exceed 255 characters');
      }
      
      if (payload.description !== undefined && typeof payload.description !== 'string') {
        validationErrors.push('Description must be a string');
      }
      
      if (payload.category !== undefined && (typeof payload.category !== 'string' || payload.category.length > 100)) {
        validationErrors.push('Category must be a string and cannot exceed 100 characters');
      }

      if (payload.status !== undefined && !['draft', 'published', 'archived'].includes(payload.status)) {
        validationErrors.push('Status must be one of: draft, published, archived');
      }

      if (payload.pricing !== undefined) {
        if (typeof payload.pricing !== 'object') {
          validationErrors.push('Pricing must be an object');
        } else if (!("credits_per_completion_token" in payload.pricing)) {
          validationErrors.push('Pricing must include credits_per_completion_token');
        }
      }

      if (validationErrors.length > 0) {
        console.error('agent-types-update: Validation errors for agent ID:', payload.id);
        console.error('agent-types-update: Validation details:', JSON.stringify({
          errors: validationErrors,
          payload: {
            id: payload.id,
            name: payload.name,
            description: typeof payload.description,
            status: payload.status,
            pricing: payload.pricing ? JSON.stringify(payload.pricing) : 'undefined'
          }
        }));
        
        return new Response(
          JSON.stringify({
            error: 'Validation failed',
            details: validationErrors
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      } else {
        console.log('agent-types-update: Validation passed for agent ID:', payload.id);
      }

      // Create Supabase client
      const authHeader = req.headers.get('Authorization');
      const supabaseClient = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_ANON_KEY') ?? '',
        {
          global: {
            headers: { Authorization: authHeader || '' },
          },
        }
      );

      // First check if agent with this ID exists
      const { data: existingAgent, error: checkError } = await supabaseClient
        .from('agents')
        .select('id, status')
        .eq('id', payload.id)
        .single();

      if (checkError) {
        console.error('agent-types-update: Error checking existing agent:', checkError);
        
        const status = checkError.code === 'PGRST116' ? 404 : 500; // PGRST116 = no rows returned
        const message = status === 404 
          ? `Agent type with ID "${payload.id}" not found` 
          : 'Database error: ' + checkError.message;
        
        return new Response(
          JSON.stringify({ 
            error: status === 404 ? 'Not Found' : 'Database Error', 
            details: message 
          }),
          {
            status,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      if (!existingAgent) {
        console.error(`agent-types-update: Agent with ID "${payload.id}" not found`);
        return new Response(
          JSON.stringify({ 
            error: 'Not Found', 
            details: `Agent type with ID "${payload.id}" not found` 
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Prepare update data, only including fields that were provided
      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString()
      };
      
      if (payload.name !== undefined) updateData.name = payload.name;
      if (payload.description !== undefined) updateData.description = payload.description;
      if (payload.category !== undefined) updateData.category = payload.category;
      if (payload.version !== undefined) updateData.version = payload.version;
      if (payload.status !== undefined) updateData.status = payload.status;
      if (payload.is_public !== undefined) updateData.is_public = payload.is_public;
      if (payload.pricing !== undefined) updateData.pricing = payload.pricing;
      // Store mastra_agent_id in dedicated column
      if (payload.mastra_agent_id !== undefined) updateData.mastra_agent_id = payload.mastra_agent_id;
      // Store mastra_api_base_url in dedicated column
      if (payload.mastra_api_base_url !== undefined) updateData.mastra_api_base_url = payload.mastra_api_base_url;
      
      // Get existing agent data to merge configuration properly
      const { data: existingAgentData, error: fetchError } = await supabaseClient
        .from('agents')
        .select('configuration')
        .eq('id', payload.id)
        .single();
        
      if (fetchError) {
        console.error('agent-types-update: Error fetching existing agent configuration:', fetchError);
        return new Response(
          JSON.stringify({
            error: 'Failed to fetch existing agent configuration',
            details: fetchError.message
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }
      
      // Handle configuration data
      const existingConfiguration: Record<string, unknown> =
        (existingAgentData?.configuration as Record<string, unknown>) || {};
      
      // Ensure we're working with a deep clone of the existing configuration
      const safeExistingConfig = JSON.parse(JSON.stringify(existingConfiguration));
      
      // Log existing configuration for debugging
      console.log('agent-types-update: Existing configuration:', JSON.stringify({
        fields: Object.keys(safeExistingConfig),
        has_presentation_config: !!safeExistingConfig.presentation_config,
        has_available_channels: !!safeExistingConfig.available_channels,
      }));
      
      // Create a deep clone of the existing configuration to work with
      let updatedConfiguration: Record<string, unknown> = JSON.parse(JSON.stringify(safeExistingConfig));
      
      // If the entire configuration object is provided, merge it with existing instead of replacing entirely
      if (payload.configuration !== undefined) {
        console.log('agent-types-update: Merging complete configuration object from payload with existing configuration');
        
        // Create a deep clone of the payload configuration
        const payloadConfig = JSON.parse(JSON.stringify(payload.configuration));
        
        // CRITICAL FIX: Extract mastra fields from configuration and store in dedicated columns
        // These fields should NOT be stored in the configuration JSONB
        if (payloadConfig.mastra_agent_id !== undefined) {
          console.log('agent-types-update: Extracting mastra_agent_id from configuration to dedicated column');
          updateData.mastra_agent_id = payloadConfig.mastra_agent_id;
          delete payloadConfig.mastra_agent_id; // Remove from configuration object
        }
        
        if (payloadConfig.mastra_api_base_url !== undefined) {
          console.log('agent-types-update: Extracting mastra_api_base_url from configuration to dedicated column');
          updateData.mastra_api_base_url = payloadConfig.mastra_api_base_url;
          delete payloadConfig.mastra_api_base_url; // Remove from configuration object
        }
        
        // Merge with existing configuration using a structure-aware approach
        updatedConfiguration = {
          ...updatedConfiguration,
          ...payloadConfig
        };
        
        // Special handling for complex nested objects to ensure proper merging
        
        // Ensure required fields are preserved even if they aren't in the payload
        if (!updatedConfiguration.presentation_config && safeExistingConfig.presentation_config) {
          console.log('agent-types-update: Preserving existing presentation_config that was missing in payload');
          updatedConfiguration.presentation_config = JSON.parse(JSON.stringify(safeExistingConfig.presentation_config));
        }
        
        if (!updatedConfiguration.available_channels && safeExistingConfig.available_channels) {
          console.log('agent-types-update: Preserving existing available_channels that was missing in payload');
          updatedConfiguration.available_channels = Array.isArray(safeExistingConfig.available_channels) ?
            [...safeExistingConfig.available_channels] : [];
        }
      } else {
        // Otherwise, update individual fields in the configuration
        console.log('agent-types-update: Updating individual configuration fields');
        
        // Process schema fields with proper deep cloning to prevent reference issues
        if (payload.configuration_schema !== undefined) {
          try {
            updatedConfiguration.configuration_schema = JSON.parse(JSON.stringify(payload.configuration_schema));
            console.log('agent-types-update: Updated configuration_schema');
          } catch (e) {
            console.error('agent-types-update: Error processing configuration_schema:', e);
          }
        }
          
        if (payload.runtime_context_schema !== undefined) {
          try {
            updatedConfiguration.runtime_context_schema = JSON.parse(JSON.stringify(payload.runtime_context_schema));
          } catch (e) {
            console.error('agent-types-update: Error processing runtime_context_schema:', e);
          }
        }
          
        if (payload.knowledge_source_config_schema !== undefined) {
          try {
            updatedConfiguration.knowledge_source_config_schema = JSON.parse(JSON.stringify(payload.knowledge_source_config_schema));
          } catch (e) {
            console.error('agent-types-update: Error processing knowledge_source_config_schema:', e);
          }
        }
          
        if (payload.human_in_the_loop_schema !== undefined) {
          try {
            updatedConfiguration.human_in_the_loop_schema = JSON.parse(JSON.stringify(payload.human_in_the_loop_schema));
          } catch (e) {
            console.error('agent-types-update: Error processing human_in_the_loop_schema:', e);
          }
        }
          
        if (payload.default_config_values !== undefined) {
          try {
            updatedConfiguration.default_config_values = JSON.parse(JSON.stringify(payload.default_config_values));
          } catch (e) {
            console.error('agent-types-update: Error processing default_config_values:', e);
          }
        }
          
        if (payload.presentation_config !== undefined) {
          try {
            updatedConfiguration.presentation_config = JSON.parse(JSON.stringify(payload.presentation_config));
          } catch (e) {
            console.error('agent-types-update: Error processing presentation_config:', e);
          }
        }
          
        // Process array fields
        if (payload.available_channels !== undefined) {
          updatedConfiguration.available_channels = Array.isArray(payload.available_channels) ?
            [...payload.available_channels] : [];
        }
          
        if (payload.avatar_type !== undefined) {
          updatedConfiguration.avatar_type = payload.avatar_type;
        }
          
        // CRITICAL FIX: Handle mastra_agent_id extraction for individual field updates
        // mastra_agent_id should be stored in dedicated column, not in configuration JSONB
        if (payload.mastra_agent_id !== undefined) {
          console.log('agent-types-update: Extracting mastra_agent_id from individual field to dedicated column');
          updateData.mastra_agent_id = payload.mastra_agent_id;
          // Do NOT store in configuration JSONB
        }
        
        // NOTE: mastra_api_base_url is now stored in dedicated column, not in configuration JSONB
        // It will be handled separately in the updateData object
          
        if (payload.capabilities !== undefined) {
          updatedConfiguration.capabilities = Array.isArray(payload.capabilities) ?
            [...payload.capabilities] : [];
        }
          
        if (payload.agent_operational_mode !== undefined) {
          updatedConfiguration.agent_operational_mode = payload.agent_operational_mode;
        }
          
        if (payload.trigger_events !== undefined) {
          updatedConfiguration.trigger_events = Array.isArray(payload.trigger_events) ?
            [...payload.trigger_events] : [];
        }
          
        // Process legacy object fields with proper cloning
        if (payload.customizable_parameters !== undefined) {
          try {
            updatedConfiguration.customizable_parameters = JSON.parse(JSON.stringify(payload.customizable_parameters));
          } catch (e) {
            console.error('agent-types-update: Error processing customizable_parameters:', e);
          }
        }
          
        if (payload.voice_config !== undefined) {
          try {
            updatedConfiguration.voice_config = JSON.parse(JSON.stringify(payload.voice_config));
          } catch (e) {
            console.error('agent-types-update: Error processing voice_config:', e);
          }
        }
          
        if (payload.applicable_metrics !== undefined) {
          try {
            updatedConfiguration.applicable_metrics = JSON.parse(JSON.stringify(payload.applicable_metrics));
          } catch (e) {
            console.error('agent-types-update: Error processing applicable_metrics:', e);
          }
        }
          
        if (payload.chat_ui_settings !== undefined) {
          try {
            updatedConfiguration.chat_ui_settings = JSON.parse(JSON.stringify(payload.chat_ui_settings));
          } catch (e) {
            console.error('agent-types-update: Error processing chat_ui_settings:', e);
          }
        }
          
        if (payload.phone_settings !== undefined) {
          try {
            updatedConfiguration.phone_settings = JSON.parse(JSON.stringify(payload.phone_settings));
          } catch (e) {
            console.error('agent-types-update: Error processing phone_settings:', e);
          }
        }
          
        if (payload.static_overrides !== undefined) {
          try {
            updatedConfiguration.static_overrides = JSON.parse(JSON.stringify(payload.static_overrides));
          } catch (e) {
            console.error('agent-types-update: Error processing static_overrides:', e);
          }
        }
          
        if (payload.presentation_overrides !== undefined) {
          try {
            updatedConfiguration.presentation_overrides = JSON.parse(JSON.stringify(payload.presentation_overrides));
          } catch (e) {
            console.error('agent-types-update: Error processing presentation_overrides:', e);
          }
        }
      }
      
      // Ensure required fields have at least default values if they're missing
      if (!updatedConfiguration.presentation_config) {
        console.log('agent-types-update: Setting default presentation_config');
        updatedConfiguration.presentation_config = {};
      }
      
      if (!updatedConfiguration.available_channels) {
        console.log('agent-types-update: Setting default available_channels');
        updatedConfiguration.available_channels = [];
      }
      
      if (!updatedConfiguration.avatar_type) {
        console.log('agent-types-update: Setting default avatar_type');
        updatedConfiguration.avatar_type = 'default';
      }
      
      if (!updatedConfiguration.capabilities) {
        console.log('agent-types-update: Setting default capabilities');
        updatedConfiguration.capabilities = [];
      }
      
      if (!updatedConfiguration.agent_operational_mode) {
        console.log('agent-types-update: Setting default agent_operational_mode');
        updatedConfiguration.agent_operational_mode = 'interactive_user_facing';
      }
      
      // Validate the configuration structure
      const missingFields: string[] = [];
      const requiredConfigFields: Array<keyof RequiredConfigFields> = [
        'presentation_config', 'available_channels', 'avatar_type',
        'capabilities', 'agent_operational_mode'
      ];
      
      for (const field of requiredConfigFields) {
        if (updatedConfiguration[field] === undefined) {
          missingFields.push(field);
        }
      }
      
      if (missingFields.length > 0) {
        console.error('agent-types-update: Missing required configuration fields:', missingFields);
        return new Response(
          JSON.stringify({
            error: 'Missing required configuration fields',
            details: `The following required configuration fields are missing: ${missingFields.join(', ')}`
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }
      
      // Log configuration data before saving
      console.log('agent-types-update: Configuration fields being saved:', JSON.stringify({
        fields: Object.keys(updatedConfiguration),
        has_presentation_config: !!updatedConfiguration.presentation_config,
        has_available_channels: Array.isArray(updatedConfiguration.available_channels),
        diff_from_original: Object.keys(updatedConfiguration).filter(k =>
          JSON.stringify(updatedConfiguration[k]) !== JSON.stringify(existingConfiguration[k])
        )
      }));
      
      // Only update configuration if it has changed
      const configHasChanged = JSON.stringify(updatedConfiguration) !== JSON.stringify(existingConfiguration);
      if (configHasChanged) {
        console.log('agent-types-update: Configuration has changed, updating');
        updateData.configuration = updatedConfiguration;
      } else {
        console.log('agent-types-update: Configuration unchanged');
      }

      // Update the agent type
      console.log(`agent-types-update: Updating agent type with ID "${payload.id}" with fields:`, Object.keys(updateData));
      
      // Log full update data for debugging (excluding large objects)
      console.log('agent-types-update: Update data:', JSON.stringify({
        fields: Object.keys(updateData),
        has_configuration: !!updateData.configuration,
        configuration_fields: updateData.configuration ? Object.keys(updateData.configuration) : []
      }));
      const { data: updatedAgent, error: updateError } = await supabaseClient
        .from('agents')
        .update(updateData)
        .eq('id', payload.id)
        .select('*')
        .single();

      if (updateError) {
        console.error('agent-types-update: Error updating agent:', updateError);
        return new Response(
          JSON.stringify({ 
            error: 'Failed to update agent type', 
            details: updateError.message 
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Validate the response structure before sending it back
      if (!updatedAgent || !updatedAgent.configuration) {
        console.error('agent-types-update: Invalid agent response structure', {
          has_agent: !!updatedAgent,
          has_configuration: updatedAgent ? !!updatedAgent.configuration : false
        });
        
        return new Response(
          JSON.stringify({
            error: 'Invalid agent response structure',
            details: 'The updated agent does not have the expected structure'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }
      
      // Verify required configuration fields are present in the response
      const agentConfig = updatedAgent.configuration as Record<string, unknown>;
      const missingResponseFields: string[] = [];
      
      if (!agentConfig.presentation_config) missingResponseFields.push('presentation_config');
      if (!agentConfig.available_channels) missingResponseFields.push('available_channels');
      
      if (missingResponseFields.length > 0) {
        console.error('agent-types-update: Missing required fields in response', {
          missing_fields: missingResponseFields,
          config_keys: Object.keys(agentConfig)
        });
        
        // We'll still return the agent but log the issue
        console.warn('agent-types-update: Returning agent despite missing fields');
      }
      
      // Log success with configuration details
      console.log('agent-types-update: Successfully updated agent with configuration fields:',
        Object.keys(updatedAgent.configuration as Record<string, unknown>));
      
      // Return success response with updated agent
      return new Response(
        JSON.stringify({
          data: updatedAgent,
          message: `Successfully updated agent type "${payload.id}"`,
          metadata: {
            updated_at: new Date().toISOString(),
            configuration_fields: Object.keys(updatedAgent.configuration as Record<string, unknown>)
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );

    } catch (error) {
      console.error('agent-types-update: Unexpected error:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : 'No stack trace available',
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        timestamp: new Date().toISOString()
      });
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      return new Response(
        JSON.stringify({
          error: `Internal Server Error: ${errorMessage}`,
          details: error instanceof Error ? error.stack : 'No stack trace available',
          requestId: crypto.randomUUID(), // Add a unique identifier for tracking this error
          timestamp: new Date().toISOString()
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }
  }, { requiredPermission: Permissions.MANAGE_AGENTS }) // Require agent management permission
);