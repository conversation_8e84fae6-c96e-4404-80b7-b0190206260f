import React, { useState, useEffect } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotChat } from '@copilotkit/react-ui';
import '@copilotkit/react-ui/styles.css';
import { RepRoomManager, AgentConfig } from './RepRoomManager';
import { LiveKitRoom } from '@livekit/components-react';
import { RepRoomUIWithTurnDetection } from './RepRoomUIWithTurnDetection';
import { Bot, Loader2 } from 'lucide-react';

interface CopilotKitRepRoomInterfaceProps {
  repRoomSlug: string;
  token?: string;
  livekitUrl?: string;
}

export function CopilotKitRepRoomInterface({ 
  repRoomSlug, 
  token, 
  livekitUrl 
}: CopilotKitRepRoomInterfaceProps) {
  const [hasStarted, setHasStarted] = useState(false);

  return (
    <RepRoomManager repRoomSlug={repRoomSlug}>
      {(config, loading, error) => {
        if (loading) {
          return (
            <div className="flex h-screen items-center justify-center bg-gray-900">
              <div className="text-center text-white">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                <p>Loading agent configuration...</p>
              </div>
            </div>
          );
        }

        if (error || !config) {
          return (
            <div className="flex h-screen items-center justify-center bg-gray-900">
              <div className="text-center text-red-400 p-8 max-w-md">
                <h2 className="text-xl font-semibold mb-4">Configuration Error</h2>
                <p className="mb-4">{error || 'Failed to load agent configuration'}</p>
                <button 
                  onClick={() => window.location.reload()} 
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          );
        }

        // Extract the agent ID from the blueprint ID (e.g., "keywordResearchAgent")
        const agentId = config.agent.blueprintId;

        // Handle browser autoplay policies
        if (!hasStarted) {
          return (
            <div className="flex h-screen items-center justify-center bg-gray-900">
              <div className="text-center">
                <div className="mb-6">
                  <Bot className="w-16 h-16 text-blue-500 mx-auto mb-4" />
                  <h1 className="text-3xl font-bold text-white mb-2">
                    {config.repRoom.title}
                  </h1>
                  <p className="text-gray-400 max-w-md">
                    {config.repRoom.introductionText}
                  </p>
                  <div className="mt-4 text-sm text-gray-500">
                    <p>Agent: {config.agent.displayName}</p>
                    <p>Mode: {config.agent.operationalMode}</p>
                  </div>
                </div>
                
                <button 
                  onClick={() => setHasStarted(true)} 
                  className="px-8 py-4 bg-blue-600 text-white rounded-lg text-xl font-semibold hover:bg-blue-700 transition-colors"
                >
                  Start Conversation
                </button>
              </div>
            </div>
          );
        }

        // If we have a voice token, wrap with LiveKit
        if (token && livekitUrl) {
          return (
            <CopilotKit
              runtimeUrl={config.agent.mastraApiBaseUrl}
              agent={agentId}
              publicApiKey="disabled" // Disable branding
            >
              <LiveKitRoom
                token={token}
                serverUrl={livekitUrl}
                connect={true}
                audio={true}
                video={false}
                options={{
                  adaptiveStream: true,
                  dynacast: true,
                }}
                onDisconnected={() => console.log('Disconnected from LiveKit room')}
              >
                <div className="h-screen">
                  {/* Enhanced Voice UI with Turn Detection */}
                  <RepRoomUIWithTurnDetection roomData={{
                    id: config.repRoom.id,
                    slug: config.repRoom.slug,
                    title: config.repRoom.title,
                    introductionText: config.repRoom.introductionText,
                    agent_clones: {
                      id: config.agent.cloneId,
                      name: config.agent.displayName,
                      avatar: '',
                      voices: {
                        provider: config.voice.ttsProvider,
                        voice_id: config.voice.ttsVoiceId
                      }
                    }
                  }} />
                </div>
              </LiveKitRoom>
            </CopilotKit>
          );
        }

        // Text-only mode (no voice)
        return (
          <CopilotKit
            runtimeUrl={config.agent.mastraApiBaseUrl}
            agent={agentId}
            publicApiKey="disabled" // Disable branding
          >
            <div className="h-screen flex flex-col bg-gray-900">
              <div className="bg-gray-800 p-4 border-b border-gray-700">
                <div className="max-w-4xl mx-auto">
                  <h1 className="text-2xl font-bold text-white">{config.repRoom.title}</h1>
                  <p className="text-gray-400 mt-1">{config.repRoom.introductionText}</p>
                </div>
              </div>
              
              <div className="flex-1 max-w-4xl mx-auto w-full">
                <CopilotChat
                  className="h-full"
                  instructions={`You are ${config.agent.displayName}, an AI assistant helping visitors in this rep room. ${config.repRoom.introductionText}`}
                  labels={{
                    title: config.agent.displayName,
                    initial: `Welcome! ${config.repRoom.introductionText}`
                  }}
                />
              </div>
            </div>
          </CopilotKit>
        );
      }}
    </RepRoomManager>
  );
}