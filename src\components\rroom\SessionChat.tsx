import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useUnifiedVoice, VoiceMessage } from '../../contexts/rroom/UnifiedVoiceContext';
import {
  createChatMessage,
  ParticipantInfo,
  ChatMessage as ChatMessageType
} from '../../types/DataChannelMessages';
import { useSupabase } from '../../contexts/supabase-context';

// Event data interfaces for type safety
interface ChatMessageEventData {
  text: string;
  sender?: {
    name: string;
    isAgent?: boolean;
  };
}

interface SystemEventData {
  event: string;
  participant?: ParticipantInfo;
  message?: string;
}

interface TranscriptionEventData {
  text: string;
  speaker?: {
    name: string;
    isAgent?: boolean;
  };
}

interface SessionChatProps {
  sessionId: string;
  currentUser?: ParticipantInfo;
  className?: string;
}

interface ChatDisplayMessage {
  id: string;
  content: string;
  sender: {
    name: string;
    isAgent: boolean;
  };
  timestamp: number;
  type: 'chat' | 'system' | 'transcript' | 'agent_response';
}

// Database message interface for type safety
interface DatabaseChatMessage {
  message_id: string;
  session_id: string;
  sender_identity: string;
  sender_name: string;
  content: string;
  message_type: string;
  timestamp: string;
  metadata: Record<string, unknown>;
}

/**
 * SessionChat Component - Phase 4: Real-time Features & Data Channels
 * 
 * Features:
 * - Real-time chat synchronized across all participants
 * - Display messages with participant names
 * - Persist chat history in session
 * - Support text, system messages, and agent responses
 * - Integration with voice transcripts
 */
export function SessionChat({ sessionId, currentUser, className = '' }: SessionChatProps) {
  const { state, controls, isVoiceActive } = useUnifiedVoice();
  const supabase = useSupabase();
  const [chatMessages, setChatMessages] = useState<ChatDisplayMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [historyLoaded, setHistoryLoaded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Load chat history from database
  const loadChatHistory = useCallback(async () => {
    if (!sessionId || historyLoaded) return;

    setIsLoadingHistory(true);
    try {
      // Use hardcoded Supabase URL and key (same as in client.ts)
      const supabaseUrl = "https://kjkehonxatogcwrybslr.supabase.co";
      const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtqa2Vob254YXRvZ2N3cnlic2xyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzMTU4NDEsImV4cCI6MjA2MDg5MTg0MX0.Pca0AIGyKFwTCPvx1Egn11QXloji--4oOQ_XMfYwWxw";
      
      const response = await fetch(`${supabaseUrl}/functions/v1/save-chat-message?session_id=${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          const historyMessages: ChatDisplayMessage[] = result.data.map((msg: DatabaseChatMessage) => ({
            id: msg.message_id,
            content: msg.content,
            sender: {
              name: msg.sender_name || 'Unknown',
              isAgent: msg.sender_identity?.includes('agent') || false
            },
            timestamp: new Date(msg.timestamp).getTime(),
            type: msg.message_type as ChatDisplayMessage['type'] || 'chat'
          }));

          setChatMessages(prev => {
            // Merge history with existing messages, avoiding duplicates
            const existingIds = new Set(prev.map(msg => msg.id));
            const newMessages = historyMessages.filter(msg => !existingIds.has(msg.id));
            return [...newMessages, ...prev].sort((a, b) => a.timestamp - b.timestamp);
          });
        }
      }
    } catch (error) {
      console.error('[SessionChat] Failed to load chat history:', error);
    } finally {
      setIsLoadingHistory(false);
      setHistoryLoaded(true);
    }
  }, [sessionId, historyLoaded]);

  // Save chat message to database
  const saveChatMessage = useCallback(async (message: ChatDisplayMessage) => {
    if (!sessionId) return;

    try {
      // Use hardcoded Supabase URL and key (same as in client.ts)
      const supabaseUrl = "https://kjkehonxatogcwrybslr.supabase.co";
      const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtqa2Vob254YXRvZ2N3cnlic2xyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzMTU4NDEsImV4cCI6MjA2MDg5MTg0MX0.Pca0AIGyKFwTCPvx1Egn11QXloji--4oOQ_XMfYwWxw";
      
      const response = await fetch(`${supabaseUrl}/functions/v1/save-chat-message`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          message_id: message.id,
          sender_identity: currentUser?.id || 'unknown',
          sender_name: message.sender.name,
          content: message.content,
          message_type: message.type,
          timestamp: new Date(message.timestamp).toISOString(),
          metadata: {
            isAgent: message.sender.isAgent
          }
        }),
      });

      if (!response.ok) {
        console.error('[SessionChat] Failed to save chat message:', await response.text());
      }
    } catch (error) {
      console.error('[SessionChat] Error saving chat message:', error);
    }
  }, [sessionId, currentUser]);

  // Load chat history on component mount
  useEffect(() => {
    loadChatHistory();
  }, [loadChatHistory]);

  // Convert voice messages to chat display format
  const convertVoiceMessageToChatMessage = useCallback((voiceMessage: VoiceMessage): ChatDisplayMessage => {
    return {
      id: voiceMessage.id,
      content: voiceMessage.content,
      sender: {
        name: voiceMessage.role === 'assistant' ? 'Agent' : currentUser?.name || 'User',
        isAgent: voiceMessage.role === 'assistant'
      },
      timestamp: voiceMessage.timestamp,
      type: voiceMessage.role === 'assistant' ? 'agent_response' : 'transcript'
    };
  }, [currentUser]);

  // Update chat messages from voice context
  useEffect(() => {
    const voiceMessages = state.visibleMessages || [];
    const convertedMessages = voiceMessages.map(convertVoiceMessageToChatMessage);
    setChatMessages(prev => {
      // Merge with existing chat messages, avoiding duplicates
      const existingIds = new Set(prev.map(msg => msg.id));
      const newMessages = convertedMessages.filter(msg => !existingIds.has(msg.id));
      return [...prev, ...newMessages].sort((a, b) => a.timestamp - b.timestamp);
    });
  }, [state.visibleMessages, convertVoiceMessageToChatMessage]);

  // Listen for chat-specific events
  useEffect(() => {
    const handleChatMessage = (data: unknown) => {
      const chatData = data as ChatMessageEventData;
      console.log('[SessionChat] Chat message received:', data);
      
      const chatMessage: ChatDisplayMessage = {
        id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: chatData.text,
        sender: {
          name: chatData.sender?.name || 'Unknown',
          isAgent: chatData.sender?.isAgent || false
        },
        timestamp: Date.now(),
        type: 'chat'
      };
      
      setChatMessages(prev => [...prev, chatMessage]);
    };

    const handleSystemEvent = (data: unknown) => {
      const systemData = data as SystemEventData;
      console.log('[SessionChat] System event received:', data);
      
      if (systemData.event === 'participant_joined' || systemData.event === 'participant_left') {
        const systemMessage: ChatDisplayMessage = {
          id: `system_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          content: systemData.message || `${systemData.participant?.name || 'Someone'} ${systemData.event === 'participant_joined' ? 'joined' : 'left'} the session`,
          sender: {
            name: 'System',
            isAgent: false
          },
          timestamp: Date.now(),
          type: 'system'
        };
        
        setChatMessages(prev => [...prev, systemMessage]);
      }
    };

    const handleTranscriptionReceived = (data: unknown) => {
      const transcriptData = data as TranscriptionEventData;
      console.log('[SessionChat] Transcription received:', data);
      
      const transcriptMessage: ChatDisplayMessage = {
        id: `transcript_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: transcriptData.text,
        sender: {
          name: transcriptData.speaker?.name || (transcriptData.speaker?.isAgent ? 'Agent' : 'User'),
          isAgent: transcriptData.speaker?.isAgent || false
        },
        timestamp: Date.now(),
        type: 'transcript'
      };
      
      setChatMessages(prev => [...prev, transcriptMessage]);
    };

    // Register event listeners
    controls.on('chat_message_received', handleChatMessage);
    controls.on('system_event', handleSystemEvent);
    controls.on('transcription_received', handleTranscriptionReceived);

    // Cleanup
    return () => {
      controls.off('chat_message_received', handleChatMessage);
      controls.off('system_event', handleSystemEvent);
      controls.off('transcription_received', handleTranscriptionReceived);
    };
  }, [controls]);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [chatMessages, scrollToBottom]);

  // Send chat message
  const sendMessage = useCallback(async () => {
    if (!inputMessage.trim() || !currentUser || !sessionId) {
      return;
    }

    const messageText = inputMessage.trim();
    setInputMessage('');
    setIsTyping(false);

    try {
      // Create structured chat message
      const chatMessage = createChatMessage(sessionId, messageText, currentUser);
      
      // Add to local state immediately for responsive UI
      const displayMessage: ChatDisplayMessage = {
        id: chatMessage.messageId || `local_${Date.now()}`,
        content: messageText,
        sender: {
          name: currentUser.name,
          isAgent: currentUser.isAgent || false
        },
        timestamp: Date.now(),
        type: 'chat'
      };
      
      setChatMessages(prev => [...prev, displayMessage]);

      // Save message to database
      await saveChatMessage(displayMessage);

      // Send via data channel (this will be handled by the backend agent)
      console.log('[SessionChat] Sending chat message:', chatMessage);
      
      // Note: In a real implementation, this would be sent via the LiveKit data channel
      // For now, we're just adding it to the local state
      
    } catch (error) {
      console.error('[SessionChat] Failed to send chat message:', error);
    }
  }, [inputMessage, currentUser, sessionId]);

  // Handle input key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInputMessage(e.target.value);
    setIsTyping(e.target.value.length > 0);
  }, []);

  // Format timestamp
  const formatTimestamp = useCallback((timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Get message style based on type
  const getMessageStyle = useCallback((message: ChatDisplayMessage) => {
    switch (message.type) {
      case 'system':
        return 'bg-gray-100 text-gray-600 text-sm italic text-center';
      case 'agent_response':
        return 'bg-blue-100 border-l-4 border-blue-500';
      case 'transcript':
        return message.sender.isAgent 
          ? 'bg-purple-50 border-l-4 border-purple-400' 
          : 'bg-green-50 border-l-4 border-green-400';
      case 'chat':
      default:
        return message.sender.isAgent 
          ? 'bg-blue-50 border-l-4 border-blue-400' 
          : 'bg-gray-50 border-l-4 border-gray-400';
    }
  }, []);

  // Get message type label
  const getMessageTypeLabel = useCallback((message: ChatDisplayMessage) => {
    switch (message.type) {
      case 'transcript':
        return '🎤';
      case 'agent_response':
        return '🤖';
      case 'system':
        return '📢';
      case 'chat':
      default:
        return '💬';
    }
  }, []);

  return (
    <div className={`flex flex-col h-full bg-white border rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <h3 className="font-semibold text-gray-800">Session Chat</h3>
          <span className="text-sm text-gray-500">
            ({state.totalParticipants} participant{state.totalParticipants !== 1 ? 's' : ''})
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {state.isConnecting && (
            <div className="flex items-center space-x-1 text-sm text-yellow-600">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
              <span>Connecting...</span>
            </div>
          )}
          {isVoiceActive && (
            <div className="flex items-center space-x-1 text-sm text-green-600">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>Voice Active</span>
            </div>
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 min-h-0">
        {isLoadingHistory && (
          <div className="text-center text-gray-500 py-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Loading chat history...</span>
            </div>
          </div>
        )}
        {!isLoadingHistory && chatMessages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start a conversation!</p>
            {isVoiceActive && (
              <p className="text-sm mt-2">Voice transcripts will appear here automatically.</p>
            )}
          </div>
        ) : (
          chatMessages.map((message) => (
            <div
              key={message.id}
              className={`p-3 rounded-lg ${getMessageStyle(message)}`}
            >
              {message.type !== 'system' && (
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">{getMessageTypeLabel(message)}</span>
                    <span className="font-medium text-sm text-gray-800">
                      {message.sender.name}
                    </span>
                    {message.sender.isAgent && (
                      <span className="px-2 py-0.5 text-xs bg-blue-200 text-blue-800 rounded-full">
                        Agent
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(message.timestamp)}
                  </span>
                </div>
              )}
              <div className={`${message.type === 'system' ? 'text-center' : ''}`}>
                {message.content}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t bg-gray-50 rounded-b-lg">
        <div className="flex items-center space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={currentUser ? "Type a message..." : "Connect to chat"}
            disabled={!currentUser || !isVoiceActive}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
          />
          <button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || !currentUser || !isVoiceActive}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            Send
          </button>
        </div>
        {isTyping && (
          <div className="mt-2 text-xs text-gray-500">
            Press Enter to send, Shift+Enter for new line
          </div>
        )}
      </div>
    </div>
  );
}

export default SessionChat;