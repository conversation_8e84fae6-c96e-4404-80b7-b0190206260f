import { renderHook, act, waitFor } from '@testing-library/react';
import { useConnectionState } from '../../hooks/useConnectionState';

// Get references to mocked globals from setup
const mockNavigator = navigator as Navigator & { onLine: boolean };
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
const mockPerformanceNow = global.performance.now as jest.MockedFunction<typeof performance.now>;
const mockAddEventListener = window.addEventListener as jest.MockedFunction<typeof window.addEventListener>;
const mockRemoveEventListener = window.removeEventListener as jest.MockedFunction<typeof window.removeEventListener>;

// Mock timers
const mockSetTimeout = jest.fn();
const mockClearTimeout = jest.fn();
const mockSetInterval = jest.fn();
const mockClearInterval = jest.fn();

// Mock console methods to avoid noise in tests
const mockConsoleLog = jest.fn();
const mockConsoleWarn = jest.fn();
const mockConsoleError = jest.fn();

describe('useConnectionState', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock timers
    jest.spyOn(global, 'setTimeout').mockImplementation(mockSetTimeout);
    jest.spyOn(global, 'clearTimeout').mockImplementation(mockClearTimeout);
    jest.spyOn(global, 'setInterval').mockImplementation(mockSetInterval);
    jest.spyOn(global, 'clearInterval').mockImplementation(mockClearInterval);
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation(mockConsoleLog);
    jest.spyOn(console, 'warn').mockImplementation(mockConsoleWarn);
    jest.spyOn(console, 'error').mockImplementation(mockConsoleError);
    
    // Set default navigator.onLine to true
    mockNavigator.onLine = true;
    
    // Reset performance.now mock
    mockPerformanceNow.mockReturnValue(100);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should initialize with correct default state when online', () => {
      mockNavigator.onLine = true;
      
      const { result } = renderHook(() => useConnectionState());
      
      expect(result.current.connectionState.status).toBe('disconnected');
      expect(result.current.connectionState.isOnline).toBe(true);
      expect(result.current.connectionState.reconnectAttempts).toBe(0);
      expect(result.current.connectionState.lastConnected).toBeNull();
      expect(result.current.connectionState.error).toBeNull();
      expect(result.current.connectionState.latency).toBeNull();
    });

    it('should initialize with correct default state when offline', () => {
      mockNavigator.onLine = false;
      
      const { result } = renderHook(() => useConnectionState());
      
      expect(result.current.connectionState.status).toBe('disconnected');
      expect(result.current.connectionState.isOnline).toBe(false);
      expect(result.current.connectionState.reconnectAttempts).toBe(0);
      expect(result.current.connectionState.lastConnected).toBeNull();
      expect(result.current.connectionState.error).toBeNull();
      expect(result.current.connectionState.latency).toBeNull();
    });

    it('should initialize with correct computed properties', () => {
      const { result } = renderHook(() => useConnectionState());
      
      expect(result.current.isConnected).toBe(false);
      expect(result.current.isConnecting).toBe(false);
      expect(result.current.isReconnecting).toBe(false);
      expect(result.current.isFailed).toBe(false);
      expect(result.current.isOffline).toBe(false);
      expect(result.current.canReconnect).toBe(true);
    });
  });

  describe('Network Event Handling', () => {
    it('should attach online and offline event listeners on mount', () => {
      renderHook(() => useConnectionState());
      
      expect(mockAddEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(mockAddEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });

    it('should update state to online when online event fires', () => {
      const { result } = renderHook(() => useConnectionState());
      
      // Get the online event handler
      const onlineHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'online'
      )?.[1];
      
      expect(onlineHandler).toBeDefined();
      
      // Initially offline
      mockNavigator.onLine = false;
      act(() => {
        onlineHandler();
      });
      
      expect(result.current.connectionState.isOnline).toBe(true);
    });

    it('should update state to offline when offline event fires', () => {
      const { result } = renderHook(() => useConnectionState());
      
      // Get the offline event handler
      const offlineHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'offline'
      )?.[1];
      
      expect(offlineHandler).toBeDefined();
      
      act(() => {
        offlineHandler();
      });
      
      expect(result.current.connectionState.isOnline).toBe(false);
      expect(result.current.connectionState.status).toBe('offline');
    });

    it('should remove event listeners on unmount', () => {
      const { unmount } = renderHook(() => useConnectionState());
      
      unmount();
      
      expect(mockRemoveEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(mockRemoveEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
  });

  describe('Connection Functions', () => {
    it('should provide connect function', () => {
      const { result } = renderHook(() => useConnectionState());
      
      expect(typeof result.current.connect).toBe('function');
    });

    it('should provide disconnect function', () => {
      const { result } = renderHook(() => useConnectionState());
      
      expect(typeof result.current.disconnect).toBe('function');
    });

    it('should provide reconnect function', () => {
      const { result } = renderHook(() => useConnectionState());
      
      expect(typeof result.current.reconnect).toBe('function');
    });

    it('should provide forceReconnect function', () => {
      const { result } = renderHook(() => useConnectionState());
      
      expect(typeof result.current.forceReconnect).toBe('function');
    });

    it('should provide ping function', () => {
      const { result } = renderHook(() => useConnectionState());
      
      expect(typeof result.current.ping).toBe('function');
    });
  });

  describe('Ping Functionality', () => {
    it('should make HEAD request to /api/health', async () => {
      // Mock AbortController
      const mockAbortController = {
        signal: {
          aborted: false,
          onabort: null,
          reason: undefined,
          throwIfAborted: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn()
        },
        abort: jest.fn()
      };
      global.AbortController = jest.fn(() => mockAbortController) as jest.MockedClass<typeof AbortController>;
      
      mockFetch.mockResolvedValueOnce(new Response('', {
        status: 200,
        statusText: 'OK'
      }));
      
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.ping();
      });
      
      expect(mockFetch).toHaveBeenCalledWith('/api/health', {
        method: 'HEAD',
        signal: mockAbortController.signal,
        cache: 'no-cache'
      });
    });

    it('should calculate and update latency', async () => {
      mockPerformanceNow
        .mockReturnValueOnce(100) // Start time
        .mockReturnValueOnce(150); // End time
      
      mockFetch.mockResolvedValueOnce(new Response('', {
        status: 200,
        statusText: 'OK'
      }));
      
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        const latency = await result.current.ping();
        expect(latency).toBe(50);
      });
      
      expect(result.current.connectionState.latency).toBe(50);
    });

    it('should throw error when ping fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await expect(result.current.ping()).rejects.toThrow('Network error');
      });
    });

    it('should throw timeout error when ping times out', async () => {
      const mockAbortController = {
        signal: {
          aborted: false,
          onabort: null,
          reason: undefined,
          throwIfAborted: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn()
        } as AbortSignal,
        abort: jest.fn()
      };
      
      global.AbortController = jest.fn(() => mockAbortController) as unknown as typeof AbortController;
      
      mockFetch.mockRejectedValueOnce(Object.assign(new Error('AbortError'), { name: 'AbortError' }));
      
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await expect(result.current.ping()).rejects.toThrow('Ping timeout');
      });
    });
  });

  describe('Connection State Management', () => {
    it('should update status to connecting when connect is called', async () => {
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      act(() => {
        result.current.connect(mockConnectFn);
      });
      
      expect(result.current.connectionState.status).toBe('connecting');
    });

    it('should update status to connected when connect succeeds', async () => {
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.connect(mockConnectFn);
      });
      
      expect(result.current.connectionState.status).toBe('connected');
      expect(result.current.connectionState.lastConnected).toBeGreaterThan(0);
      expect(result.current.connectionState.reconnectAttempts).toBe(0);
    });

    it('should update status to failed when connect fails', async () => {
      const mockConnectFn = jest.fn().mockRejectedValue(new Error('Connection failed'));
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await expect(result.current.connect(mockConnectFn)).rejects.toThrow('Connection failed');
      });
      
      expect(result.current.connectionState.status).toBe('failed');
      expect(result.current.connectionState.error).toBe('Connection failed');
    });

    it('should not connect if already connecting', async () => {
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      // Start first connection
      const promise1 = act(() => result.current.connect(mockConnectFn));
      
      // Try to connect again while connecting
      await act(async () => {
        await result.current.connect(mockConnectFn);
      });
      
      // Should only call connect function once
      expect(mockConnectFn).toHaveBeenCalledTimes(1);
      
      await promise1;
    });

    it('should not connect if already connected', async () => {
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      // Connect first
      await act(async () => {
        await result.current.connect(mockConnectFn);
      });
      
      // Try to connect again
      await act(async () => {
        await result.current.connect(mockConnectFn);
      });
      
      // Should only call connect function once
      expect(mockConnectFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('Disconnection', () => {
    it('should update status to disconnected when disconnect is called', async () => {
      const mockDisconnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.disconnect(mockDisconnectFn);
      });
      
      expect(result.current.connectionState.status).toBe('disconnected');
      expect(result.current.connectionState.reconnectAttempts).toBe(0);
      expect(result.current.connectionState.latency).toBeNull();
    });

    it('should call disconnect function if provided', async () => {
      const mockDisconnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.disconnect(mockDisconnectFn);
      });
      
      expect(mockDisconnectFn).toHaveBeenCalled();
    });

    it('should handle disconnect function errors gracefully', async () => {
      const mockDisconnectFn = jest.fn().mockRejectedValue(new Error('Disconnect error'));
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.disconnect(mockDisconnectFn);
      });
      
      expect(result.current.connectionState.status).toBe('disconnected');
      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[ConnectionState] Disconnect error:',
        expect.any(Error)
      );
    });

    it('should clear reconnection timeout on disconnect', async () => {
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.disconnect();
      });
      
      expect(mockClearTimeout).toHaveBeenCalled();
    });

    it('should stop ping monitoring on disconnect', async () => {
      const { result } = renderHook(() => useConnectionState());
      
      await act(async () => {
        await result.current.disconnect();
      });
      
      expect(mockClearInterval).toHaveBeenCalled();
    });
  });

  describe('Reconnection Logic', () => {
    it('should attempt reconnection with exponential backoff', async () => {
      const mockConnectFn = jest.fn().mockRejectedValue(new Error('Connection failed'));
      const { result } = renderHook(() => useConnectionState({
        maxReconnectAttempts: 3,
        reconnectInterval: 1000,
        exponentialBackoff: true
      }));
      
      await act(async () => {
        await result.current.reconnect(mockConnectFn);
      });
      
      expect(result.current.connectionState.status).toBe('reconnecting');
      expect(result.current.connectionState.reconnectAttempts).toBe(1);
      expect(mockSetTimeout).toHaveBeenCalled();
    });

    it('should stop reconnection after max attempts', async () => {
      const mockConnectFn = jest.fn().mockRejectedValue(new Error('Connection failed'));
      const { result } = renderHook(() => useConnectionState({
        maxReconnectAttempts: 2
      }));
      
      // Set reconnect attempts to max
      act(() => {
        result.current.connectionState.reconnectAttempts = 2;
      });
      
      await act(async () => {
        await result.current.reconnect(mockConnectFn);
      });
      
      expect(result.current.connectionState.status).toBe('failed');
      expect(result.current.connectionState.error).toContain('Max reconnection attempts');
    });

    it('should reset reconnect attempts on successful connection', async () => {
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      // Set some reconnect attempts
      act(() => {
        result.current.connectionState.reconnectAttempts = 3;
      });
      
      await act(async () => {
        await result.current.connect(mockConnectFn);
      });
      
      expect(result.current.connectionState.reconnectAttempts).toBe(0);
    });

    it('should force reconnect by resetting attempt counter', async () => {
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useConnectionState());
      
      // Set some reconnect attempts
      act(() => {
        result.current.connectionState.reconnectAttempts = 5;
      });
      
      await act(async () => {
        await result.current.forceReconnect(mockConnectFn);
      });
      
      expect(result.current.connectionState.reconnectAttempts).toBe(0);
    });
  });

  describe('Configuration Options', () => {
    it('should use custom maxReconnectAttempts', () => {
      const { result } = renderHook(() => useConnectionState({
        maxReconnectAttempts: 10
      }));
      
      expect(result.current.canReconnect).toBe(true);
      
      // Set attempts to 9 (less than max)
      act(() => {
        result.current.connectionState.reconnectAttempts = 9;
      });
      
      expect(result.current.canReconnect).toBe(true);
      
      // Set attempts to 10 (equal to max)
      act(() => {
        result.current.connectionState.reconnectAttempts = 10;
      });
      
      expect(result.current.canReconnect).toBe(false);
    });

    it('should call onStatusChange callback when status changes', async () => {
      const onStatusChange = jest.fn();
      const mockConnectFn = jest.fn().mockResolvedValue(undefined);
      
      const { result } = renderHook(() => useConnectionState({
        onStatusChange
      }));
      
      await act(async () => {
        await result.current.connect(mockConnectFn);
      });
      
      expect(onStatusChange).toHaveBeenCalledWith('connecting');
      expect(onStatusChange).toHaveBeenCalledWith('connected');
    });

    it('should call onError callback when error occurs', async () => {
      const onError = jest.fn();
      const mockConnectFn = jest.fn().mockRejectedValue(new Error('Test error'));
      
      const { result } = renderHook(() => useConnectionState({
        onError
      }));
      
      await act(async () => {
        await expect(result.current.connect(mockConnectFn)).rejects.toThrow();
      });
      
      expect(onError).toHaveBeenCalledWith('Test error');
    });

    it('should use custom reconnectInterval without exponential backoff', () => {
      const { result } = renderHook(() => useConnectionState({
        reconnectInterval: 5000,
        exponentialBackoff: false
      }));
      
      // Access the private getReconnectDelay method through the hook's internal logic
      // This is tested indirectly through the reconnection behavior
      expect(result.current).toBeDefined();
    });

    it('should use custom pingInterval', () => {
      renderHook(() => useConnectionState({
        pingInterval: 10000
      }));
      
      // Verify that setInterval would be called with custom interval
      // This is tested indirectly through the ping monitoring behavior
      expect(mockSetInterval).toHaveBeenCalled();
    });

    it('should use custom pingTimeout', async () => {
      const { result } = renderHook(() => useConnectionState({
        pingTimeout: 3000
      }));
      
      // Mock AbortController
      const mockAbortController = {
        signal: {
          aborted: false,
          onabort: null,
          reason: undefined,
          throwIfAborted: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn()
        } as AbortSignal,
        abort: jest.fn()
      };
      global.AbortController = jest.fn(() => mockAbortController) as unknown as typeof AbortController;
      
      mockFetch.mockResolvedValueOnce(new Response('', {
        status: 200,
        statusText: 'OK'
      }));
      
      await act(async () => {
        await result.current.ping();
      });
      
      expect(mockSetTimeout).toHaveBeenCalledWith(expect.any(Function), 3000);
    });
  });

  describe('Cleanup and Memory Leaks', () => {
    it('should clear all timeouts on unmount', () => {
      const { unmount } = renderHook(() => useConnectionState());
      
      unmount();
      
      expect(mockClearTimeout).toHaveBeenCalled();
      expect(mockClearInterval).toHaveBeenCalled();
    });

    it('should remove event listeners on unmount', () => {
      const { unmount } = renderHook(() => useConnectionState());
      
      unmount();
      
      expect(mockRemoveEventListener).toHaveBeenCalledTimes(2);
    });

    it('should handle rapid mount/unmount cycles', () => {
      const { unmount: unmount1 } = renderHook(() => useConnectionState());
      const { unmount: unmount2 } = renderHook(() => useConnectionState());
      const { unmount: unmount3 } = renderHook(() => useConnectionState());
      
      unmount1();
      unmount2();
      unmount3();
      
      // Should not throw errors
      expect(mockRemoveEventListener).toHaveBeenCalledTimes(6); // 2 per hook instance
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing performance API', async () => {
      // Remove performance API
      Object.defineProperty(global, 'performance', {
        value: undefined,
        writable: true
      });
      
      const { result } = renderHook(() => useConnectionState());
      
      // Should not throw when performance API is unavailable
      expect(() => result.current.ping()).not.toThrow();
    });

    it('should handle missing navigator.onLine', () => {
      Object.defineProperty(global, 'navigator', {
        value: {},
        writable: true
      });
      
      const { result } = renderHook(() => useConnectionState());
      
      // Should default to false when navigator.onLine is unavailable
      expect(result.current.connectionState.isOnline).toBe(false);
    });

    it('should handle rapid online/offline transitions', () => {
      const { result } = renderHook(() => useConnectionState());
      
      const onlineHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'online'
      )?.[1] as EventListener;
      const offlineHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'offline'
      )?.[1] as EventListener;
      
      expect(onlineHandler).toBeDefined();
      expect(offlineHandler).toBeDefined();
      
      // Rapid transitions
      act(() => {
        (offlineHandler as () => void)();
        (onlineHandler as () => void)();
        (offlineHandler as () => void)();
        (onlineHandler as () => void)();
      });
      
      expect(result.current.connectionState.isOnline).toBe(true);
    });

    it('should handle component unmount during reconnection', async () => {
      const mockConnectFn = jest.fn().mockRejectedValue(new Error('Connection failed'));
      const { result, unmount } = renderHook(() => useConnectionState());
      
      // Start reconnection
      act(() => {
        result.current.reconnect(mockConnectFn);
      });
      
      // Unmount during reconnection
      unmount();
      
      // Should clean up properly
      expect(mockClearTimeout).toHaveBeenCalled();
    });

    it('should handle invalid configuration gracefully', () => {
      const { result } = renderHook(() => useConnectionState({
        maxReconnectAttempts: -1,
        reconnectInterval: -1000,
        pingInterval: 0,
        pingTimeout: -500
      }));
      
      // Should still initialize without throwing
      expect(result.current.connectionState).toBeDefined();
    });
  });
});