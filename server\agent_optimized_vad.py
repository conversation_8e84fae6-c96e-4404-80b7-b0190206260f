#!/usr/bin/env python3
"""
Voice Cost Optimized Python Agent for Rep Room
Implements VAD-based STT lifecycle management to drastically reduce Deepgram costs
Only processes speech when actually detected using LiveKit track events and VAD
"""

import asyncio
import os
import logging
import time
import json
import uuid
from typing import Optional, Dict, Any
import threading
from dataclasses import dataclass
from enum import Enum

# Suppress ONNX Runtime verbose logging
os.environ['ORT_LOG_LEVEL'] = '3'
os.environ['ONNXRUNTIME_LOG_LEVEL'] = '3'

from livekit.agents import JobContext, cli
from livekit.plugins.deepgram import STT
from livekit import rtc
from flask import Flask, jsonify

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Voice Activity Detection States
class VADState(Enum):
    IDLE = "idle"
    SPEECH_DETECTED = "speech_detected"
    PROCESSING = "processing"
    COOLDOWN = "cooldown"

@dataclass
class VoiceActivityConfig:
    """Configuration for voice activity detection and cost optimization"""
    # VAD sensitivity settings
    speech_threshold: float = 0.5
    silence_threshold: float = 0.3
    min_speech_duration_ms: int = 300  # Minimum speech duration to trigger STT
    max_silence_duration_ms: int = 2000  # Max silence before stopping STT
    
    # Cost optimization settings
    stt_startup_delay_ms: int = 100  # Delay before starting STT to avoid false positives
    stt_shutdown_delay_ms: int = 1000  # Delay before stopping STT to catch trailing speech
    max_stt_session_duration_ms: int = 30000  # Max STT session duration (30s)
    
    # Inactivity settings
    inactivity_timeout_seconds: int = 90
    warning_threshold_seconds: int = 75
    
    # Audio level tracking
    audio_level_smoothing: float = 0.7  # Exponential smoothing factor
    silence_frames_threshold: int = 50  # Frames of silence before considering stopping

@dataclass
class VoiceActivityState:
    """Current state of voice activity detection"""
    current_state: VADState = VADState.IDLE
    last_speech_time: float = 0
    last_activity_time: float = 0
    session_start_time: float = 0
    
    # Audio analysis
    current_audio_level: float = 0
    smoothed_audio_level: float = 0
    silence_frame_count: int = 0
    speech_frame_count: int = 0
    
    # STT session management
    stt_session_active: bool = False
    stt_session_start_time: float = 0
    stt_startup_timer: Optional[asyncio.Task] = None
    stt_shutdown_timer: Optional[asyncio.Task] = None
    
    # Cost tracking
    total_stt_time_seconds: float = 0
    stt_activations: int = 0
    false_positive_count: int = 0

class VoiceActivityDetector:
    """Advanced Voice Activity Detector with cost optimization"""
    
    def __init__(self, config: VoiceActivityConfig):
        self.config = config
        self.state = VoiceActivityState()
        self.state.session_start_time = time.time()
        self.callbacks = {
            'speech_start': [],
            'speech_end': [],
            'stt_start': [],
            'stt_stop': [],
            'inactivity_warning': [],
            'inactivity_timeout': []
        }
        
    def add_callback(self, event: str, callback):
        """Add callback for VAD events"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def _emit_event(self, event: str, data: Dict[Any, Any] = None):
        """Emit event to registered callbacks"""
        for callback in self.callbacks.get(event, []):
            try:
                asyncio.create_task(callback(data or {}))
            except Exception as e:
                logger.error(f"Error in VAD callback for {event}: {e}")
    
    def analyze_audio_frame(self, audio_frame) -> bool:
        """
        Analyze audio frame for voice activity
        Returns True if speech is detected, False otherwise
        """
        try:
            # Calculate RMS audio level
            import numpy as np
            audio_data = np.frombuffer(audio_frame.data, dtype=np.int16)
            rms_level = np.sqrt(np.mean(audio_data.astype(np.float32) ** 2))
            
            # Normalize to 0-1 range (assuming 16-bit audio)
            self.state.current_audio_level = min(rms_level / 32768.0, 1.0)
            
            # Apply exponential smoothing
            self.state.smoothed_audio_level = (
                self.config.audio_level_smoothing * self.state.smoothed_audio_level +
                (1 - self.config.audio_level_smoothing) * self.state.current_audio_level
            )
            
            # Determine if speech is present
            is_speech = self.state.smoothed_audio_level > self.config.speech_threshold
            
            if is_speech:
                self.state.speech_frame_count += 1
                self.state.silence_frame_count = 0
            else:
                self.state.silence_frame_count += 1
                if self.state.smoothed_audio_level < self.config.silence_threshold:
                    self.state.speech_frame_count = max(0, self.state.speech_frame_count - 1)
            
            return is_speech
            
        except Exception as e:
            logger.error(f"Error analyzing audio frame: {e}")
            return False
    
    async def process_audio_frame(self, audio_frame):
        """Process audio frame and manage VAD state transitions"""
        current_time = time.time()
        is_speech = self.analyze_audio_frame(audio_frame)
        
        # State machine for VAD
        if self.state.current_state == VADState.IDLE:
            if is_speech and self.state.speech_frame_count >= 3:  # Require multiple frames
                await self._transition_to_speech_detected(current_time)
                
        elif self.state.current_state == VADState.SPEECH_DETECTED:
            if is_speech:
                self.state.last_speech_time = current_time
                # Start STT after minimum speech duration
                if (current_time - self.state.last_speech_time) * 1000 >= self.config.min_speech_duration_ms:
                    await self._transition_to_processing(current_time)
            else:
                # Check if we should go back to idle
                if self.state.silence_frame_count >= self.config.silence_frames_threshold:
                    await self._transition_to_idle()
                    
        elif self.state.current_state == VADState.PROCESSING:
            if is_speech:
                self.state.last_speech_time = current_time
            else:
                # Check if we should stop processing
                silence_duration = (current_time - self.state.last_speech_time) * 1000
                if silence_duration >= self.config.max_silence_duration_ms:
                    await self._transition_to_cooldown(current_time)
                    
        elif self.state.current_state == VADState.COOLDOWN:
            if is_speech:
                # Speech detected during cooldown, go back to processing
                await self._transition_to_processing(current_time)
            else:
                # Cooldown period completed
                cooldown_duration = (current_time - self.state.last_activity_time) * 1000
                if cooldown_duration >= self.config.stt_shutdown_delay_ms:
                    await self._transition_to_idle()
        
        # Check for inactivity timeout
        await self._check_inactivity_timeout(current_time)
        
        # Check for maximum STT session duration
        if (self.state.stt_session_active and 
            (current_time - self.state.stt_session_start_time) * 1000 >= self.config.max_stt_session_duration_ms):
            logger.warning("Maximum STT session duration reached, forcing stop")
            await self._stop_stt_session()
    
    async def _transition_to_speech_detected(self, current_time: float):
        """Transition to speech detected state"""
        logger.info("VAD: Speech detected")
        self.state.current_state = VADState.SPEECH_DETECTED
        self.state.last_speech_time = current_time
        self.state.last_activity_time = current_time
        self._emit_event('speech_start', {
            'timestamp': current_time,
            'audio_level': self.state.smoothed_audio_level
        })
    
    async def _transition_to_processing(self, current_time: float):
        """Transition to processing state and start STT"""
        logger.info("VAD: Starting STT processing")
        self.state.current_state = VADState.PROCESSING
        self.state.last_activity_time = current_time
        
        # Cancel any pending shutdown timer
        if self.state.stt_shutdown_timer:
            self.state.stt_shutdown_timer.cancel()
            self.state.stt_shutdown_timer = None
        
        # Start STT session with delay to avoid false positives
        if not self.state.stt_session_active:
            self.state.stt_startup_timer = asyncio.create_task(
                self._delayed_stt_start(self.config.stt_startup_delay_ms / 1000)
            )
    
    async def _transition_to_cooldown(self, current_time: float):
        """Transition to cooldown state"""
        logger.info("VAD: Entering cooldown")
        self.state.current_state = VADState.COOLDOWN
        self.state.last_activity_time = current_time
        self._emit_event('speech_end', {
            'timestamp': current_time,
            'duration': current_time - self.state.last_speech_time
        })
        
        # Schedule STT shutdown with delay
        if self.state.stt_session_active and not self.state.stt_shutdown_timer:
            self.state.stt_shutdown_timer = asyncio.create_task(
                self._delayed_stt_stop(self.config.stt_shutdown_delay_ms / 1000)
            )
    
    async def _transition_to_idle(self):
        """Transition to idle state"""
        logger.info("VAD: Returning to idle")
        self.state.current_state = VADState.IDLE
        self.state.speech_frame_count = 0
        self.state.silence_frame_count = 0
        
        # Ensure STT is stopped
        if self.state.stt_session_active:
            await self._stop_stt_session()
    
    async def _delayed_stt_start(self, delay_seconds: float):
        """Start STT session after delay"""
        try:
            await asyncio.sleep(delay_seconds)
            if self.state.current_state in [VADState.SPEECH_DETECTED, VADState.PROCESSING]:
                await self._start_stt_session()
        except asyncio.CancelledError:
            logger.debug("STT startup cancelled")
    
    async def _delayed_stt_stop(self, delay_seconds: float):
        """Stop STT session after delay"""
        try:
            await asyncio.sleep(delay_seconds)
            if self.state.current_state == VADState.COOLDOWN:
                await self._stop_stt_session()
        except asyncio.CancelledError:
            logger.debug("STT shutdown cancelled")
    
    async def _start_stt_session(self):
        """Start STT session"""
        if not self.state.stt_session_active:
            current_time = time.time()
            self.state.stt_session_active = True
            self.state.stt_session_start_time = current_time
            self.state.stt_activations += 1
            
            logger.info(f"🎤 STT Session Started (Activation #{self.state.stt_activations})")
            self._emit_event('stt_start', {
                'timestamp': current_time,
                'activation_count': self.state.stt_activations
            })
    
    async def _stop_stt_session(self):
        """Stop STT session and update cost tracking"""
        if self.state.stt_session_active:
            current_time = time.time()
            session_duration = current_time - self.state.stt_session_start_time
            self.state.total_stt_time_seconds += session_duration
            self.state.stt_session_active = False
            
            # Cancel any pending timers
            if self.state.stt_startup_timer:
                self.state.stt_startup_timer.cancel()
                self.state.stt_startup_timer = None
            if self.state.stt_shutdown_timer:
                self.state.stt_shutdown_timer.cancel()
                self.state.stt_shutdown_timer = None
            
            logger.info(f"🛑 STT Session Stopped (Duration: {session_duration:.2f}s, Total: {self.state.total_stt_time_seconds:.2f}s)")
            self._emit_event('stt_stop', {
                'timestamp': current_time,
                'session_duration': session_duration,
                'total_stt_time': self.state.total_stt_time_seconds
            })
    
    async def _check_inactivity_timeout(self, current_time: float):
        """Check for inactivity timeout"""
        time_since_activity = current_time - self.state.last_activity_time
        
        if time_since_activity >= self.config.inactivity_timeout_seconds:
            logger.warning("Inactivity timeout reached")
            self._emit_event('inactivity_timeout', {
                'timestamp': current_time,
                'inactive_duration': time_since_activity
            })
        elif time_since_activity >= self.config.warning_threshold_seconds:
            logger.warning("Inactivity warning threshold reached")
            self._emit_event('inactivity_warning', {
                'timestamp': current_time,
                'remaining_time': self.config.inactivity_timeout_seconds - time_since_activity
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current VAD and cost optimization stats"""
        current_time = time.time()
        session_duration = current_time - self.state.session_start_time
        
        return {
            'state': self.state.current_state.value,
            'session_duration': session_duration,
            'total_stt_time': self.state.total_stt_time_seconds,
            'stt_activations': self.state.stt_activations,
            'stt_efficiency': (self.state.total_stt_time_seconds / session_duration * 100) if session_duration > 0 else 0,
            'current_audio_level': self.state.current_audio_level,
            'smoothed_audio_level': self.state.smoothed_audio_level,
            'stt_session_active': self.state.stt_session_active,
            'time_since_activity': current_time - self.state.last_activity_time
        }

# Health check server
app = Flask(__name__)

@app.route('/health')
def health_check():
    """Health check endpoint with VAD stats"""
    return jsonify({
        "status": "healthy",
        "service": "voice-cost-optimized-agent",
        "version": "v2.0-vad-optimized"
    })

@app.route('/stats')
def vad_stats():
    """VAD and cost optimization stats endpoint"""
    global vad_detector
    if vad_detector:
        return jsonify(vad_detector.get_stats())
    return jsonify({"error": "VAD not initialized"})

def start_health_server():
    """Start the health check server in a separate thread."""
    logger.info("Starting Flask health server...")
    app.run(host='0.0.0.0', port=8080, debug=False, threaded=True)

def get_stt_provider():
    """Get STT provider with optimized configuration."""
    deepgram_api_key = os.getenv("DEEPGRAM_API_KEY")
    if not deepgram_api_key:
        logger.error("DEEPGRAM_API_KEY not found in environment")
        return None
    
    try:
        logger.info("Initializing Deepgram STT with cost optimization")
        stt_provider = STT(
            model="nova-2",
            language="en",
            interim_results=True,
            smart_format=False,
            punctuate=True,
            filler_words=False,
            profanity_filter=False,
            numerals=False,
            # Optimized settings for cost efficiency
            endpointing=300,  # 300ms endpointing for faster response
            utterance_end_ms=1000,  # 1s utterance end detection
            vad_turnoff=250  # 250ms VAD turnoff
        )
        logger.info("✅ Deepgram STT provider initialized with cost optimization")
        return stt_provider
    except Exception as e:
        logger.error(f"❌ Deepgram STT initialization failed: {e}")
        return None

async def send_transcript_data_channel(ctx: JobContext, transcript: str, is_final: bool = False):
    """Send transcript via LiveKit data channel."""
    try:
        # Create transcript event with the format expected by UnifiedVoiceContext
        if is_final:
            event = {
                "type": "USER_TRANSCRIPT_FINAL",
                "transcript": transcript,
                "timestamp": int(time.time() * 1000),
                "message_id": str(uuid.uuid4())
            }
        else:
            event = {
                "type": "USER_TRANSCRIPT_INTERIM",
                "transcript": transcript,
                "timestamp": int(time.time() * 1000),
                "message_id": str(uuid.uuid4())
            }
        
        # Convert to JSON and encode
        event_json = json.dumps(event)
        event_bytes = event_json.encode('utf-8')
        
        # Send via LiveKit data channel
        await ctx.room.local_participant.publish_data(
            payload=event_bytes,
            destination_identities=[],  # Broadcast to all participants
            topic="voice-transcripts",
            reliable=is_final  # Use reliable delivery for final transcripts
        )
        
        transcript_type = "FINAL" if is_final else "INTERIM"
        logger.info(f"📡 {transcript_type} transcript sent via data channel: '{transcript}'")
        
    except Exception as e:
        logger.error(f"❌ Failed to send transcript via data channel: {e}")

# Global VAD detector instance
vad_detector: Optional[VoiceActivityDetector] = None

async def entrypoint(ctx: JobContext):
    """Voice cost optimized agent entry point with VAD-based STT lifecycle management."""
    global vad_detector
    
    logger.info(f"🚀 Voice Cost Optimized Agent started for room: {ctx.room.name}")
    
    try:
        # Initialize VAD detector
        vad_config = VoiceActivityConfig()
        vad_detector = VoiceActivityDetector(vad_config)
        
        # Get STT provider
        stt_provider = get_stt_provider()
        if not stt_provider:
            logger.error("❌ No STT provider available - cannot proceed")
            return
        
        # Connect to the room
        logger.info("Connecting to LiveKit room...")
        await ctx.connect()
        logger.info("✅ Agent connected and ready for voice processing")
        
        # Wait for participant to join
        participant = await ctx.wait_for_participant()
        logger.info(f"👤 Participant joined: {participant.identity}")
        
        # STT stream management
        stt_stream = None
        stt_task = None
        
        async def start_stt_processing():
            """Start STT processing when VAD detects speech"""
            nonlocal stt_stream, stt_task
            
            if stt_stream is None:
                logger.info("🎯 Starting STT stream due to VAD detection")
                stt_stream = stt_provider.stream()
                stt_task = asyncio.create_task(handle_stt_results(stt_stream))
        
        async def stop_stt_processing():
            """Stop STT processing when VAD detects silence"""
            nonlocal stt_stream, stt_task
            
            if stt_stream is not None:
                logger.info("🛑 Stopping STT stream due to VAD silence detection")
                try:
                    if stt_task:
                        stt_task.cancel()
                        try:
                            await stt_task
                        except asyncio.CancelledError:
                            pass
                    
                    # Close STT stream
                    await stt_stream.aclose()
                    stt_stream = None
                    stt_task = None
                    
                    # Log cost savings
                    stats = vad_detector.get_stats()
                    logger.info(f"💰 STT Cost Optimization - Efficiency: {stats['stt_efficiency']:.1f}% "
                              f"(Active: {stats['total_stt_time']:.1f}s / Session: {stats['session_duration']:.1f}s)")
                    
                except Exception as e:
                    logger.error(f"Error stopping STT stream: {e}")
        
        # Register VAD callbacks
        vad_detector.add_callback('stt_start', lambda data: start_stt_processing())
        vad_detector.add_callback('stt_stop', lambda data: stop_stt_processing())
        
        # Set up audio processing for participant
        async def handle_participant_audio(participant: rtc.RemoteParticipant):
            """Handle audio from a participant with VAD-optimized STT processing."""
            logger.info(f"🎤 Setting up VAD-optimized STT processing for participant: {participant.identity}")
            
            # Subscribe to participant's audio tracks
            for publication in participant.track_publications.values():
                if publication.track and publication.track.kind == rtc.TrackKind.KIND_AUDIO:
                    logger.info(f"🔊 Subscribing to audio track: {publication.track.sid}")
                    
                    # Create audio stream for VAD analysis
                    audio_stream = rtc.AudioStream(publication.track)
                    
                    # Audio processing task with VAD
                    async def process_audio():
                        """Process audio frames with VAD analysis and conditional STT."""
                        frame_count = 0
                        try:
                            async for audio_frame_event in audio_stream:
                                frame_count += 1
                                audio_frame = audio_frame_event.frame
                                
                                # Process frame through VAD
                                await vad_detector.process_audio_frame(audio_frame)
                                
                                # Only send to STT if session is active
                                if vad_detector.state.stt_session_active and stt_stream:
                                    stt_stream.push_frame(audio_frame)
                                
                                # Log stats periodically
                                if frame_count % 1000 == 0:  # Every 1000 frames
                                    stats = vad_detector.get_stats()
                                    logger.debug(f"VAD Stats - State: {stats['state']}, "
                                               f"Audio Level: {stats['smoothed_audio_level']:.3f}, "
                                               f"STT Active: {stats['stt_session_active']}")
                                
                        except Exception as e:
                            logger.error(f"❌ Audio processing error: {e}")
                    
                    # Start audio processing task
                    audio_task = asyncio.create_task(process_audio())
                    logger.info("🔄 VAD-optimized audio processing started")
        
        async def handle_stt_results(stt_stream):
            """Handle STT results and send transcripts via data channel."""
            logger.info("🎯 STT Results Handler Started (VAD-controlled)")
            
            try:
                async for event in stt_stream:
                    # Check for transcript content
                    if hasattr(event, 'alternatives') and event.alternatives and len(event.alternatives) > 0:
                        transcript_text = event.alternatives[0].text.strip()
                        
                        if transcript_text:
                            # Check if this is a final transcript
                            is_final = False
                            
                            # Method 1: Check is_final attribute (most common)
                            if hasattr(event, 'is_final'):
                                is_final = event.is_final
                            
                            # Method 2: Check type attribute and convert to string
                            elif hasattr(event, 'type'):
                                event_type_str = str(event.type).upper()
                                is_final = 'FINAL' in event_type_str and 'TRANSCRIPT' in event_type_str
                            
                            # Method 3: Check speech_final attribute (alternative)
                            elif hasattr(event, 'speech_final'):
                                is_final = event.speech_final
                            
                            # Method 4: Check if alternatives have is_final
                            elif hasattr(event.alternatives[0], 'is_final'):
                                is_final = event.alternatives[0].is_final
                            
                            # Send transcript via data channel
                            await send_transcript_data_channel(ctx, transcript_text, is_final)
                            
                            if is_final:
                                logger.info(f"✅ FINAL: {transcript_text}")
                                # Record activity for inactivity tracking
                                vad_detector.state.last_activity_time = time.time()
                            else:
                                logger.debug(f"⏳ INTERIM: {transcript_text}")
                
            except Exception as e:
                logger.error(f"❌ STT results handling error: {e}")
        
        # Handle existing participants
        for participant in ctx.room.remote_participants.values():
            await handle_participant_audio(participant)
        
        # Handle new participants joining
        @ctx.room.on("participant_connected")
        def on_participant_connected(participant: rtc.RemoteParticipant):
            logger.info(f"🆕 New participant connected: {participant.identity}")
            asyncio.create_task(handle_participant_audio(participant))
        
        # Handle track subscriptions
        @ctx.room.on("track_subscribed")
        def on_track_subscribed(track: rtc.Track, publication: rtc.RemoteTrackPublication, participant: rtc.RemoteParticipant):
            if track.kind == rtc.TrackKind.KIND_AUDIO:
                logger.info(f"🎵 Audio track subscribed from {participant.identity}")
                asyncio.create_task(handle_participant_audio(participant))
        
        # Periodic stats logging
        async def log_stats():
            while ctx.room.connection_state == rtc.ConnectionState.CONN_CONNECTED:
                await asyncio.sleep(30)  # Log every 30 seconds
                if vad_detector:
                    stats = vad_detector.get_stats()
                    logger.info(f"📊 VAD Stats - State: {stats['state']}, "
                              f"STT Efficiency: {stats['stt_efficiency']:.1f}%, "
                              f"Activations: {stats['stt_activations']}, "
                              f"Total STT Time: {stats['total_stt_time']:.1f}s")
        
        # Start stats logging task
        stats_task = asyncio.create_task(log_stats())
        
        # Keep the agent alive
        logger.info("🎧 Voice cost optimized agent ready - VAD-controlled STT processing")
        while ctx.room.connection_state == rtc.ConnectionState.CONN_CONNECTED:
            await asyncio.sleep(1)
            
    except Exception as e:
        logger.error(f"❌ Failed to initialize voice cost optimized agent: {e}")
        raise e
    finally:
        # Cleanup
        if vad_detector and vad_detector.state.stt_session_active:
            await vad_detector._stop_stt_session()

# CLI Entry Point
if __name__ == "__main__":
    # Start health check server in background
    health_thread = threading.Thread(target=start_health_server, daemon=True)
    health_thread.start()
    
    # Give the health server time to start
    time.sleep(2)
    logger.info("🏥 Health check server started on port 8080")
    logger.info("🚀 VOICE COST OPTIMIZED PYTHON AGENT STARTUP - VAD-CONTROLLED STT")
    
    # Start the LiveKit agent
    from livekit.agents import WorkerOptions
    logger.info("▶️ STARTING VOICE COST OPTIMIZED LIVEKIT AGENT")
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))