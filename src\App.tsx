
import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { Layout } from './components/layout';
import { OrganizationCreate } from './components/organizations/organization-create';
import AgentEnablementPage from './pages/super-agency/AgentEnablementPage';
import Dashboard from './pages/Dashboard';
import TenantDashboard from './pages/TenantDashboard';
import Debug from './pages/Debug';
import Index from './pages/Index';
import Login from './pages/Login';
import ResetPassword from './pages/ResetPassword';
import UpdatePassword from './pages/UpdatePassword';
import Settings from './pages/Settings';
import Team from './pages/Team';
import CreditLedgerPage from './pages/CreditLedgerPage';
import { OrganizationsPage } from './pages/organizations-page';
import { OrgsPage } from './pages/orgs-page';
import { ThemeProvider } from './components/theme-provider';
import { Toaster } from './components/ui/sonner';
import { ProtectedRoute, RoleProtectedRoute } from './components/auth';
import OrganizationResourcesPage from './pages/OrganizationResourcesPage';
import OrganizationBillingManagementPage from './pages/OrganizationBillingManagementPage';
import { UserCreate } from './components/users/user-create';
import UsersPage from './pages/UsersPage';
import UserProfile from './pages/UserProfile';
import InvitationAcceptance from './components/invitations/invitation-accept';
import AgentTypesListPage from './pages/AgentTypesListPage';
import AgentTypeDetailsPage from './pages/AgentTypeDetailsPage';
import AgentTypeCreatePage from './pages/AgentTypeCreatePage';
import AgentTypeUpdatePage from './pages/AgentTypeUpdatePage';
import Agents from './pages/Agents';
import AgentRegistry from './pages/AgentRegistry';
import MyActivatedAgentsPage from './pages/MyActivatedAgentsPage';
import MyClonesPage from './pages/MyClonesPage';
import CloneCreatePage from './pages/CloneCreatePage';
import CloneEditPage from './pages/CloneEditPage';
import CloneDetailsPage from './pages/CloneDetailsPage';
import AgentActivationDetailsPage from './pages/AgentActivationDetailsPage';
import AgentActivationPage from './pages/AgentActivationPage';
import Chat from './pages/Chat';
import Analytics from './pages/Analytics';
import ContentListPage from './pages/ContentListPage';
import ContentGenerationPage from './pages/ContentGenerationPage';
import ProcessedContentDetailsPage from './pages/ProcessedContentDetailsPage';
import ContentUploadPage from './pages/ContentUploadPage';
import ContentReviewPage from './pages/ContentReviewPage';
import ContentEditPage from './pages/ContentEditPage';
import ApiKeys from './pages/ApiKeys';
import { Permissions } from './lib/rbac';
import AgenticProjectsPage from './pages/tenant/AgenticProjectsPage';
import MyTasksPage from './pages/tenant/MyTasksPage';
import MemoriesPage from './pages/tenant/MemoriesPage';
import PlaygroundPage from './pages/tenant/PlaygroundPage';
import ActivityLogPage from './pages/tenant/ActivityLogPage';
import { AssetsManagementPage } from './pages/AssetsManagementPage';
import { TaskManagementPage } from './pages/TaskManagementPage';
import { AgentDashboardPage } from './pages/AgentDashboardPage';
import { ProjectAnalyticsPage } from './pages/ProjectAnalyticsPage';
import { ProjectsPage } from './pages/ProjectsPage';
import ProjectManagementPage from './pages/ProjectManagementPage';
import { OrganizationManagementPage } from './pages/OrganizationManagementPage';
import { MonitoringDashboardPage } from './pages/MonitoringDashboardPage';
import { IntegrationManagementPage } from './pages/IntegrationManagementPage';
import { RepRoomsPage } from './pages/tenant/RepRoomsPage';
import RepRoomPageUnifiedFixed from './pages/RepRoomPageUnifiedFixed';
import RepRoomPageUnifiedLiveKit from './pages/RepRoomPageUnifiedLiveKit';
import RepRoomPageUnifiedEnhanced from './pages/RepRoomPageUnifiedEnhanced';
import SimpleRepRoomVoice from './pages/SimpleRepRoomVoice';
import { RepRoomT1Page } from './pages/public/RepRoomT1Page';
import { RepRoomT1PageDebug } from './pages/public/RepRoomT1PageDebug';
import { RepRoomT1PageSimple } from './pages/public/RepRoomT1PageSimple';
import { VoiceTestPage } from './pages/public/VoiceTestPage';
import { VoiceRepRoomPage } from './pages/VoiceRepRoomPage';
import RepRoomMasterT1Page from './pages/public/RepRoomMasterT1Page';
import WebhooksPage from './pages/tenant/WebhooksPage';
import PlaygroundPageImproved from './pages/PlaygroundPageImproved';
import PlaygroundPageAlternative from './pages/PlaygroundPageAlternative';
import UnifiedPlaygroundPage from './pages/UnifiedPlaygroundPage';
import { StandaloneDirectCopilotKitChat } from './components/playground/DirectCopilotKitChat';
import { NewRepRoomPage } from './pages/rroom/NewRepRoomPage';
import SimpleRepRoomPage from './pages/SimpleRepRoomPage';
import RepRoomPage from './pages/RepRoomPage';
import { RepRoomPageEnhanced } from './pages/RepRoomPageEnhanced';
import { RepRoomPageV3 } from './pages/RepRoomPageV3';
import { RepRoomSessionPage } from './pages/RepRoomSessionPage';

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/reset-password" element={<ResetPassword />} />
        <Route path="/update-password" element={<UpdatePassword />} />
        <Route path="/invitation" element={<InvitationAcceptance />} />
        <Route path="/auth/accept-invitation" element={<InvitationAcceptance />} />
        
        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route element={<Layout />}>
            <Route path="/" element={<Index />} />
            <Route path="/dashboard" element={<Dashboard />} />
            {/* Restrict /organizations to house accounts only */}
            <Route path="/organizations/*" element={
              <RoleProtectedRoute allowedRoles={['house']} redirectPath="/orgs">
                <OrganizationsPage />
              </RoleProtectedRoute>
            } />
            {/* New /orgs route for direct child organizations */}
            <Route path="/orgs/*" element={<OrgsPage />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/team" element={<Team />} />
            <Route path="/users/create" element={<UserCreate />} />
            <Route path="/organizations/create" element={<OrganizationCreate />} />
            <Route path="/resources" element={<OrganizationResourcesPage />} />
            <Route path="/credit-ledger" element={<CreditLedgerPage />} />
            <Route path="/debug" element={<Debug />} />
            <Route path="/organizations/:id/billing-management" element={<OrganizationBillingManagementPage />} />
            <Route path="/orgs/:id/billing-management" element={<OrganizationBillingManagementPage />} />
            <Route path="/users" element={<UsersPage />} />
            <Route path="/users/:id" element={<UserProfile />} />
            
            {/* Agent Type Management Routes - House Admin only */}
            <Route path="/agent-types" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.Manage}
                redirectPath="/dashboard"
              >
                <AgentTypesListPage />
              </RoleProtectedRoute>
            } />
            <Route path="/agent-types/create" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.Manage}
                redirectPath="/dashboard"
              >
                <AgentTypeCreatePage />
              </RoleProtectedRoute>
            } />
            <Route path="/agent-types/:id" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <AgentTypeDetailsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/agent-types/:id/edit" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.Manage}
                redirectPath="/agent-types"
              >
                <AgentTypeUpdatePage />
              </RoleProtectedRoute>
            } />

            {/* Agent Management Routes */}
            <Route path="/agents" element={
              <RoleProtectedRoute allowedRoles={['house_admin']} redirectPath="/agent-registry">
                <Agents />
              </RoleProtectedRoute>
            } />
            <Route path="/agent-registry" element={<AgentRegistry />} />
            <Route path="/my-activated-agents" element={<MyActivatedAgentsPage />} />
            <Route path="/my-clones" element={<MyClonesPage />} />
            <Route path="/clones" element={<MyClonesPage />} />
            <Route path="/agents/activate/:id" element={<AgentActivationPage />} />
            <Route path="/clones/create" element={<CloneCreatePage />} />
            <Route path="/clones/:id/edit" element={<CloneEditPage />} />
            <Route path="/clones/:id" element={<CloneDetailsPage />} />
            <Route path="/agent-activations/:id" element={<AgentActivationDetailsPage />} />
            
            {/* Super Agency Routes */}
            <Route path="/super-agency/agent-enablement" element={
              <RoleProtectedRoute allowedRoles={['super_agency_admin']} redirectPath="/dashboard">
                <AgentEnablementPage />
              </RoleProtectedRoute>
            } />
            
            {/* Chat Route */}
            <Route path="/chat" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <Chat />
              </RoleProtectedRoute>
            } />
            
            {/* Analytics Route */}
            <Route path="/analytics" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <Analytics />
              </RoleProtectedRoute>
            } />
            
            {/* Content Management Routes */}
            <Route path="/content" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ContentListPage />
              </RoleProtectedRoute>
            } />
            <Route path="/content/generate" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ContentGenerationPage />
              </RoleProtectedRoute>
            } />
            <Route path="/content/upload" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ContentUploadPage />
              </RoleProtectedRoute>
            } />
            <Route path="/content/review" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.Manage}
                redirectPath="/dashboard"
              >
                <ContentReviewPage />
              </RoleProtectedRoute>
            } />
            <Route path="/content/:id" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ProcessedContentDetailsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/content/:contentId/edit" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ContentEditPage />
              </RoleProtectedRoute>
            } />
            
            {/* API Keys Management Routes */}
            <Route path="/api-keys" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.Manage}
                redirectPath="/dashboard"
              >
                <ApiKeys />
              </RoleProtectedRoute>
            } />

            {/* Phase 6 Management Pages */}
            <Route path="/projects" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ProjectsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/project-management" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ProjectManagementPage />
              </RoleProtectedRoute>
            } />
            <Route path="/assets-management" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <AssetsManagementPage />
              </RoleProtectedRoute>
            } />
            <Route path="/task-management" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <TaskManagementPage />
              </RoleProtectedRoute>
            } />
            <Route path="/agent-dashboard" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <AgentDashboardPage />
              </RoleProtectedRoute>
            } />
            <Route path="/project-analytics" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <ProjectAnalyticsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/organization-management" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Organizations.Update}
                redirectPath="/dashboard"
              >
                <OrganizationManagementPage />
              </RoleProtectedRoute>
            } />
            <Route path="/monitoring-dashboard" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.View}
                redirectPath="/dashboard"
              >
                <MonitoringDashboardPage />
              </RoleProtectedRoute>
            } />
            <Route path="/integration-management" element={
              <RoleProtectedRoute
                permissionCheck={Permissions.Resources.Manage}
                redirectPath="/dashboard"
              >
                <IntegrationManagementPage />
              </RoleProtectedRoute>
            } />

            {/* Tenant-specific Routes */}
            <Route path="/agentic-projects" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <AgenticProjectsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/tenant/agentic-projects" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <AgenticProjectsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/my-tasks" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <MyTasksPage />
              </RoleProtectedRoute>
            } />
            <Route path="/tenant/my-tasks" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <MyTasksPage />
              </RoleProtectedRoute>
            } />
            <Route path="/memories" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <MemoriesPage />
              </RoleProtectedRoute>
            } />
            <Route path="/tenant/memories" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <MemoriesPage />
              </RoleProtectedRoute>
            } />
            <Route path="/playground" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <UnifiedPlaygroundPage mode="hybrid" />
              </RoleProtectedRoute>
            } />
            <Route path="/playground-copilotkit" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <PlaygroundPage />
              </RoleProtectedRoute>
            } />
            <Route path="/playground-direct" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <StandaloneDirectCopilotKitChat />
              </RoleProtectedRoute>
            } />
            <Route path="/playground-alternative" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <PlaygroundPageAlternative />
              </RoleProtectedRoute>
            } />
            <Route path="/tenant/playground" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <UnifiedPlaygroundPage mode="hybrid" />
              </RoleProtectedRoute>
            } />
            <Route path="/activity-log" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <ActivityLogPage />
              </RoleProtectedRoute>
            } />
            <Route path="/rep-rooms" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <RepRoomsPage />
              </RoleProtectedRoute>
            } />
            <Route path="/voice-rep-room/:slug" element={
              <RoleProtectedRoute allowedRoles={['tenant_admin', 'tenant_user']} redirectPath="/dashboard">
                <VoiceRepRoomPage />
              </RoleProtectedRoute>
            } />
            <Route path="/webhooks" element={
              <RoleProtectedRoute allowedRoles={['house_admin', 'tenant_admin', 'agency_admin', 'super_agency_admin']} redirectPath="/dashboard">
                <WebhooksPage />
              </RoleProtectedRoute>
            } />
          </Route>
        </Route>

        {/* Public Rep Room Routes - No authentication required */}
        <Route path="/rroom/:slug/:sessionId?" element={<NewRepRoomPage />} />
        <Route path="/rroom-v2/:slug/:sessionId?" element={<RepRoomPageEnhanced />} />
        <Route path="/rroom-v3/:slug/:sessionId?" element={<RepRoomSessionPage />} />
        <Route path="/rep-room/:slug" element={<RepRoomPageUnifiedLiveKit />} />
        <Route path="/rep-room/t1" element={<RepRoomPageUnifiedLiveKit />} />
        <Route path="/rr/t1" element={<RepRoomPageUnifiedLiveKit />} />
        <Route path="/rrm/t1" element={<RepRoomMasterT1Page />} />
        
        {/* Enhanced Rep Room Routes - More specific routes first to avoid conflicts */}
        <Route path="/enhanced-rep-room/room/:roomId" element={<RepRoomPageUnifiedEnhanced />} />
        <Route path="/enhanced-rep-room/:slug/:roomId" element={<RepRoomPageUnifiedEnhanced />} />
        <Route path="/enhanced-rep-room/:slug" element={<RepRoomPageUnifiedEnhanced />} />
        
        <Route path="/simple-voice/:slug" element={<SimpleRepRoomVoice />} />
        <Route path="/simple-rep-room/:slug" element={<SimpleRepRoomPage />} />
        <Route path="/voice-test" element={<VoiceTestPage />} />
        <Route path="/public/voice-rep-room/:slug" element={<VoiceRepRoomPage />} />
      </Routes>
      <Toaster />
    </ThemeProvider>
  );
}

export default App;
